import React, { useState } from 'react';
import { IconSearch } from "@tabler/icons-react";

const CategoryList = ({
  categories,
  selectedCategory,
  onCategorySelect,
  searchQuery,
  onSearchChange,
  iconStroke = 1.5
}) => {
  // Aktif kategori yolunu tutan state
  // [level1Id, level2Id, level3Id] şeklinde olacak
  const [activePath, setActivePath] = useState([]);
  
  // Kategorileri seviyelere göre filtreleme
  const getCategories = (parentId = null, level = 0) => {
    return categories
      .filter(cat => cat.parent_id === parentId)
      .sort((a, b) => a.sort_order - b.sort_order);
  };
  
  // Alt kategorilere sahip olup olmadığını kontrol et
  const hasSubCategories = (categoryId) => {
    return categories.some(cat => cat.parent_id === categoryId);
  };
  
  // Kategori seçildiğinde çağrılacak fonksiyon
  const handleCategoryClick = (category, level) => {
    // Kategoriyi seç
    onCategorySelect(category.id);
    
    // Aktif yolu güncelle - bu seviyeden sonraki tüm seviyeleri temizle
    const newPath = [...activePath.slice(0, level)];
    
    // Bu kategori alt kategorilere sahipse yola ekle, değilse yolu temizle
    if (hasSubCategories(category.id)) {
      newPath[level] = category.id;
      setActivePath(newPath);
    } else {
      // Alt kategorisi yoksa bu düzeyi ekle, sonraki düzeyleri temizle
      newPath[level] = category.id;
      setActivePath(newPath);
    }
  };
  
  // Ana kategorileri (level 0) al
  const mainCategories = getCategories(null, 0);
  
  // İkinci seviye kategorileri al (eğer bir ana kategori seçilmişse)
  const level1Categories = activePath[0] ? getCategories(activePath[0], 1) : [];
  
  // Üçüncü seviye kategorileri al (eğer bir ikinci seviye kategori seçilmişse)
  const level2Categories = activePath[1] ? getCategories(activePath[1], 2) : [];

  return (
    <div className="flex flex-col gap-2 bg-white/40 backdrop-blur sticky top-0 w-full z-10 rounded-t-2xl">
      {/* Search */}
      <label className="w-full bg-gray-100 rounded-full px-3 py-2 text-gray-500 flex items-center gap-2">
        <IconSearch size={18} stroke={iconStroke} />
        <input
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
          type="search"
          placeholder="Ürün Ara"
          className="w-full bg-transparent outline-none"
        />
      </label>
      
      {/* Ana Kategoriler (Level 0) */}
      <div className="grid grid-flow-col grid-rows-1 gap-2 overflow-x-auto pt-2 pb-2 scrollbar-hide auto-cols-[minmax(120px,auto)]">
        <button
          onClick={() => {
            onCategorySelect('all');
            setActivePath([]);
          }}
          className={`px-4 pt-2 pb-2 rounded-md  transition active:scale-95 ${
            selectedCategory === 'all'
              ? 'bg-restro-green font-extrabold  text-white'
              : 'bg-gray-100 text-gray-500  font-extrabold  hover:bg-gray-200'
          }`}
        >
          Tümü
        </button>
        
        {mainCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => handleCategoryClick(category, 0)}
            className={`px-4 pt-2 pb-2 rounded-md stransition active:scale-95 ${
              activePath[0] === category.id || selectedCategory === category.id
                ? 'bg-restro-green font-extrabold  text-white'
              : 'bg-gray-100 text-gray-500  font-extrabold  hover:bg-gray-200'
            }`}
          >
            {category.title}
          </button>
        ))}
      </div>
      
      {/* İkinci Seviye Kategoriler (Level 1) */}
      {level1Categories.length > 0 && (
        <div className="grid grid-flow-col grid-rows-1 gap-2 overflow-x-auto pt-0 pb-2 scrollbar-hide auto-cols-[minmax(120px,auto)]">
          {level1Categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category, 1)}
              className={`px-4 pt-2 pb-2 rounded-md transition active:scale-95 border-l-4 border-restro-green ${
                activePath[1] === category.id || selectedCategory === category.id
                  ? 'bg-restro-green font-extrabold  text-white'
                  : 'bg-gray-100 text-gray-500  font-extrabold hover:bg-gray-200'
              }`}
            >
              {category.title}
            </button>
          ))}
        </div>
      )}
      
      {/* Üçüncü Seviye Kategoriler (Level 2) */}
      {level2Categories.length > 0 && (
        <div className="grid grid-flow-col grid-rows-1 gap-2 overflow-x-auto pt-0 pb-2 scrollbar-hide auto-cols-[minmax(120px,auto)]">
          {level2Categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category, 2)}
              className={`px-4 rounded-md transition active:scale-95 border-l-4 border-yellow-500 ${
                selectedCategory === category.id
                  ? 'bg-restro-green text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {category.title}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default CategoryList;