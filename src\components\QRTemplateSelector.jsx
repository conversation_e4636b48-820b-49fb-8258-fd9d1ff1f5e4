import React, { useState, useEffect } from 'react';
import { 
  getQRTemplates, 
  getTenantSelectedTemplate, 
  selectTenantTemplate 
} from '../controllers/qrmenu.controller';
import toast from 'react-hot-toast';
import { IconCheck, IconEye, IconPalette } from '@tabler/icons-react';
import { iconStroke } from '../config/config';

export default function QRTemplateSelector() {
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    fetchTemplates();
    fetchCurrentTemplate();
  }, []);

  const fetchTemplates = async () => {
    try {
      const data = await getQRTemplates();
      setTemplates(data.templates || []);
    } catch (error) {
      console.error('Şablonlar yüklenemedi:', error);
      toast.error('Şablonlar yüklenirken hata olu<PERSON>tu');
    }
  };

  const fetchCurrentTemplate = async () => {
    try {
      const data = await getTenantSelectedTemplate();
      setSelectedTemplate(data.template);
    } catch (error) {
      console.error('Mevcut şablon yüklenemedi:', error);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSelectTemplate = async (templateId) => {
    if (selectedTemplate?.id === templateId) return;
    
    setLoading(true);
    try {
      const response = await selectTenantTemplate(templateId);
      
      if (response.success) {
        await fetchCurrentTemplate();
        toast.success('Şablon başarıyla seçildi!');
      }
    } catch (error) {
      console.error('Şablon seçilemedi:', error);
      toast.error('Şablon seçilirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const getTemplatePreview = (templateConfig) => {
    if (!templateConfig) return {};
    
    return {
      borderRadius: templateConfig.button_style === 'rounded' ? '8px' : 
                   templateConfig.button_style === 'pill' ? '50px' : '0px',
      backgroundColor: templateConfig.layout_type === 'luxury' ? '#8b5a2b' :
                      templateConfig.layout_type === 'modern' ? '#2563eb' :
                      templateConfig.layout_type === 'minimal' ? '#000' : '#333',
      color: '#fff'
    };
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <IconPalette stroke={iconStroke} size={24} />
        <h3 className="text-xl font-semibold">QR Menü Tasarımı</h3>
      </div>

      {selectedTemplate && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-1">
            Mevcut Tasarım: {selectedTemplate.name}
          </h4>
          <p className="text-blue-700 text-sm">{selectedTemplate.description}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map(template => {
          const isSelected = selectedTemplate?.id === template.id;
          const previewStyle = getTemplatePreview(template.template_config);
          
          return (
            <div 
              key={template.id} 
              className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                isSelected 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300'
              } ${loading ? 'opacity-50 pointer-events-none' : ''}`}
              onClick={() => handleSelectTemplate(template.id)}
            >
              {isSelected && (
                <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                  <IconCheck size={16} stroke={2} />
                </div>
              )}
              
              {template.is_default === 1 && (
                <div className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                  Varsayılan
                </div>
              )}

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">{template.name}</h4>
                <p className="text-sm text-gray-600">{template.description}</p>
                
                {/* Template Preview */}
                <div className="bg-gray-100 rounded p-3 space-y-2">
                  <div className="text-xs text-gray-500 mb-2">Önizleme:</div>
                  
                  {/* Header Preview */}
                  <div 
                    className="h-8 rounded flex items-center justify-center text-xs text-white"
                    style={{ backgroundColor: previewStyle.backgroundColor }}
                  >
                    Başlık
                  </div>
                  
                  {/* Card Preview */}
                  <div className="space-y-1">
                    <div 
                      className="h-6 bg-white border flex items-center px-2 text-xs"
                      style={{ borderRadius: previewStyle.borderRadius }}
                    >
                      Ürün Kartı
                    </div>
                    <div 
                      className="h-4 text-xs flex items-center justify-center text-white"
                      style={{ 
                        backgroundColor: previewStyle.backgroundColor,
                        borderRadius: previewStyle.borderRadius 
                      }}
                    >
                      Buton
                    </div>
                  </div>
                </div>

                {template.preview_image && (
                  <div className="mt-2">
                    <img 
                      src={template.preview_image} 
                      alt={template.name}
                      className="w-full h-32 object-cover rounded border"
                    />
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {templates.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <IconPalette size={48} stroke={1} className="mx-auto mb-2 text-gray-300" />
          <p>Henüz şablon bulunamadı</p>
        </div>
      )}
    </div>
  );
}
