import React, { useState, useEffect } from 'react';
import {
  getTenantSelectedTemplate,
  selectTenantTemplate
} from '../controllers/qrmenu.controller';
import toast from 'react-hot-toast';
import { IconCheck, IconPalette } from '@tabler/icons-react';
import { iconStroke } from '../config/config';

export default function QRTemplateSelector() {
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Sabit tema listesi (backend ile senkronize)
  const availableTemplates = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      description: 'Geleneksel ve sade tasarım',
      is_default: 1
    },
    {
      id: 2,
      name: 'Modern',
      description: 'Modern ve dinamik tasarım'
    },
    {
      id: 3,
      name: 'Minimal',
      description: 'Sade ve temiz tasarım'
    },
    {
      id: 4,
      name: '<PERSON><PERSON><PERSON>',
      description: '<PERSON>ık ve premium tasarım'
    }
  ];

  useEffect(() => {
    fetchCurrentTemplate();
  }, []);

  const fetchCurrentTemplate = async () => {
    try {
      const data = await getTenantSelectedTemplate();
      setSelectedTemplate(data.template);
    } catch (error) {
      console.error('Mevcut şablon yüklenemedi:', error);
      // Hata durumunda varsayılan template'i seç
      setSelectedTemplate(availableTemplates[0]);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSelectTemplate = async (templateId) => {
    if (selectedTemplate?.id === templateId) return;

    setLoading(true);
    try {
      const response = await selectTenantTemplate(templateId);

      if (response.success) {
        await fetchCurrentTemplate();
        toast.success('Şablon başarıyla seçildi!');
      }
    } catch (error) {
      console.error('Şablon seçilemedi:', error);
      toast.error('Şablon seçilirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };



  if (initialLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <IconPalette stroke={iconStroke} size={24} />
        <h3 className="text-xl font-semibold">QR Menü Tasarımı</h3>
      </div>

      {selectedTemplate && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-1">
            Mevcut Tasarım: {selectedTemplate.name}
          </h4>
          <p className="text-blue-700 text-sm">{selectedTemplate.description}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {availableTemplates.map(template => {
          const isSelected = selectedTemplate?.id === template.id;

          return (
            <div
              key={template.id}
              className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md ${
                isSelected
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${loading ? 'opacity-50 pointer-events-none' : ''}`}
              onClick={() => handleSelectTemplate(template.id)}
            >
              {isSelected && (
                <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                  <IconCheck size={16} stroke={2} />
                </div>
              )}

              {template.is_default === 1 && (
                <div className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                  Varsayılan
                </div>
              )}

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">{template.name}</h4>
                <p className="text-sm text-gray-600">{template.description}</p>

                {/* Template Preview */}
                <div className="bg-gray-100 rounded p-3 space-y-2">
                  <div className="text-xs text-gray-500 mb-2">Önizleme:</div>

                  {/* Tema tipine göre önizleme */}
                  {template.id === 1 && (
                    <div className="space-y-1">
                      <div className="h-6 bg-green-600 rounded flex items-center justify-center text-xs text-white">Klasik Header</div>
                      <div className="h-4 bg-white border rounded flex items-center px-2 text-xs">Ürün Kartı</div>
                      <div className="h-3 bg-green-600 rounded text-xs flex items-center justify-center text-white">Buton</div>
                    </div>
                  )}

                  {template.id === 2 && (
                    <div className="space-y-1">
                      <div className="h-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-xs text-white">Modern Header</div>
                      <div className="h-4 bg-white/90 backdrop-blur border border-white/20 rounded-xl flex items-center px-2 text-xs shadow-lg">Modern Kart</div>
                      <div className="h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full text-xs flex items-center justify-center text-white">Buton</div>
                    </div>
                  )}

                  {template.id === 3 && (
                    <div className="space-y-1">
                      <div className="h-6 bg-black flex items-center justify-center text-xs text-white">Minimal Header</div>
                      <div className="h-4 bg-white border border-gray-300 flex items-center px-2 text-xs">Minimal Kart</div>
                      <div className="h-3 bg-black text-xs flex items-center justify-center text-white">Buton</div>
                    </div>
                  )}

                  {template.id === 4 && (
                    <div className="space-y-1">
                      <div className="h-6 bg-gradient-to-r from-yellow-600 to-yellow-700 rounded flex items-center justify-center text-xs text-white">Lüks Header</div>
                      <div className="h-4 bg-gradient-to-r from-yellow-50 to-yellow-100 border-2 border-yellow-400 rounded flex items-center px-2 text-xs">Lüks Kart</div>
                      <div className="h-3 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded text-xs flex items-center justify-center text-black font-bold">Buton</div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
