import React, { useContext, useState, useEffect   } from 'react';
import {   cancelKitchenOrder,
    completeKitchenOrder,
    getCompleteOrderPaymentSummary,
    payAndCompleteKitchenOrder,
    updateKitchenOrderItemStatus, } from "../../../controllers/orders.controller";
import { toast } from "react-hot-toast";
import { IconArmchair, IconCash, IconClock, IconCheck, IconChecks, IconX, IconDotsVertical } from "@tabler/icons-react";
import { iconStroke } from "../../../config/config";
import { SocketContext } from "../../../contexts/SocketContext";
import { getUserDetailsInLocalStorage } from "../../../helpers/UserDetails";
import { PAYMENT_ICONS } from "../../../config/payment_icons";


const CustomOrdersDetailModal = ({ table, kitchenOrders, refreshOrders, paymentTypes, orderSummary, onGetOrderSummary, currency }) => {
 const { socket, isSocketConnected } = useContext(SocketContext);
 const user = getUserDetailsInLocalStorage();
 const tableOrders = kitchenOrders.find(order => String(order.table_id) === String(table.id));
 const [state, setState] = useState({
    selectedPaymentType: null,
  });


  useEffect(() => {
    if(tableOrders?.order_ids) {
      onGetOrderSummary(tableOrders.order_ids);
    }
  }, [tableOrders]);

  const handlePayment = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      
      const res = await getCompleteOrderPaymentSummary(tableOrders.order_ids);
      
      if (res.status === 200) {
        const { subtotal, taxTotal, total, orders } = res.data;
        const tokens = orders.map(o => o.token_no).join(",");
   
        setState({
            ...state,
            summaryNetTotal: subtotal,
            summaryTaxTotal: taxTotal,
            summaryTotal: total,
            summaryOrders: orders,
            completeOrderIds: tableOrders.order_ids,
            completeTokenIds: tokens,
            order: tableOrders,
          });
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Ödeme bilgileri alınamadı!");
    } finally {
      toast.dismiss();
    }
   };

   const sendOrderUpdateEvent = () => {
    const user = getUserDetailsInLocalStorage();

    if (isSocketConnected) {
      socket.emit("order_update_backend", {}, user.tenant_id);
    } else {
      // Handle disconnected state (optional)
      initSocket();
      socket.emit("order_update_backend", {}, user.tenant_id);
    }
  };

  const handlePayAndComplete = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await payAndCompleteKitchenOrder(
        orderSummary.completeOrderIds, // state yerine orderSummary
        orderSummary.summaryNetTotal, // state yerine orderSummary  
        orderSummary.summaryTaxTotal, // state yerine orderSummary
        orderSummary.summaryTotal, // state yerine orderSummary
        state.selectedPaymentType,
      );

      if (res.status === 200) {
        await sendOrderUpdateEvent
        toast.success("Ödeme başarıyla alındı!");
        document.getElementById(`custom-orders-detail-modal-${table.id}`).close();
        setState(prev => ({...prev, selectedPaymentType: null}));
        await refreshOrders();
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Ödeme işlemi başarısız!");
    } finally {
      toast.dismiss();
    }
  };


 

  const btnChangeOrderItemStatus = async (orderItemId, status) => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateKitchenOrderItemStatus(orderItemId, status);
      
      if (res.status === 200) {
        if (isSocketConnected) {
          socket.emit("order_update_backend", {}, user.tenant_id);
          socket.emit("order_update", {}, user.tenant_id);
        }
        await refreshOrders();
        if(tableOrders?.order_ids) {
          onGetOrderSummary(tableOrders.order_ids); // handlePayment yerine onGetOrderSummary
        }
        toast.success(res.data.message);
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Error processing request!");
    } finally {
      toast.dismiss(); 
    }
  };
 return (
   <dialog id={`custom-orders-detail-modal-${table.id}`} className="modal">
     <div className="modal-box w-11/12 max-w-5xl">
       <div className="flex items-center justify-between mb-4">
         <div className="flex items-center gap-2">
           <div className="flex w-12 h-12 rounded-full items-center justify-center bg-gray-100 text-gray-400">
             <IconArmchair size={24} stroke={iconStroke} />
           </div>
           <div>
             <p className="font-bold">{table.table_title}</p>
             {table.floor && <p className="text-sm">{table.floor}</p>}
           </div>
         </div>
         <form method="dialog">
           <button className="btn btn-sm btn-circle">✕</button>
         </form>
       </div>

       <div className="my-6">
          <div className="flex w-full items-center divide-x gap-x-4">
            <div className="flex-1 text-center">
              <p>Ara Toplam</p>
              <p className="text-2xl">
                {Number(orderSummary.summaryNetTotal).toFixed(2)}
                {currency}
              </p>
            </div>

            <div className="flex-1 text-center">
              <p>KDV</p>
              <p className="text-2xl">
                {Number(orderSummary.summaryTaxTotal).toFixed(2)}
                {currency}
              </p>
            </div>

            <div className="flex-1 text-center">
              <p>Toplam</p>
              <p className="text-2xl text-restro-green font-bold">
                {Number(orderSummary.summaryTotal).toFixed(2)}
                {currency}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2 mt-4">
             {paymentTypes.map((paymentType) => (
               <label key={paymentType.id}>
                 <input
                   type="radio"
                   name="payment_type"
                   value={paymentType.id}
                   checked={state.selectedPaymentType === paymentType.id}
                   onChange={(e) => setState({...state, selectedPaymentType: e.target.value})}
                   className="peer hidden"
                 />
                 <div className="border rounded-2xl flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-restro-green peer-checked:text-restro-green peer-checked:font-bold cursor-pointer transition">
                   {paymentType.icon && PAYMENT_ICONS[paymentType.icon]}
                   <p className="text-xs">{paymentType.title}</p>
                 </div>
               </label>
             ))}
           </div>

          <button
            onClick={() => handlePayAndComplete(table.id)}
            className="w-full btn hover:bg-restro-green-dark bg-restro-green text-white mt-4"
          >
            Ödemeyi Al ve Siparişi Kapat
          </button>
        </div>

        <div className="flex flex-col">
          {tableOrders && tableOrders.orders.map((o, i) => (
            <div key={i} className="bg-gray-50  rounded-xl">
            
              {/* <div className="flex justify-between items-center mb-2">
        <div>
          <p className="font-medium">Token: {o.token_no}</p>
        </div>
        <p className="flex items-center gap-1">
          <IconCash stroke={iconStroke} size={16} /> {o.payment_status}
          <p>{new Intl.DateTimeFormat("tr-TR", {timeStyle: "short"}).format(new Date(o.date))}</p>
        </p>
      </div> */}
              {o.items.map((item, itemIndex) => (
                <div key={itemIndex} className="flex items-center py-1 gap-2">
                  {item.status === "preparing" && <IconClock stroke={iconStroke} className="text-amber-500" />}
                  {item.status === "completed" && <IconCheck stroke={iconStroke} className="text-green-500" />}
                  {item.status === "cancelled" && <IconX stroke={iconStroke} className="text-red-500" />}
                  {item.status === "delivered" && <IconChecks stroke={iconStroke} className="text-green-500" />}
                  <div className="flex-1 text-lg font-medium  ">
                    <p>
                      {item.item_title} {item.variant_title} x {item.quantity}
                      <span className="ml-2  text-lg font-medium text-gray-700">
                        {Number(item.price * item.quantity).toFixed(2)} {currency}
                      </span>
                    </p>
                    {item.addons?.length > 0 && (
                      <p className="text-sm text-gray-500">
                        Ekstralar: {item.addons.map(a => `${a.title}`).join(", ")}
                      </p>
                    )}
                    {item.notes && <p className="text-sm text-gray-500">Not: {item.notes}</p>}
                  </div>
                  <div className="dropdown dropdown-left">
                    <div tabIndex={0} role="button" className="btn btn-sm btn-circle bg-transparent border-none shadow-none">
                      <IconDotsVertical size={16} stroke={iconStroke} />
                    </div>
                    <ul className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-40">
                      <li>
                        <button className="flex items-center gap-2 text-amber-500" onClick={() => btnChangeOrderItemStatus(item.id, "preparing")}>
                          <IconClock size={16} stroke={iconStroke} /> Hazırlanıyor
                        </button>
                      </li>
                      <li>
                        <button className="flex items-center gap-2 text-green-500" onClick={() => btnChangeOrderItemStatus(item.id, "completed")}>
                          <IconCheck size={16} stroke={iconStroke} /> Hazır
                        </button>
                      </li>
                      <li>
                        <button className="flex items-center gap-2" onClick={() => btnChangeOrderItemStatus(item.id, "delivered")}>
                          <IconChecks size={16} stroke={iconStroke} /> Teslim Edildi  
                        </button>
                      </li>
                      <li>
                        <button className="flex items-center gap-2 text-red-500" onClick={() => btnChangeOrderItemStatus(item.id, "cancelled")}>
                          <IconX size={16} stroke={iconStroke} /> İptal
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
     </div>
     <form method="dialog" className="modal-backdrop">
       <button>Kapat</button>
     </form>
   </dialog>
 );
};

export default CustomOrdersDetailModal;