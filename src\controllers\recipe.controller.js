import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = url => ApiClient.get(url).then(res => res.data);

export function useRecipes() {
  const APIURL = `/recipes`;
  const { data: response, error, isLoading } = useSWR(APIURL, fetcher);
  const recipes = response || [];
  return { data: recipes, error, isLoading, APIURL };
}

export function useRecipeDetails(id) {
  const APIURL = `/recipes/${id}`;
  const { data, error, isLoading } = useSWR(id ? APIURL : null, fetcher);
  return { data, error, isLoading };
}

export async function addRecipe(data) {
  try {
    const res = await ApiClient.post("/recipes/add", data);
    return res;
  } catch (error) {
    throw error;
  }
}

export async function updateRecipe(id, data) {
  try {
    const res = await ApiClient.put(`/recipes/${id}`, data);
    return res;
  } catch (error) {
    throw error;
  }
}