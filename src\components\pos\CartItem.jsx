import React, { useState } from 'react';
import { IconMinus, IconPlus, IconNote, IconTrash, IconEdit, IconCheck } from "@tabler/icons-react";
import { getUserDetailsInLocalStorage } from '../../helpers/UserDetails';
import RequireScopes from '../RequireScopes'; 
import { SCOPES } from "../../config/scopes";


const CartItem = ({
  item,
  index,
  currency,
  onQuantityDecrease,
  onQuantityIncrease,
  onAddNote,
  onRemove,
  onPriceChange // Yeni prop ekledik
}) => {
  const { quantity, notes, title, price, variant, addons } = item;
  const itemTotal = price * quantity;

  const user = getUserDetailsInLocalStorage();
  
  const { role: userRole, scope, } = user;
    
  const userScopes = scope?.split(",");

  // Fiyat düzenleme state'leri
  const [isEditing, setIsEditing] = useState(false);
  const [editedPrice, setEditedPrice] = useState(price);

  // Fiyat düzenleme fonksiyonları
  const handlePriceEdit = () => {
    setIsEditing(true);
  };

  const handlePriceChange = (e) => {
    const newPrice = parseFloat(e.target.value);
    if (!isNaN(newPrice)) {
      setEditedPrice(newPrice);
    }
  };

  const handlePriceSubmit = () => {
    onPriceChange(index, editedPrice);
    setIsEditing(false);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handlePriceSubmit();
    }
  };

  return (
    <div className='text-sm border border-restro-border-green-light rounded-lg p-2 relative'>
      <p>#{index + 1} {title} x {quantity}</p>
      <div className='mt-1 flex items-center gap-2'>
        {isEditing ? (
         <RequireScopes requiredScopes={[SCOPES.KASIYER]} userScopes={userScopes} userRole={userRole}    >
          <div className="flex items-center gap-2">
            
            <input
              type="number"
              value={editedPrice}
              onChange={handlePriceChange}
              onKeyPress={handleKeyPress}
              className="w-20 px-2 py-1 border rounded focus:outline-none focus:border-restro-green"
              autoFocus
              step="0.01"
            />
            <button
              onClick={handlePriceSubmit}
              className="text-green-500 hover:text-green-600"
            >
              <IconCheck size={18} stroke={1.8} />
            </button>

          </div>
          </RequireScopes>

        ) : (
          <div 
            className="flex items-center gap-2 cursor-pointer hover:text-restro-green"
            onClick={handlePriceEdit}
          >
            <span>{Number(price).toFixed(2)} {currency}</span>
            <IconEdit size={16} stroke={1.8} className="text-gray-500" />
          </div>
        )}
        <span className='text-xs text-gray-500'>x {quantity}</span>
        <span className='font-bold'>= {itemTotal.toFixed(2)} {currency}</span>
      </div>
      
      {notes && (
        <p className="mt-1 text-xs text-gray-400">
          Not: {notes}
        </p>
      )}

      {variant && <p className="mt-1 text-xs text-gray-400">{variant.title}</p>}

      {(addons && addons?.length > 0) && (
        <p className="mt-1 text-xs text-gray-400">
          Ekstralar: {addons?.map(addon => addon.title)?.join(", ")}
        </p>
      )}

      <div className="flex items-center justify-between gap-2 w-full mt-4">
        <div className='flex items-center gap-2'>
          <button 
            onClick={() => onQuantityDecrease(index, quantity)}
            className='btn btn-circle btn-sm'
          >
            <IconMinus stroke={1.8} size={18} />
          </button>
          <div className='w-8 h-8 flex items-center justify-center bg-gray-100 rounded-full'>
            {quantity}
          </div>
          <button 
            onClick={() => onQuantityIncrease(index, quantity)}
            className='btn btn-circle btn-sm'
          >
            <IconPlus stroke={1.8} size={18} />
          </button>
        </div>
        <div>
          <button 
            onClick={() => onAddNote(index, notes)}
            className="text-sm rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
          >
            <div><IconNote size={18} stroke={1.8} /></div>
            <p>Not Ekle</p>
          </button>
        </div>
      </div>

      <button 
        onClick={() => onRemove(index)}
        className='absolute right-2 top-2 text-red-500 flex items-center justify-center rounded-full w-6 h-6 hover:bg-red-100 transition'
      >
        <IconTrash stroke={1.8} size={14} />
      </button>
    </div>
  );
};

export default CartItem;