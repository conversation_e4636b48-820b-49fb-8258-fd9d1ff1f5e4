import React, { useRef, useState } from "react";
import Page from "../../components/Page";
import { CURRENCIES } from "../../config/currencies.config";
import { saveStoreSettings, useStoreSettings, uploadSlidesPhoto, clearTenantData, removeSlidesPhoto, uploadStoreImage , deleteStoreImage  } from "../../controllers/settings.controller";
import {toast} from "react-hot-toast"
import { mutate } from "swr";
import Popover from "../../components/Popover";
import { IconExternalLink, IconQrcode, IconUpload, IconTrash } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import QRCode from "qrcode";
import { getQRMenuLink } from "../../helpers/QRMenuHelper";
import imageCompression from "browser-image-compression";
import { getImageURL } from "../../helpers/ImageHelper";


export default function SettingDetailsPage() {
  const storeNameRef = useRef();
  const addressRef = useRef();
  const emailRef = useRef();
  const phoneRef = useRef();
  const currencyRef = useRef();
  const isQRMenuEnabledRef = useRef();
  const isQROrderEnabledRef = useRef();
  const defaultOrderTypeRef = useRef();
  const dineInEnabledRef = useRef();
  const deliveryEnabledRef = useRef();
  const takeawayEnabledRef = useRef();
  const facebookRef = useRef();
  const instagramRef = useRef();
  const twitterRef = useRef();
  const whatsappRef = useRef();


  const { APIURL, data, error, isLoading } = useStoreSettings();

  const itemId = data?.id; // veya başka bir unique identifier
  const image = data?.image; // API'den gelen resim yolu
  const imageURL = image ? getImageURL(image) : null;

  const TABLE_OPTIONS = [
    "order_items",
    "order_discounts",
    "partial_payments",
    "invoice_sequences",
    "invoices",
    "orders",
  ];

  const [selectedTables, setSelectedTables] = useState([]);
  
  const toggleTable = (table) => {
    setSelectedTables(prev =>
      prev.includes(table) ? prev.filter(t => t !== table) : [...prev, table]
    );
  };

  if(isLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>
  }

  if(error) {
    console.error(error);
    return <Page className="px-8 py-6">Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!</Page>;
  }

  const { storeImage, storeName, email, address, phone, currency, isQRMenuEnabled, uniqueQRCode , isQROrderEnabled, uniqueId, defaultOrderType = 'dinein', dineInEnabled = true, deliveryEnabled = true, takeawayEnabled = true, facebook, instagram, twitter, whatsapp } = data;
  const QR_MENU_LINK = getQRMenuLink(uniqueQRCode);

  const btnSave = async () => {
    const storeName = storeNameRef.current.value;
    const address = addressRef.current.value;
    const email = emailRef.current.value;
    const phone = phoneRef.current.value;
    const currency = currencyRef.current.value;
    const isQRMenuEnabled = isQRMenuEnabledRef.current.checked;
    const isQROrderEnabled = isQROrderEnabledRef.current.checked;
    const defaultOrderType = defaultOrderTypeRef.current.value;
    const dineInEnabled = dineInEnabledRef.current.checked;
    const deliveryEnabled = deliveryEnabledRef.current.checked;
    const takeawayEnabled = takeawayEnabledRef.current.checked;
    const facebook = facebookRef.current.value;
    const instagram = instagramRef.current.value;
    const twitter = twitterRef.current.value;
    const whatsapp = whatsappRef.current.value;

    try {

      toast.loading("Lütfen bekleyin...");
      const res = await saveStoreSettings(storeName, address, phone, email, currency, isQRMenuEnabled , isQROrderEnabled, defaultOrderType, dineInEnabled, deliveryEnabled, takeawayEnabled, facebook, instagram, twitter, whatsapp);

      if(res.status == 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }

    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };


  const handleDeleteData = async () => {
    try {
      toast.loading("Veriler siliniyor...");
      const res = await clearTenantData(selectedTables);
      toast.dismiss();
      toast.success(res.data.message || "Seçili veriler silindi!");
      await mutate(APIURL);
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || "Silme işlemi başarısız!");
    }
  };

  const handleFileChangeTwo = async (e) => {

    const file = e.target.files[0];

    if(!file) {
      return;
    }

    // compress image
    try {
      toast.loading("Please wait...");
      const compressedImage = await imageCompression(file, {
        maxSizeMB: 0.5,
        maxWidthOrHeight: 512,
        useWebWorker: true,
      })

      const formData = new FormData();
      formData.append("store_image", compressedImage);

      const res = await uploadStoreImage(formData);
      if(res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);

        // update the image state
        const imagePath = res.data.imageURL;
        await mutate(APIURL);
        location.reload();
      }

    } catch (error) {
      console.error(error);
      toast.dismiss();
      const message = error?.response?.data?.message || "We're getting issue while processing your request, Please try later!";
      toast.error(message)
    }
  }

  const handleFileDelete = async () => {
    try {
      toast.loading("Please wait...");


      const res = await deleteStoreImage(uniqueId);
      if(res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);
        await mutate(APIURL);
        location.reload();
      }

    } catch (error) {
      console.error(error);
      toast.dismiss();
      const message = error?.response?.data?.message || "We're getting issue while processing your request, Please try later!";
      toast.error(message)
    }
  }

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
  
    if (!file) {
      return;
    }
  
    try {
      toast.loading("Lütfen bekleyin...");
      const compressedImage = await imageCompression(file, {
        maxSizeMB: 0.5,
        maxWidthOrHeight: 512,
        useWebWorker: true
      });
  
      const formData = new FormData();
      formData.append("image", compressedImage);
  
      const res = await uploadSlidesPhoto(formData);
      if (res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);
        await mutate(APIURL);
      }
  
    } catch (error) {
      console.error(error);
      toast.dismiss();
      const message = error?.response?.data?.message || "İşlem sırasında bir hata oluştu!";
      toast.error(message);
    }
  };
  
  const btnRemoveMenuItemImage = async (imageUrl) => {
    const isConfirm = window.confirm("Bu resmi silmek istediğinizden emin misiniz?");
  
    if (!isConfirm) {
      return;
    }
  
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await removeSlidesPhoto(imageUrl);
      
      if (res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);
        await mutate(APIURL);
      }
  
    } catch (error) {
      console.error(error);
      toast.dismiss();
      const message = error?.response?.data?.message || "İşlem sırasında bir hata oluştu!";
      toast.error(message);
    }
  };

  const btnDownloadMenuQR = async () => {
    try {
      const qrDataURL = await QRCode.toDataURL(QR_MENU_LINK, {width: 1080});
      const link = document.createElement("a");
      link.download="qr.png";
      link.href=qrDataURL;
      link.click();
      link.remove();
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <Page className="px-8 py-6">
      <h3 className="text-3xl font-light">İşletme Detayları</h3>

      {/* Store Image Upload Section */}
      <div className="mb-6">
          <label htmlFor="storeImage" className="block mb-1 font-medium">
            İşeltme Logosu
          </label>
          <div className="flex flex-col items-start gap-2">
            <input
              type="file"
              name="storeImage"
              id="storeImage"
              accept="image/*"
              className="hidden"
              onChange={handleFileChangeTwo}
            />
            <div className="w-24 h-24 border border-restro-border-green-light rounded-xl bg-gray-50 flex items-center justify-center relative">
              {storeImage ? (
                <img
                  src={getImageURL(storeImage)}
                  alt="Store"
                  className="object-cover w-24 h-24 rounded-xl bg-gray-50"
                />
              ) : (
                <span className="text-gray-400 text-sm">Logo Yok</span>
              )}

              {!storeImage ? (
                <label
                  htmlFor="storeImage"
                  className="absolute top-0 right-0 translate-x-1/3 -translate-y-1/3 bg-white p-1 rounded-full shadow cursor-pointer hover:bg-gray-100 z-10"
                >
                  <IconUpload size={14} stroke={iconStroke} />
                </label>
              ) : (
                <button
                  className="absolute top-0 right-0 translate-x-1/3 -translate-y-1/3 bg-red-50 p-1 rounded-full shadow cursor-pointer hover:bg-red-100 z-10 text-red-500"
                  onClick={handleFileDelete}
                >
                  <IconTrash size={14} stroke={iconStroke}/>
                </button>
              )}

            </div>
            {/* <p className="text-xs text-gray-400">
              Supported formats: JPG, PNG. Max size: 5MB.
            </p> */}
          </div>
        </div>

            {/* Resim yükleme alanını email input'undan önce ekleyelim */}
            <div className="mt-4">
        <label className="block mb-1">Slider Resimleri</label>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-4">
          {/* Mevcut resimleri göster */}
          {data?.slides?.split(',').filter(Boolean).map((imageUrl, index) => (
        <div key={index} className="relative group">
          <img 
            src={getImageURL(imageUrl)}
            alt={`Slide ${index + 1}`} 
            className="w-full h-48 object-cover rounded-lg border"
          />
          <button
            onClick={() => btnRemoveMenuItemImage(imageUrl)}
            className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      ))}
      
          {/* Yükleme alanı */}
          <label className="border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center cursor-pointer hover:border-gray-400 transition">
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
            />
            <div className="text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <div className="mt-2">
                <span className="block text-sm font-medium text-gray-900">
                  Yeni Slider Ekle
                </span>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                PNG, JPG, JPEG (max. 500KB)
              </p>
            </div>
          </label>
        </div>
      </div>

      <div className="mt-8 text-sm text-gray-500">
        <div>
          <label htmlFor="name" className="block mb-1">
            İşyeri Adı
          </label>
          <input
            ref={storeNameRef}
            type="text"
            name="name"
            id="name"
            defaultValue={storeName}
            placeholder="İşyeri adınızı giriniz."
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="address" className="block mb-1">
            Adres
          </label>
          <textarea
            ref={addressRef}
            type="text"
            name="address"
            id="address"
            defaultValue={address}
            placeholder="İşyeri adresinizi giriniz"
            className="block w-full h-20 lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="email" className="block mb-1">
            E-posta
          </label>
          <input
            ref={emailRef}
            type="email"
            name="email"
            id="email"
            defaultValue={email}
            placeholder="E posta adresinizi giriniz"
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="phone" className="block mb-1">
            Telefon
          </label>
          <input
            ref={phoneRef}
            type="tel"
            name="phone"
            id="phone"
            defaultValue={phone}
            placeholder="İşletme Telefonnuu giriniz"
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="facebook" className="block mb-1">
            Faceook Adresiniz
          </label>
          <input
            ref={facebookRef}
            type="text"
            name="facebook"
            id="facebook"
            defaultValue={facebook}
            placeholder="Facebook Porfilinizin Linikini giriniz"
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="facebook" className="block mb-1">
            İnstagram Adresiniz
          </label>
          <input
            ref={instagramRef}
            type="text"
            name="instagram"
            id="instagram"
            defaultValue={instagram}
            placeholder="İnstagram Porfilinizin Linikini giriniz"
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="facebook" className="block mb-1">
            Twitter Adresiniz
          </label>
          <input
            ref={twitterRef}
            type="text"
            name="twitter"
            id="twitter"
            defaultValue={twitter}
            placeholder="Twitter Porfilinizin Linikini giriniz"
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="facebook" className="block mb-1">
            Whatsapp Linki
          </label>
          <input
            ref={whatsappRef}
            type="text"
            name="whatsapp"
            id="whatsapp"
            defaultValue={whatsapp}
            placeholder="Whatsapp Linikini giriniz"
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          />
        </div>
        <div className="mt-4">
          <label htmlFor="currency" className="block mb-1">
            Para Birimi
          </label>
          <select
            ref={currencyRef}
            name="currency"
            id="currency"
            defaultValue={currency}
            placeholder="Para Birimini Seçiniz..."
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          >
            <option value="" hidden>
              Para Birimi Seçiniz
            </option>
            {CURRENCIES.map((item, index) => {
              return (
                <option value={item.cc} key={index}>
                  {item.name} - ({item.symbol})
                </option>
              );
            })}
          </select>
        </div>

        <div className="mt-4">
          <label htmlFor="defaultOrderType" className="block mb-1">
            Varsayılan Sipariş Türü
          </label>
          <select
            ref={defaultOrderTypeRef}
            name="defaultOrderType"
            id="defaultOrderType"
            defaultValue={defaultOrderType}
            className="block w-full lg:min-w-96 border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
          >
            <option value="dinein">Masa</option>
            <option value="delivery">Paket</option>
            <option value="takeaway">Hızlı Satış</option>
          </select>
        </div>

        <div className="mt-4">
          <label className="flex items-center justify-between w-full">
            <span className="flex items-center gap-2">
              Masa Siparişi Aktif
              <Popover text="Kasiyerin masadan sipariş vermesine olanak sağlar." />
            </span>
            {/* Switch */}
            <label className="relative inline-flex items-center cursor-pointer no-drag">
              <input
                ref={dineInEnabledRef}
                type="checkbox"
                defaultChecked={dineInEnabled}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-restro-green peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600"></div>
            </label>
          </label>
        </div>
        
        <div className="mt-2">
          <label className="flex items-center justify-between w-full">
            <span className="flex items-center gap-2">
              Paket Siparişi Aktif
              <Popover text="Kasiyerin paket sipariş oluşturmasına olanak sağlar." />
            </span>
            {/* Switch */}
            <label className="relative inline-flex items-center cursor-pointer no-drag">
              <input
                ref={deliveryEnabledRef}
                type="checkbox"
                defaultChecked={deliveryEnabled}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-restro-green peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600"></div>
            </label>
          </label>
        </div>
        
        <div className="mt-2">
          <label className="flex items-center justify-between w-full">
            <span className="flex items-center gap-2">
              Hızlı Satış Aktif
              <Popover text="Kasiyerin hızlı satış işlemi yapmasına olanak sağlar." />
            </span>
            {/* Switch */}
            <label className="relative inline-flex items-center cursor-pointer no-drag">
              <input
                ref={takeawayEnabledRef}
                type="checkbox"
                defaultChecked={takeawayEnabled}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-restro-green peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600"></div>
            </label>
          </label>
        </div>


        <div className="w-full lg:min-w-96 flex items-center justify-between mt-4">
          <label htmlFor="qrmenu" className="flex items-center gap-2">
            QR Menu Aktif Edilsin Mi?
            <Popover text="Bu sayede link ve QR kodu ile erişilebilen dijital menüye kavuşacaksınız!" />
          </label>

          {/* switch */}
          <label className="relative inline-flex items-center cursor-pointer no-drag">
            <input
              ref={isQRMenuEnabledRef}
              defaultChecked={isQRMenuEnabled}
              type="checkbox"
              name="qrmenu"
              id="qrmenu"
              value=""
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-restro-green peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600"></div>
          </label>
          {/* switch */}
        </div>

        {
          isQRMenuEnabled && <div className="mt-4 flex flex-col lg:flex-row gap-4">
            <button onClick={btnDownloadMenuQR} className="btn btn-sm">
              <IconQrcode stroke={iconStroke} /> QR Kodunu İndirin
            </button>
            <a target="_blank" href={QR_MENU_LINK} className="btn btn-sm">
              <IconExternalLink stroke={iconStroke} /> Dijital Menüyü Görüntüle
            </a>
          </div>
        }

         <div className="w-full lg:min-w-96 flex items-center justify-between mt-4">
            <label htmlFor="qrorder" className="flex items-center gap-2">
              QR Menüsünden Siparişi Etkinleştir
              <Popover text="Bu sayede bağlantı ve QR kodu ile erişilebilen QR menü üzerinden sipariş verme imkânı sağlanacak!" />
            </label>

            {/* switch */}
            <label className="relative inline-flex items-center cursor-pointer no-drag">
              <input
                ref={isQROrderEnabledRef}
                defaultChecked={isQROrderEnabled}
                type="checkbox"
                name="qrorder"
                id="qrorder"
                value=""
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-restro-green peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600"></div>
            </label>
            {/* switch */}
          </div>

        <button onClick={btnSave} className="text-white w-full lg:min-w-96 bg-restro-green transition hover:bg-restro-green/80 active:scale-95 rounded-lg px-4 py-2 mt-6 outline-restro-border-green-light">
          Kaydet
        </button>
        <hr className="my-8 border-gray-200"/>

<div className="mb-6">
  <h4 className="text-xl font-semibold mb-4">Veri Silme</h4>
  <p className="text-sm text-gray-600 mb-2">
    Silmek istediğiniz tabloları seçin (geri alınamaz).
  </p>

  <div className="grid grid-cols-2 gap-2 mb-4">
    {TABLE_OPTIONS.map(tbl => (
      <label key={tbl} className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={selectedTables.includes(tbl)}
          onChange={() => toggleTable(tbl)}
          className="form-checkbox"
        />
        <span className="capitalize">{tbl.replace(/_/g, " ")}</span>
      </label>
    ))}
  </div>

  <button
    onClick={handleDeleteData}
    className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition"
  >
    Seçili Verileri Sil
  </button>
</div>

      </div>
    </Page>
  );
}
