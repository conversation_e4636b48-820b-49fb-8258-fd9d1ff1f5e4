import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { 
  useTranslations, 
  saveTranslation, 
  saveTranslationsForObject,
  saveBatchTranslations,
  deleteAllTranslationsForObject,
  autoTranslate,
} from '../../controllers/translations.controller';
import { useMenuItems } from '../../controllers/menu_item.controller';
import { useCategories } from '../../controllers/settings.controller';


export default function TranslationsManager() {
  // Veri kaynaklarını yükle
  const { data: menuItems = [], error: menuError, isLoading: menuLoading } = useMenuItems();
  const { data: translationsData, error, isLoading, APIURL, mutate } = useTranslations();
  const { data: categories = [], error: catError, isLoading: catLoading } = useCategories();
  
  // Yerel state'leri tanımla
  const [translations, setTranslations] = useState({});
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('menu_items'); // 'menu_items', 'menu_description' veya 'categories'
  const [processingItems, setProcessingItems] = useState([]); // İşlem yapılan öğeleri izlemek için
  const [isBatchProcessing, setIsBatchProcessing] = useState(false); // Toplu işlem yapılıyor mu?

  // API'den gelen çevirileri yerel state'e yükle
  useEffect(() => {
    if (translationsData?.translations && Array.isArray(translationsData.translations)) {
      // Backend API yapısına uygun olarak çevirileri organize et
      const formattedTranslations = {};
      
      translationsData.translations.forEach(item => {
        const key = `${item.object_type}_${item.object_id}`;
        if (!formattedTranslations[key]) {
          formattedTranslations[key] = {};
        }
        formattedTranslations[key][item.language_code] = item.translation;
      });
      
      setTranslations(formattedTranslations);
    }
  }, [translationsData]);

  // Çeviri değişikliklerini takip et
  const handleTranslationChange = (key, lang, value) => {
    setTranslations(prev => ({
      ...prev,
      [key]: { 
        ...(prev[key] || {}), 
        [lang]: value 
      },
    }));
  };

  // Aktif sekmeye göre object type belirle
  const getObjectType = () => {
    if (activeTab === 'categories') return 'category';
    if (activeTab === 'menu_description') return 'menu_description';
    return 'menu_item';
  }

  const handleAutoTranslate = async () => {
    // Aktif sekmeye göre işlem yapılacak öğeleri belirle
    const activeItems = activeTab === 'menu_items' ? filteredMenuItems : 
                        activeTab === 'menu_description' ? filteredMenuItems : 
                        filteredCategories;
    
    const objectType = getObjectType();

    // İşlem görüyor olarak işaretle
    setIsBatchProcessing(true);

    try {
      const toastId = toast.loading('Otomatik çeviriler hazırlanıyor...');

      // Çeviri yapılacak diller
      const targetLanguages = ['en', 'de', 'fr'].filter(lang => lang !== 'tr');

      // Çeviri için hazırlık (sadece boş çeviriler için)
      const translateItems = activeItems
        .filter(item => {
          const key = `${objectType}_${item.id}`;
          return targetLanguages.some(lang => !translations[key]?.[lang]);
        })
        .map(item => ({
          objectType,
          objectId: item.id,
          originalText: activeTab === 'menu_description' ? item.description : item.title,
          targetLanguages
        }));

      // Çeviri yapılacak öğü yoksa işlemi sonlandır
      if (translateItems.length === 0) {
        toast.dismiss(toastId);
        toast.error('Çevrilecek boş alan bulunamadı.');
        return;
      }

      // Toplu çeviri işlemi
      const response = await autoTranslate(translateItems);

      // Gelen çevirileri state'e ekle
      if (response?.translations) {
        const newTranslations = {};

        response.translations.forEach(translationItem => {
          const key = `${translationItem.objectType}_${translationItem.objectId}`;
          newTranslations[key] = {
            ...(translations[key] || {}),
            ...translationItem.translations
          };
        });

        // State'i güncelle (sadece boş alanları doldur)
        setTranslations(prev => {
          const updatedTranslations = { ...prev };
          
          Object.entries(newTranslations).forEach(([key, langs]) => {
            if (!updatedTranslations[key]) {
              updatedTranslations[key] = {};
            }
            
            Object.entries(langs).forEach(([lang, translation]) => {
              // Sadece boş alanları doldur
              if (!updatedTranslations[key][lang]) {
                updatedTranslations[key][lang] = translation;
              }
            });
          });
          
          return updatedTranslations;
        });

        toast.dismiss(toastId);
        toast.success('Otomatik çeviriler hazırlandı.');
      } else {
        toast.dismiss(toastId);
        toast.error('Çeviri alınamadı.');
      }
    } catch (error) {
      toast.error('Otomatik çeviri sırasında bir hata oluştu.');
      console.error('Otomatik çeviri hatası:', error);
    } finally {
      setIsBatchProcessing(false);
    }
  };

  // Tekil çeviri kaydet (Bir öğe için tüm dilleri)
  const handleSaveTranslations = async (objectType, objectId) => {
    const key = `${objectType}_${objectId}`;
    const translationData = translations[key] || {};
    
    // En azından bir dil için çeviri olmalı
    const hasAnyTranslation = Object.values(translationData).some(val => val && val.trim() !== '');
    if (!hasAnyTranslation) {
      toast.error('En az bir dil için çeviri eklemelisiniz.');
      return;
    }
    
    // İşlem yapılan öğeyi işaretle
    setProcessingItems(prev => [...prev, key]);
    
    try {
      const toastId = toast.loading('Kaydediliyor...');
      
      // objectId'nin sayı olduğundan emin ol
      const numericObjectId = parseInt(objectId, 10);
      
      // saveTranslationsForObject kullan (her dil için ayrı çağrı yapar)
      const response = await saveTranslationsForObject(objectType, numericObjectId, translationData);
      
      toast.dismiss(toastId);
      
      if (response?.data?.success) {
        toast.success(response.data.message || 'Çeviriler başarıyla kaydedildi.');
        
        // Veriyi yenile
        await mutate();
      } else {
        const successCount = response?.data?.results?.filter(r => r.success)?.length || 0;
        const totalCount = response?.data?.results?.length || 0;
        
        if (successCount > 0 && successCount < totalCount) {
          toast.success(`${successCount}/${totalCount} dil çevirisi kaydedildi.`);
          await mutate();
        } else {
          toast.error(response?.data?.message || 'Kaydetme işlemi başarısız oldu.');
        }
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || 'Bir hata oluştu.');
      console.error('Çeviri kaydetme hatası:', error);
    } finally {
      // İşlem yapılan öğeyi listeden çıkar
      setProcessingItems(prev => prev.filter(item => item !== key));
    }
  };

  // Tek bir dil çevirisi kaydet
  const handleSaveSingleTranslation = async (objectType, objectId, languageCode) => {
    const key = `${objectType}_${objectId}`;
    const translation = translations[key]?.[languageCode];
    
    if (!translation || translation.trim() === '') {
      toast.error('Boş çeviri kaydedilemez.');
      return;
    }
    
    // İşlem yapılan öğeyi işaretle
    setProcessingItems(prev => [...prev, `${key}_${languageCode}`]);
    
    try {
      const toastId = toast.loading(`${languageCode.toUpperCase()} çevirisi kaydediliyor...`);
      
      // objectId'nin sayı olduğundan emin ol
      const numericObjectId = parseInt(objectId, 10);
      
      // Tek dil çevirisi için saveTranslation kullan
      const response = await saveTranslation(objectType, numericObjectId, languageCode, translation);
      
      toast.dismiss(toastId);
      
      if (response?.data?.success) {
        toast.success(`${languageCode.toUpperCase()} çevirisi kaydedildi.`);
        
        // Veriyi yenile
        await mutate();
      } else {
        toast.error(response?.data?.message || 'Kaydetme işlemi başarısız oldu.');
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || 'Bir hata oluştu.');
      console.error('Çeviri kaydetme hatası:', error);
    } finally {
      // İşlem yapılan öğeyi listeden çıkar
      setProcessingItems(prev => prev.filter(item => item !== `${key}_${languageCode}`));
    }
  };

  // Tüm çevirileri toplu olarak kaydet
  const handleSaveAllTranslations = async () => {
    // Kaydetme yapılacak öğeleri hazırla
    const itemsToSave = [];
    
    // Aktif sekmeye göre işlem yapılacak öğeleri filtrele
    const activeItems = activeTab === 'menu_items' ? filteredMenuItems : 
                        activeTab === 'menu_description' ? filteredMenuItems : 
                        filteredCategories;
    
    const objectType = getObjectType();
    
    // Değişiklik yapılan tüm öğeleri bul
    for (const item of activeItems) {
      const key = `${objectType}_${item.id}`;
      const itemTranslations = translations[key];
      
      if (itemTranslations && Object.keys(itemTranslations).length > 0) {
        const hasTranslations = Object.values(itemTranslations).some(val => val && val.trim() !== '');
        
        if (hasTranslations) {
          // objectId'nin sayı olduğundan emin ol
          const numericObjectId = parseInt(item.id, 10);
          
          itemsToSave.push({
            objectType,
            objectId: numericObjectId,
            translations: itemTranslations
          });
        }
      }
    }
    
    if (itemsToSave.length === 0) {
      toast.error('Kaydedilecek değişiklik bulunamadı.');
      return;
    }
    
    // Toplu işlem durumunu ayarla
    setIsBatchProcessing(true);
    
    try {
      const toastId = toast.loading(`${itemsToSave.length} öğe için çeviriler kaydediliyor...`);
      
      // Toplu güncelleme için saveBatchTranslations kullan
      const response = await saveBatchTranslations(itemsToSave);
      
      toast.dismiss(toastId);
      
      if (response?.data?.success) {
        const successCount = response.data.results.filter(r => r.success).length;
        const totalCount = response.data.results.length;
        
        toast.success(`${successCount}/${totalCount} öğe için çeviriler kaydedildi.`);
        
        // Veriyi yenile
        await mutate();
      } else {
        toast.error(response?.data?.message || 'Kaydetme işlemi başarısız oldu.');
      }
    } catch (error) {
      toast.dismiss();
      toast.error('Toplu kaydetme işlemi sırasında bir hata oluştu.');
      console.error('Toplu çeviri kaydetme hatası:', error);
    } finally {
      setIsBatchProcessing(false);
    }
  };


  // Nesne için tüm çevirileri sil
  const handleDeleteAllTranslations = async (objectType, objectId) => {
    if (!window.confirm('Bu öğe için tüm dillerdeki çevirileri silmek istediğinize emin misiniz?')) {
      return;
    }
    
    const key = `${objectType}_${objectId}`;
    
    // İşlem yapılan öğeyi işaretle
    setProcessingItems(prev => [...prev, key]);
    
    try {
      const toastId = toast.loading('Siliniyor...');
      
      // objectId'nin sayı olduğundan emin ol
      const numericObjectId = parseInt(objectId, 10);
      
      // Öğeye ait tüm çevirileri sil
      const response = await deleteAllTranslationsForObject(objectType, numericObjectId);
      
      toast.dismiss(toastId);
      
      if (response?.data?.success) {
        toast.success(response.data.message || 'Çeviriler başarıyla silindi.');
        
        // Yerel state'den de sil
        setTranslations(prev => {
          const newTranslations = { ...prev };
          delete newTranslations[key];
          return newTranslations;
        });
        
        // Veriyi yenile
        await mutate();
      } else {
        toast.error(response?.data?.message || 'Silme işlemi başarısız oldu.');
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || 'Bir hata oluştu.');
      console.error('Çeviri silme hatası:', error);
    } finally {
      // İşlem yapılan öğeyi listeden çıkar
      setProcessingItems(prev => prev.filter(item => item !== key));
    }
  };

  // Arama filtreleme
  const filteredMenuItems = menuItems?.filter((item) =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
    (activeTab === 'menu_description' && item.description && 
     item.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const filteredCategories = categories?.filter((cat) =>
    cat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Aktif sekme verisi
  const activeData = activeTab === 'menu_items' || activeTab === 'menu_description' ? filteredMenuItems : filteredCategories;
  const isActiveLoading = activeTab === 'categories' ? catLoading : menuLoading;
  const activeError = activeTab === 'categories' ? catError : menuError;

  // Bir öğe işlem görüyor mu kontrolü
  const isItemProcessing = (objectType, objectId, languageCode = null) => {
    const key = `${objectType}_${objectId}`;
    if (languageCode) {
      return processingItems.includes(`${key}_${languageCode}`);
    }
    return processingItems.includes(key);
  };

  // Aktif sekmeye göre tablo başlığını belirle
  const getTableTitleText = () => {
    switch(activeTab) {
      case 'menu_items':
        return 'Ürün';
      case 'menu_description':
        return 'Ürün Açıklaması';
      case 'categories':
        return 'Kategori';
      default:
        return 'Öğe';
    }
  };

  return (
    <div className="p-4 bg-gray-50 min-h-screen">
      <div className="mx-auto">
        <h2 className="text-2xl font-bold mb-4 text-gray-800">Çeviri Yönetimi</h2>

        {/* Sekmeler */}
        <div className="flex gap-4 mb-4">
          <button
            onClick={() => setActiveTab('menu_items')}
            className={`px-4 py-2 rounded ${activeTab === 'menu_items' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            Menü Öğeleri
          </button>
          <button
            onClick={() => setActiveTab('menu_description')}
            className={`px-4 py-2 rounded ${activeTab === 'menu_description' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            Menü Açıklamaları
          </button>
          <button
            onClick={() => setActiveTab('categories')}
            className={`px-4 py-2 rounded ${activeTab === 'categories' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            Kategoriler
          </button>
        </div>

        {/* Arama ve Toplu Kaydet */}
        <div className="flex justify-between items-center mb-4">
          <input
            type="text"
            placeholder={`${getTableTitleText()} ara...`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="border p-2 rounded w-1/3 text-sm shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
          />
            {/* Otomatik Çevir Butonu */}
            <button
              onClick={handleAutoTranslate}
              disabled={isBatchProcessing}
              className="bg-purple-500 text-white px-4 py-2 rounded shadow-sm hover:bg-purple-600 transition mr-2"
            >
              {isBatchProcessing ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  İşleniyor...
                </span>
              ) : 'Otomatik Çevir'}
            </button>
          <button
            onClick={handleSaveAllTranslations}
            disabled={isBatchProcessing}
            className="bg-green-500 text-white px-4 py-2 rounded shadow-sm hover:bg-green-600 transition"
          >
            {isBatchProcessing ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                İşleniyor...
              </span>
            ) : 'Tümünü Kaydet'}
          </button>
        </div>

        {isLoading || isActiveLoading ? (
          <p className="text-center text-gray-600">Yükleniyor...</p>
        ) : error || activeError ? (
          <p className="text-center text-red-500">Veri yüklenirken hata oluştu.</p>
        ) : !activeData || activeData.length === 0 ? (
          <p className="text-center text-gray-600">Sonuç bulunamadı.</p>
        ) : (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="w-full text-sm text-left text-gray-700">
              <thead className="bg-gray-200 text-gray-800">
                <tr>
                  <th className="p-3 font-semibold">{getTableTitleText()}</th>
                  <th className="p-3 font-semibold">EN</th>
                  <th className="p-3 font-semibold">DE</th>
                  <th className="p-3 font-semibold">FR</th>
                  <th className="p-3 font-semibold w-36">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                {activeData.map((item) => {
                  const objectType = getObjectType();
                  const key = `${objectType}_${item.id}`;
                  const isProcessing = isItemProcessing(objectType, item.id);
                  
                  return (
                    <tr key={item.id} className="border-b hover:bg-gray-50">
                      <td className="p-3 truncate max-w-xs">
                        {activeTab === 'menu_description' ? (
                          <div>
                            <div className="font-medium">{item.title}</div>
                            <div className="text-xs text-gray-500 mt-1 truncate">{item.description || 'Açıklama yok'}</div>
                          </div>
                        ) : (
                          item.title
                        )}
                      </td>
                      
                      {['en', 'de', 'fr'].map((lang) => {
                        const isLangProcessing = isItemProcessing(objectType, item.id, lang);
                        const hasNoDescription = activeTab === 'menu_description' && (!item.description || item.description.trim() === '');
                        
                        return (
                          <td key={lang} className="p-3">
                            <div className="flex">
                              <input
                                type="text"
                                value={translations[key]?.[lang] || ''}
                                onChange={(e) => handleTranslationChange(key, lang, e.target.value)}
                                className={`border p-1 flex-grow rounded-l text-sm focus:outline-none focus:ring-1 focus:ring-blue-400 ${hasNoDescription ? 'bg-gray-100' : ''}`}
                                placeholder={hasNoDescription ? 'Açıklama yok' : lang.toUpperCase()}
                                disabled={isProcessing || isLangProcessing || isBatchProcessing || hasNoDescription}
                              />
                              <button
                                onClick={() => handleSaveSingleTranslation(objectType, item.id, lang)}
                                className={`bg-gray-200 text-gray-700 px-2 rounded-r hover:bg-gray-300 text-xs ${hasNoDescription ? 'opacity-50 cursor-not-allowed' : ''}`}
                                title={hasNoDescription ? 'Açıklama olmadığı için çeviri yapılamaz' : `Sadece ${lang.toUpperCase()} dilini kaydet`}
                                disabled={isProcessing || isLangProcessing || isBatchProcessing || !translations[key]?.[lang] || hasNoDescription}
                              >
                                {isLangProcessing ? '...' : 'Kaydet'}
                              </button>
                            </div>
                          </td>
                        );
                      })}
                      
                      <td className="p-3 flex gap-2">
                        <button
                          onClick={() => handleSaveTranslations(objectType, item.id)}
                          className={`bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition flex-1 ${activeTab === 'menu_description' && (!item.description || item.description.trim() === '') ? 'opacity-50 cursor-not-allowed' : ''}`}
                          disabled={isProcessing || isBatchProcessing || (activeTab === 'menu_description' && (!item.description || item.description.trim() === ''))}
                          title={activeTab === 'menu_description' && (!item.description || item.description.trim() === '') ? 'Açıklama olmadığı için çeviri yapılamaz' : ''}
                        >
                          {isProcessing ? (
                            <span className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              <span>İşleniyor</span>
                            </span>
                          ) : 'Tümünü Kaydet'}
                        </button>
                        
                        <button
                          onClick={() => handleDeleteAllTranslations(objectType, item.id)}
                          className={`bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600 transition ${activeTab === 'menu_description' && (!item.description || item.description.trim() === '') ? 'opacity-50 cursor-not-allowed' : ''}`}
                          disabled={isProcessing || isBatchProcessing || (activeTab === 'menu_description' && (!item.description || item.description.trim() === ''))}
                          title={activeTab === 'menu_description' && (!item.description || item.description.trim() === '') ? 'Açıklama olmadığı için çeviri yapılamaz' : 'Tüm dillerdeki çevirileri sil'}
                        >
                          {isProcessing ? '...' : 'Sil'}
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}