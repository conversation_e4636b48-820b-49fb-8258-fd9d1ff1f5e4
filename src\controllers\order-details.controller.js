import ApiClient from "../helpers/ApiClient";
import useSWR from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

export function useOrderDetails(filters = {}) {
  // Filtreleri URL parametrelerine dönüştür
  const queryParams = new URLSearchParams();

  if (filters.startDate) queryParams.append("startDate", filters.startDate);
  if (filters.endDate) queryParams.append("endDate", filters.endDate);
  if (filters.status) queryParams.append("status", filters.status);
  if (filters.paymentStatus) queryParams.append("paymentStatus", filters.paymentStatus);
  if (filters.tableId) queryParams.append("tableId", filters.tableId);
  if (filters.floorId) queryParams.append("floorId", filters.floorId);
  if (filters.customerId) queryParams.append("customerId", filters.customerId);
  if (filters.type) queryParams.append("type", filters.type);

  const queryString = queryParams.toString();
  const APIURL = `/order-details${queryString ? `?${queryString}` : ""}`;

  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);

  // Veriyi işle ve organize et
  const processedData = data ? {
    success: data.success,
    summary: data.summary || {
      totalOrders: 0,
      totalAmount: 0,
      totalTax: 0,
      totalPaid: 0,
      totalRemaining: 0,
      period: {
        startDate: filters.startDate,
        endDate: filters.endDate
      }
    },
    orders: data.data?.orders || [],
    orderItems: data.data?.orderItems || [],
    addons: data.data?.addons || [],
    payments: data.data?.payments || [],
    invoices: data.data?.invoices || [],
    discounts: data.data?.discounts || []
  } : null;

  return {
    data: processedData,
    rawData: data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export async function exportOrderDetailsToExcel(filters = {}) {
  try {
    // Filtreleri URL parametrelerine dönüştür
    const queryParams = new URLSearchParams();

    if (filters.startDate) queryParams.append("startDate", filters.startDate);
    if (filters.endDate) queryParams.append("endDate", filters.endDate);
    if (filters.status) queryParams.append("status", filters.status);
    if (filters.paymentStatus) queryParams.append("paymentStatus", filters.paymentStatus);
    if (filters.tableId) queryParams.append("tableId", filters.tableId);
    if (filters.floorId) queryParams.append("floorId", filters.floorId);
    if (filters.customerId) queryParams.append("customerId", filters.customerId);
    if (filters.type) queryParams.append("type", filters.type);

    const queryString = queryParams.toString();
    const url = `/order-details/export${queryString ? `?${queryString}` : ""}`;

    const response = await ApiClient.get(url, { responseType: 'blob' });

    // Excel dosyasını indir
    const downloadUrl = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = downloadUrl;

    // Dosya adını oluştur
    let fileName = 'siparis-detaylari';
    if (filters.startDate && filters.endDate) {
      const startDate = new Date(filters.startDate.split('T')[0]);
      const endDate = new Date(filters.endDate.split('T')[0]);
      fileName += `-${startDate.toISOString().split('T')[0]}-${endDate.toISOString().split('T')[0]}`;
    } else if (filters.type) {
      fileName += `-${filters.type}`;
    }
    fileName += '.xlsx';

    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    link.remove();

    return response;
  } catch (error) {
    throw error;
  }
}
