import React, { useRef, useState } from "react";
import Page from "../components/Page";
import { IconDownload, IconFilter, IconSearch } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { useOrderDetails, exportOrderDetailsToExcel } from "../controllers/order-details.controller";
import { CURRENCIES } from "../config/currencies.config";
import { toast } from "react-hot-toast";
import { useStoreTables } from "../controllers/settings.controller";
import { useFloors } from "../controllers/floors.controller";

// Sayısal değerleri formatlamak için yardımcı fonksiyon
const formatNumber = (value) => {
  if (!value) return "0";
  // String ise sayıya çevirelim
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return numValue.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// Tarih formatlamak için yardımcı fonksiyon
const formatDate = (dateString) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleString('tr-TR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export default function OrderDetailsPage() {
  const filters = [
    { key: "today", value: "Bugün" },
    { key: "yesterday", value: "Dün" },
    { key: "last_7days", value: "Son 7 Gün" },
    { key: "this_month", value: "Bu Ay" },
    { key: "last_month", value: "Geçen Ay" },
    { key: "custom", value: "Özel Tarih" },
  ];

  const statusOptions = [
    { value: "", label: "Tüm Durumlar" },
    { value: "pending", label: "Beklemede" },
    { value: "processing", label: "İşleniyor" },
    { value: "completed", label: "Tamamlandı" },
    { value: "cancelled", label: "İptal Edildi" }
  ];

  const paymentStatusOptions = [
    { value: "", label: "Tüm Ödeme Durumları" },
    { value: "paid", label: "Ödendi" },
    { value: "unpaid", label: "Ödenmedi" },
    { value: "partial", label: "Kısmi Ödeme" }
  ];

  const fromDateRef = useRef();
  const toDateRef = useRef();
  const fromTimeRef = useRef();
  const toTimeRef = useRef();
  const filterTypeRef = useRef();
  const statusRef = useRef();
  const paymentStatusRef = useRef();
  const tableIdRef = useRef();
  const floorIdRef = useRef();
  const searchRef = useRef();

  const now = new Date();
  const defaultDateFrom = `${now.getFullYear()}-${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;
  const defaultDateTo = `${now.getFullYear()}-${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;
  const defaultTimeFrom = "00:00";
  const defaultTimeTo = "23:59";

  const [state, setState] = useState({
    filter: filters[0].key,
    fromDate: null,
    toDate: null,
    fromTime: null,
    toTime: null,
    status: "",
    paymentStatus: "",
    tableId: "",
    floorId: "",
    searchQuery: ""
  });

  // Masa listesini al
  const { data: tablesData } = useStoreTables();
  const tables = tablesData?.data || [];

  // Floor listesini al
  const { data: floors } = useFloors();
  const floorsList = floors || [];

  // Sipariş detaylarını al
  const { data, error, isLoading } = useOrderDetails({
    type: state.filter,
    startDate: state.fromDate ? `${state.fromDate}T${state.fromTime || defaultTimeFrom}` : null,
    endDate: state.toDate ? `${state.toDate}T${state.toTime || defaultTimeTo}` : null,
    status: state.status || null,
    paymentStatus: state.paymentStatus || null,
    tableId: state.tableId || null,
    floorId: state.floorId || null,
    customerId: null, // Şimdilik müşteri ID'si filtrelemesi yapmıyoruz
  });

  // Excel'e aktarma fonksiyonu
  const handleExportToExcel = async () => {
    try {
      toast.loading("Excel dosyası hazırlanıyor...");
      await exportOrderDetailsToExcel({
        type: state.filter,
        startDate: state.fromDate ? `${state.fromDate}T${state.fromTime || defaultTimeFrom}` : null,
        endDate: state.toDate ? `${state.toDate}T${state.toTime || defaultTimeTo}` : null,
        status: state.status || null,
        paymentStatus: state.paymentStatus || null,
        tableId: state.tableId || null,
        floorId: state.floorId || null,
        customerId: null,
      });
      toast.dismiss();
      toast.success("Excel dosyası indirildi");
    } catch (error) {
      toast.dismiss();
      console.error("Excel dışa aktarma hatası:", error);
      toast.error("Excel dosyası oluşturulurken bir hata oluştu");
    }
  };

  // Arama fonksiyonu
  const handleSearch = () => {
    setState({
      ...state,
      searchQuery: searchRef.current.value
    });
  };

  if (isLoading) {
    return <Page>
      Lütfen bekleyin...
    </Page>
  }

  if (error) {
    console.error(error);
    return <Page>
      Sipariş detayları yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin!
    </Page>;
  }

  if (!data || !data.success) {
    return <Page>
      Sipariş detayları yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin!
    </Page>;
  }

  // Veriyi filtrele (arama sorgusu varsa)
  let filteredOrders = data.orders || [];
  if (state.searchQuery) {
    const query = state.searchQuery.toLowerCase();
    filteredOrders = filteredOrders.filter(order =>
      (order.id && order.id.toString().includes(query)) ||
      (order.customer_name && order.customer_name.toLowerCase().includes(query)) ||
      (order.table_title && order.table_title.toLowerCase().includes(query)) ||
      (order.token_no && order.token_no.toString().includes(query))
    );
  }

  // Sipariş öğelerini siparişlerle eşleştir
  const orderItems = data.orderItems || [];
  const addons = data.addons || [];
  const payments = data.payments || [];
  const discounts = data.discounts || [];

  // Her sipariş için ilgili öğeleri bul
  filteredOrders.forEach(order => {
    order.items = orderItems.filter(item => item.order_id === order.id);

    // Her öğe için ilgili eklentileri bul
    order.items.forEach(item => {
      if (item.addons) {
        try {
          const addonIds = JSON.parse(item.addons);
          item.addonDetails = addons.filter(addon => addonIds.includes(addon.id));
        } catch (e) {
          item.addonDetails = [];
        }
      } else {
        item.addonDetails = [];
      }
    });

    // Siparişe ait ödemeleri bul
    order.paymentDetails = payments.filter(payment => payment.order_id === order.id);

    // Siparişe ait indirimleri bul
    order.discountDetails = discounts.filter(discount => discount.order_id === order.id);
  });

  const currency = "₺"; // Varsayılan para birimi

  return (
    <Page>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Sipariş Detayları</h1>

        <div className="flex items-center gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Sipariş ara..."
              ref={searchRef}
              className="px-4 py-2 pr-10 border rounded-lg"
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
            <button
              onClick={handleSearch}
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <IconSearch size={20} stroke={iconStroke} />
            </button>
          </div>

          <button
            onClick={() => document.getElementById("filter-dialog").showModal()}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-gray-200"
          >
            <IconFilter stroke={iconStroke} />
            Filtre
          </button>

          <button
            onClick={handleExportToExcel}
            className="bg-restro-primary text-black px-4 py-2 rounded-lg flex items-center gap-2"
          >
            <IconDownload size={20} stroke={iconStroke} />
            Excel'e Aktar
          </button>
        </div>
      </div>

      <h3 className="mt-6 mb-4 text-base">
        {filters.find(f => f.key === state.filter)?.value} Tarihli Sipariş Detayları
        {state.status && ` - Durum: ${statusOptions.find(s => s.value === state.status)?.label}`}
        {state.paymentStatus && ` - Ödeme Durumu: ${paymentStatusOptions.find(s => s.value === state.paymentStatus)?.label}`}
        {state.floorId && ` - Alan: ${floorsList.find(f => f.id === parseInt(state.floorId))?.title || state.floorId}`}
        {state.tableId && ` - Masa: ${tables.find(t => t.encrypted_id === state.tableId)?.table_title || state.tableId}`}
      </h3>

      {/* Özet Bilgiler */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
          <div className="text-sm text-gray-500">Toplam Sipariş</div>
          <div className="text-2xl font-bold">{data.summary.totalOrders}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
          <div className="text-sm text-gray-500">Toplam Tutar</div>
          <div className="text-2xl font-bold">{formatNumber(data.summary.totalAmount)} {currency}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
          <div className="text-sm text-gray-500">Toplam Vergi</div>
          <div className="text-2xl font-bold">{formatNumber(data.summary.totalTax)} {currency}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
          <div className="text-sm text-gray-500">Toplam Ödenen</div>
          <div className="text-2xl font-bold">{formatNumber(data.summary.totalPaid)} {currency}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border border-gray-100">
          <div className="text-sm text-gray-500">Toplam Kalan</div>
          <div className="text-2xl font-bold">{formatNumber(data.summary.totalRemaining)} {currency}</div>
        </div>
      </div>

      {/* Sipariş Detayları Tablosu */}
      <div className="overflow-x-auto mt-4 min-h-[50vh]">
        <table className="table w-full">
          <thead>
            <tr>
              <th>Sipariş ID</th>
              <th>Fiş No</th>
              <th>Tarih</th>
              <th>Müşteri</th>
              <th>Masa</th>
              <th>Durum</th>
              <th>Ödeme Durumu</th>
              <th>Toplam</th>
              <th>Detaylar</th>
            </tr>
          </thead>
          <tbody>
            {filteredOrders.length === 0 ? (
              <tr>
                <td colSpan="9" className="text-center py-4">
                  Sipariş bulunamadı
                </td>
              </tr>
            ) : (
              filteredOrders.map((order, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td>{order.id || "-"}</td>
                  <td>{order.token_no || "-"}</td>
                  <td>{formatDate(order.date)}</td>
                  <td>
                    {order.customer_name || "-"}
                    {order.customer_phone && <div className="text-xs text-gray-500">{order.customer_phone}</div>}
                  </td>
                  <td>{order.table_title || "-"}</td>
                  <td>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      order.status === 'completed' ? 'bg-green-100 text-green-800' :
                      order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                      order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {order.status === 'completed' ? 'Tamamlandı' :
                       order.status === 'cancelled' ? 'İptal Edildi' :
                       order.status === 'created' ? 'Oluşturuldu' :
                       'İşleniyor'}
                    </span>
                  </td>
                  <td>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      order.payment_status === 'paid' ? 'bg-green-100 text-green-800' :
                      order.payment_status === 'pending' ? 'bg-red-100 text-red-800' :
                      order.payment_status === 'credit' ? 'bg-purple-100 text-purple-800' :
                      order.payment_status === 'refunded' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {order.payment_status === 'paid' ? 'Ödendi' :
                       order.payment_status === 'pending' ? 'Beklemede' :
                       order.payment_status === 'credit' ? 'Cari' :
                       order.payment_status === 'refunded' ? 'İade Edildi' :
                       order.payment_status === 'partial' ? 'Kısmi Ödeme' :
                       'Bilinmiyor'}
                    </span>
                  </td>
                  <td className="font-medium">{formatNumber(order.calculatedTotal || 0)} {currency}</td>
                  <td>
                    <details className="dropdown dropdown-end">
                      <summary className="btn btn-xs">Detaylar</summary>
                      <div className="p-4 shadow dropdown-content z-[1] bg-base-100 rounded-box w-80">
                        <h4 className="font-bold mb-2">Sipariş Öğeleri</h4>
                        {order.items && order.items.length > 0 ? (
                          <ul className="space-y-2">
                            {order.items.map((item, idx) => (
                              <li key={idx} className="border-b pb-2">
                                <div className="flex justify-between">
                                  <span className="font-medium">{item.quantity}x {item.item_title}</span>
                                  <span>{formatNumber(item.order_item_price * item.quantity)} {currency}</span>
                                </div>
                                {item.addonDetails && item.addonDetails.length > 0 && (
                                  <div className="text-xs text-gray-500 pl-2 mt-1">
                                    <div className="font-medium">Eklentiler:</div>
                                    {item.addonDetails.map((addon, aidx) => (
                                      <div key={aidx} className="flex justify-between">
                                        <span>+ {addon.title}</span>
                                        <span>{formatNumber(addon.price)} {currency}</span>
                                      </div>
                                    ))}
                                  </div>
                                )}
                                {item.status && item.status !== 'created' && (
                                  <div className="text-xs mt-1">
                                    <span className={`px-2 py-1 rounded-full ${
                                      item.status === 'completed' ? 'bg-green-100 text-green-800' :
                                      item.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                      item.status === 'complimentary' ? 'bg-purple-100 text-purple-800' :
                                      item.status === 'waste' ? 'bg-orange-100 text-orange-800' :
                                      'bg-blue-100 text-blue-800'
                                    }`}>
                                      {item.status === 'completed' ? 'Tamamlandı' :
                                       item.status === 'cancelled' ? 'İptal Edildi' :
                                       item.status === 'preparing' ? 'Hazırlanıyor' :
                                       item.status === 'delivered' ? 'Teslim Edildi' :
                                       item.status === 'complimentary' ? 'İkram' :
                                       item.status === 'waste' ? 'Zayi' :
                                       'Bilinmiyor'}
                                    </span>
                                  </div>
                                )}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500">Sipariş öğesi bulunamadı</p>
                        )}

                        {order.paymentDetails && order.paymentDetails.length > 0 && (
                          <div className="mt-4">
                            <h4 className="font-bold mb-2">Ödemeler</h4>
                            <ul className="space-y-1">
                              {order.paymentDetails.map((payment, pidx) => (
                                <li key={pidx} className="flex justify-between text-sm">
                                  <span>{payment.payment_type}</span>
                                  <span>{formatNumber(payment.amount)} {currency}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {order.discountDetails && order.discountDetails.length > 0 && (
                          <div className="mt-4">
                            <h4 className="font-bold mb-2">İndirimler</h4>
                            <ul className="space-y-1">
                              {order.discountDetails.map((discount, didx) => (
                                <li key={didx} className="flex justify-between text-sm">
                                  <span>
                                    {discount.discount_type === 'percentage' ? `%${discount.discount_value}` : ''}
                                    {discount.discount_type === 'amount' ? `${formatNumber(discount.discount_value)} ${currency}` : ''}
                                  </span>
                                  <span>{formatDate(discount.created_at)}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        <div className="mt-4 pt-2 border-t">
                          <div className="flex justify-between">
                            <span className="font-medium">Toplam:</span>
                            <span className="font-bold">{formatNumber(order.calculatedTotal || 0)} {currency}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Vergi:</span>
                            <span>{formatNumber(order.calculatedTax || 0)} {currency}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Ödenen:</span>
                            <span>{formatNumber(order.calculatedPaid || 0)} {currency}</span>
                          </div>
                          {order.calculatedRemaining > 0 && (
                            <div className="flex justify-between text-sm text-red-600 font-medium">
                              <span>Kalan:</span>
                              <span>{formatNumber(order.calculatedRemaining || 0)} {currency}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </details>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Filtre Modalı */}
      <dialog id="filter-dialog" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg flex items-center">
            <IconFilter stroke={iconStroke} /> Filtre
          </h3>
          {/* Filtreler */}
          <div className="my-4">
            <div>
              <label className="block text-gray-500 text-sm">Tarih Filtresi</label>
              <select
                className="select select-sm select-bordered w-full"
                ref={filterTypeRef}
                defaultValue={state.filter}
              >
                {filters.map((filter, index) => (
                  <option key={index} value={filter.key}>
                    {filter.value}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex gap-2 mt-4">
              <div className="flex-1">
                <label
                  htmlFor="fromDate"
                  className="block text-gray-500 text-sm"
                >
                  Başlangıç Tarihi
                </label>
                <input
                  defaultValue={state.fromDate || defaultDateFrom}
                  type="date"
                  ref={fromDateRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
              <div className="flex-1">
                <label
                  htmlFor="fromTime"
                  className="block text-gray-500 text-sm"
                >
                  Başlangıç Saati
                </label>
                <input
                  defaultValue={state.fromTime || defaultTimeFrom}
                  type="time"
                  ref={fromTimeRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <div className="flex-1">
                <label
                  htmlFor="toDate"
                  className="block text-gray-500 text-sm"
                >
                  Bitiş Tarihi
                </label>
                <input
                  defaultValue={state.toDate || defaultDateTo}
                  type="date"
                  ref={toDateRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
              <div className="flex-1">
                <label
                  htmlFor="toTime"
                  className="block text-gray-500 text-sm"
                >
                  Bitiş Saati
                </label>
                <input
                  defaultValue={state.toTime || defaultTimeTo}
                  type="time"
                  ref={toTimeRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-gray-500 text-sm">Sipariş Durumu</label>
              <select
                className="select select-sm select-bordered w-full"
                ref={statusRef}
                defaultValue={state.status}
              >
                {statusOptions.map((option, index) => (
                  <option key={index} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="mt-4">
              <label className="block text-gray-500 text-sm">Ödeme Durumu</label>
              <select
                className="select select-sm select-bordered w-full"
                ref={paymentStatusRef}
                defaultValue={state.paymentStatus}
              >
                {paymentStatusOptions.map((option, index) => (
                  <option key={index} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="mt-4">
              <label className="block text-gray-500 text-sm">Alan/Kat</label>
              <select
                className="select select-sm select-bordered w-full"
                ref={floorIdRef}
                defaultValue={state.floorId}
              >
                <option value="">Tüm Alanlar</option>
                {floorsList.map((floor) => (
                  <option key={floor.id} value={floor.id}>
                    {floor.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="mt-4">
              <label className="block text-gray-500 text-sm">Masa</label>
              <select
                className="select select-sm select-bordered w-full"
                ref={tableIdRef}
                defaultValue={state.tableId}
              >
                <option value="">Tüm Masalar</option>
                {tables.map((table) => (
                  <option key={table.encrypted_id} value={table.encrypted_id}>
                    {table.table_title}
                  </option>
                ))}
              </select>
            </div>
          </div>
          {/* Filtreler */}
          <div className="modal-action">
            <form method="dialog">
              <button className="btn">Kapat</button>
              <button
                onClick={() => {
                  setState({
                    ...state,
                    filter: filterTypeRef.current.value,
                    fromDate: fromDateRef.current.value || null,
                    toDate: toDateRef.current.value || null,
                    fromTime: fromTimeRef.current.value || null,
                    toTime: toTimeRef.current.value || null,
                    status: statusRef.current.value,
                    paymentStatus: paymentStatusRef.current.value,
                    floorId: floorIdRef.current.value,
                    tableId: tableIdRef.current.value,
                  });
                }}
                className="btn ml-2"
              >
                Uygula
              </button>
            </form>
          </div>
        </div>
      </dialog>
    </Page>
  );
}
