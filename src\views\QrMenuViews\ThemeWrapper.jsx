import React, { Suspense, useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { getQRMenuInit } from '../../controllers/qrmenu.controller';
import { loadThemeComponents } from '../../helpers/ThemeLoader';

/**
 * QR Menü Tema Wrapper
 * Backend'den gelen template_id'ye göre doğru tema component'ini yükler
 */
export default function ThemeWrapper({ componentType }) {
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;
  
  const [ThemeComponent, setThemeComponent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadThemeComponent();
  }, [qrcode, componentType]);

  const loadThemeComponent = async () => {
    try {
      setIsLoading(true);
      
      // QR menü verilerini al
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      
      if (res.status === 200) {
        const data = res.data;
        const templateId = data?.storeSettings?.template_id || 1; // Varsayılan: 1 (classic)
        
        // Tema component'lerini yükle
        const themeComponents = loadThemeComponents(templateId);
        
        // İlgili component'i seç
        const ComponentToLoad = themeComponents[componentType];
        
        if (ComponentToLoad) {
          setThemeComponent(() => ComponentToLoad);
        } else {
          throw new Error(`Component type '${componentType}' not found`);
        }
      } else {
        throw new Error('QR menü verileri alınamadı');
      }
    } catch (error) {
      console.error('Tema yüklenirken hata:', error);
      setError(error.message);
      
      // Hata durumunda varsayılan tema yükle
      try {
        const defaultThemeComponents = loadThemeComponents(1); // Classic tema
        const DefaultComponent = defaultThemeComponents[componentType];
        setThemeComponent(() => DefaultComponent);
      } catch (fallbackError) {
        console.error('Varsayılan tema yüklenemedi:', fallbackError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state - tema component'inde hallediliyor
  if (isLoading) {
    return null;
  }

  // Error state
  if (error && !ThemeComponent) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <p className="font-bold">Tema Yükleme Hatası</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Render theme component
  if (ThemeComponent) {
    return (
      <Suspense fallback={null}>
        <ThemeComponent />
      </Suspense>
    );
  }

  return null;
}
