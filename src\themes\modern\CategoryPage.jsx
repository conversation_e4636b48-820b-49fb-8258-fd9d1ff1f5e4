import React, { useEffect, useState } from "react";
import { IconChevronLeft, IconCategory } from "@tabler/icons-react";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";

export default function CategoryPage() {
  const { t, i18n } = useTranslation(['category']);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("category");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        const currency = CURRENCIES.find(
          (c) => c.cc === data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          currency: currency?.symbol || "",
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, categories } = state;
  const storeName = storeSettings?.store_name || "";

  if (isLoading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">Lütfen bekleyin...</p>
        </div>
      </div>
    );
  }

  // Modern tema stilleri
  const bodyStyles = {
    background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
    minHeight: "100vh"
  };

  const headerStyles = {
    background: "linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)",
    boxShadow: "0 4px 12px rgba(37, 99, 235, 0.3)"
  };

  return (
    <div className="w-full" style={bodyStyles}>
      {/* Modern Header */}
      <header className="w-full py-6 sticky top-0 z-50" style={headerStyles}>
        <div className="container mx-auto px-4 flex items-center justify-between">
          <IconChevronLeft
            size={28}
            stroke={3}
            className="cursor-pointer text-white hover:bg-white/20 rounded-full p-1 transition-all"
            onClick={() => navigate(-1)}
          />
          <div className="flex items-center">
            {storeSettings?.store_image ? (
              <img
                src={getImageURL(storeSettings.store_image)}
                alt={storeName}
                className="h-12 object-contain"
              />
            ) : (
              <h1 className="text-xl font-bold text-white">{storeName}</h1>
            )}
          </div>
          <div className="flex items-center w-6"></div>
        </div>
      </header>

      <div className="container mx-auto px-4">
        {/* Modern Page Title */}
        <div className="text-center py-8">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            {t("categories")}
          </h2>
          <p className="text-gray-600">Kategori seçin ve menüyü keşfedin</p>
        </div>

        {/* Modern Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pb-8">
          {categories.map((category) => {
            const { id, name, image } = category;
            const imageURL = getImageURL(image);

            return (
              <div
                key={id}
                className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-2xl transition-all duration-300 cursor-pointer hover:-translate-y-2 group"
                onClick={() => {
                  navigate(
                    encryptedTableId
                      ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                      : `/m/${qrcode}/menu`,
                    {
                      state: { selectedCategory: id },
                    }
                  );
                }}
              >
                <div className="flex flex-col items-center text-center space-y-4">
                  {/* Modern Category Image */}
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                    {image ? (
                      <img
                        src={imageURL}
                        alt={name}
                        className="w-full h-full object-cover rounded-2xl"
                      />
                    ) : (
                      <IconCategory size={40} className="text-blue-600" />
                    )}
                  </div>

                  {/* Modern Category Name */}
                  <h3 className="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">
                    {t(`category:${id}`, { defaultValue: name })}
                  </h3>

                  {/* Modern Hover Effect */}
                  <div className="w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                </div>
              </div>
            );
          })}

          {/* Modern "All Categories" Option */}
          <div
            className="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 cursor-pointer hover:-translate-y-2 group"
            onClick={() => {
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                  : `/m/${qrcode}/menu`,
                {
                  state: { selectedCategory: "all" },
                }
              );
            }}
          >
            <div className="flex flex-col items-center text-center space-y-4">
              <div className="w-24 h-24 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                <IconCategory size={40} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-white">
                {t("allCategories")}
              </h3>
              <div className="w-full h-1 bg-white/50 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
