import React, { useEffect, useState } from "react";
import { IconChevronLeft } from "@tabler/icons-react";
import { useParams, useSearchParams, useNavigate, useLocation } from "react-router-dom";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function CategoryPage() {
  const { t, i18n } = useTranslation(['category']);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const location = useLocation();
  const parentCategory = location.state?.parentCategory || null;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("category");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        const currency = CURRENCIES.find(
          (c) => c.cc === data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          currency: currency?.symbol || "",
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - Kategoriler - SewPOS`;
        }
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, categories } = state;
  const storeName = storeSettings?.store_name || "";

  // Parent kategoriye göre alt kategorileri filtrele
  const filteredCategories = categories.filter(category => 
    category.parent_id === parentCategory
  );

  if (isLoading) {
    return <QRMenuLoading />;
  }

  return (
    <div
  className="flex flex-col justify-between min-h-screen w-full"
  style={{ backgroundColor: '#f8f7f3', fontFamily: 'Georgia, "Times New Roman", serif' }}
>
      {/* MINIMAL HEADER */}
      <header className="sticky top-0 z-10" style={{ backgroundColor: '#f8f7f3' }}>
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <IconChevronLeft
              size={24}
              className="cursor-pointer text-gray-600 hover:text-gray-800"
              onClick={() => navigate(-1)}
            />
            <div className="text-center">
              {storeSettings?.store_image ? (
                <img
                  src={getImageURL(storeSettings.store_image)}
                  alt={storeName}
                  className="h-8 object-contain mx-auto"
                />
              ) : (
                <h1 className="text-lg font-normal text-gray-900">
                  {storeName}
                </h1>
              )}
            </div>
            <div className="w-6"></div>
          </div>
        </div>
      </header>

      {/* CATEGORIES LIST */}
      <div className="max-w-4xl mx-auto px-6 pb-8 w-full">
        <div className="mb-8">
          

          {/* Categories List */}
          <div className="space-y-2">
            {filteredCategories.map((category) => {
              const { id, title } = category;

              return (
                <div
  key={id}
  className="flex justify-between items-center py-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors"
  onClick={() => {
    navigate(
      encryptedTableId
        ? `/m/${qrcode}/menu?table=${encryptedTableId}`
        : `/m/${qrcode}/menu`,
      {
        state: { selectedCategory: id },
      }
    );
  }}
>
  <span className="text-gray-900 text-sm font-normal">
    {t(`category:${id}`, { defaultValue: title })}
  </span>

  {/* Ok işareti */}
  <span className="text-gray-400 text-sm">&#8250;</span>
</div>

              );
            })}

            
          </div>
        </div>
      </div>

      {/* LANGUAGE FOOTER */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-12">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex justify-center">
            <div className="flex items-center space-x-6">
              <button
                onClick={() => i18n.changeLanguage('tr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'tr' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Türkçe
              </button>
              <button
                onClick={() => i18n.changeLanguage('en')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'en' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                English
              </button>
              <button
                onClick={() => i18n.changeLanguage('fr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'fr' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Français
              </button>
              <button
                onClick={() => i18n.changeLanguage('de')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'de' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Deutsch
              </button>
              <button
                onClick={() => i18n.changeLanguage('ru')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ru' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Русский
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}