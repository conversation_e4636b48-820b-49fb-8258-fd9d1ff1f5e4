import React, { useEffect, useState, useRef } from "react";
import {
  IconChevronLeft,
  IconChevronRight,
} from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import {
  getCart,
  setCart,
  getQRMenuInit,
} from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import toast from "react-hot-toast";
import { useParams, useSearchParams, useNavigate, useLocation } from "react-router-dom";
import { getQRMenuLink } from "../../helpers/QRMenuHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function MenuPage() {
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;

  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;
  const location = useLocation();
  const defaultCategory = location.state?.selectedCategory || "all";

  const { t } = useTranslation(['menu_item', 'category', 'menu_description']);

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    storeTable: null,
    categories: [],
    currentCategory: defaultCategory,
    menuItems: [],
    currentItem: null,
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);

      if (res.status == 200) {
        const data = res.data;

        const currency = CURRENCIES.find(
          (c) => c.cc == data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          ...state,
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          menuItems: data?.menuItems,
          storeTable: data?.storeTable || null,
          currency: currency?.symbol || "",
        });
      }
    } catch (error) {
      console.error("Menu loading error:", error);
    }
  };

  const {
    isLoading,
    storeSettings,
    categories,
    menuItems,
    currentCategory,
    currency,
  } = state;

  const storeName = storeSettings?.store_name || "";
  const is_qr_menu_enabled = storeSettings?.is_qr_menu_enabled || false;
  const storeImage = storeSettings?.store_image;

  // Dil değiştirme fonksiyonu
  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  if (isLoading) {
    return <QRMenuLoading theme="minimal" />;
  }

  if (!qrcode) {
    return (
      <div className="w-full min-h-screen bg-white flex items-center justify-center">
        <div className="bg-gray-50 rounded-lg p-8 shadow-sm border">
          <p className="text-gray-800 font-medium">Broken Link!</p>
        </div>
      </div>
    );
  }

  if (!is_qr_menu_enabled) {
    return (
      <div className="w-full min-h-screen bg-white flex items-center justify-center">
        <div className="bg-gray-50 rounded-lg p-8 shadow-sm border">
          <p className="text-gray-800 font-medium">Menu Not Available!</p>
        </div>
      </div>
    );
  }

  // Kategoriye göre filtreleme
  const getFilteredMenuItems = () => {
    return menuItems.filter((item) => {
      if (currentCategory === "all") return true;
      return item.category_id === currentCategory;
    });
  };

  return (
    <div className="w-full min-h-screen bg-white">
      {/* HEADER */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <IconChevronLeft
              size={24}
              className="cursor-pointer text-gray-600 hover:text-gray-800 transition-colors"
              onClick={() => navigate(-1)}
            />
            <div className="flex items-center">
              {storeImage ? (
                <img
                  src={getImageURL(storeImage)}
                  alt={storeName}
                  className="h-8 object-contain"
                />
              ) : (
                <h1 className="text-xl font-semibold text-gray-800">{storeName}</h1>
              )}
            </div>
            <div className="w-6"></div>
          </div>
        </div>
      </header>

      {/* CATEGORIES */}
      <div className="max-w-4xl mx-auto px-6 py-6">
        <div className="flex flex-wrap gap-2 mb-8">
          <button
            onClick={() => setState({ ...state, currentCategory: "all" })}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              currentCategory === "all"
                ? "bg-gray-900 text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            {t("allCategories", { defaultValue: "Tümü" })}
          </button>

          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setState({ ...state, currentCategory: category.id })}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                currentCategory === category.id
                  ? "bg-gray-900 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {t(`category:${category.id}`, { defaultValue: category.name })}
            </button>
          ))}
        </div>

        {/* MENU ITEMS */}
        <div className="space-y-6">
          {getFilteredMenuItems().map((item, i) => {
            const {
              id,
              price,
              description,
              title,
            } = item;

            return (
              <div
                key={id}
                className="flex justify-between items-start py-4 border-b border-gray-100 last:border-b-0"
              >
                <div className="flex-1 pr-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {t(`menu_item:${id}`, { defaultValue: title })}
                  </h3>
                  
                  {description && (
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {t(`menu_description:${id}`, { defaultValue: description })}
                    </p>
                  )}
                </div>

                <div className="flex-shrink-0">
                  <span className="text-lg font-semibold text-gray-900">
                    {price} {currency}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {getFilteredMenuItems().length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              Bu kategoride ürün bulunmuyor
            </p>
          </div>
        )}
      </div>

      {/* FOOTER */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-12">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex flex-col items-center space-y-4">
            {/* Language Options */}
            <div className="flex items-center space-x-6">
              <button
                onClick={() => changeLanguage('tr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'tr' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Türkçe
              </button>
              <button
                onClick={() => changeLanguage('en')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'en' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                English
              </button>
              <button
                onClick={() => changeLanguage('ar')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ar' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                العربية
              </button>
              <button
                onClick={() => changeLanguage('ru')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ru' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Русский
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}