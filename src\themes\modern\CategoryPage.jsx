import React, { useEffect, useState } from "react";
import { IconChevronLeft } from "@tabler/icons-react";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function CategoryPage() {
  const { t, i18n } = useTranslation(['category']);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("category");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        const currency = CURRENCIES.find(
          (c) => c.cc === data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          currency: currency?.symbol || "",
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - Kategoriler - SewPOS`;
        }
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, categories } = state;
  const storeName = storeSettings?.store_name || "";

  if (isLoading) {
    return <QRMenuLoading />;
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100" style={{ fontFamily: '"Inter", "Segoe UI", system-ui, sans-serif' }}>
      {/* Modern Header */}
      <header className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white sticky top-0 z-50 shadow-xl">
        <div className="max-w-4xl mx-auto px-6 py-6 flex items-center justify-between">
          <IconChevronLeft
            size={28}
            stroke={2}
            className="cursor-pointer hover:bg-white/20 rounded-full p-1 transition-all duration-300"
            onClick={() => navigate(-1)}
          />
          <div className="flex items-center">
            {storeSettings?.store_image ? (
              <img
                src={getImageURL(storeSettings.store_image)}
                alt={storeName}
                className="h-12 object-contain"
              />
            ) : (
              <h1 className="text-2xl font-bold tracking-wide">{storeName}</h1>
            )}
          </div>
          <div className="w-7"></div>
        </div>
      </header>

      {/* Modern Categories List */}
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-blue-800 mb-4 tracking-wide">
            Kategoriler
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto"></div>
        </div>

        {/* Categories List - Alt Alta */}
        <div className="space-y-4">
          {categories.map((category) => {
            const { id, name } = category;

            return (
              <div
                key={id}
                className="group bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer border border-white/20 hover:bg-white"
                onClick={() => {
                  navigate(
                    encryptedTableId
                      ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                      : `/m/${qrcode}/menu`,
                    {
                      state: { selectedCategory: id },
                    }
                  );
                }}
              >
                <h3 className="text-2xl font-semibold text-blue-800 group-hover:text-blue-900 transition-colors text-center tracking-wide">
                  {t(`category:${id}`, { defaultValue: name })}
                </h3>
              </div>
            );
          })}

          {/* All Categories */}
          <div
            className="group bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer"
            onClick={() => {
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                  : `/m/${qrcode}/menu`,
                {
                  state: { selectedCategory: "all" },
                }
              );
            }}
          >
            <h3 className="text-2xl font-semibold text-white text-center tracking-wide">
              Tüm Kategoriler
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
}