import ApiClient from "../helpers/ApiClient";

// İptal Nedenleri
export async function getCancellationReasons() {
  try {
    const response = await ApiClient.get("/reasons/cancellation");
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function createCancellationReason(data) {
  try {
    const response = await ApiClient.post("/reasons/cancellation", data);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function updateCancellationReason(id, data) {
  try {
    const response = await ApiClient.put(`/reasons/cancellation/${id}`, data);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function deleteCancellationReason(id) {
  try {
    const response = await ApiClient.delete(`/reasons/cancellation/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// İkram Nedenleri
export async function getComplimentaryReasons() {
  try {
    const response = await ApiClient.get("/reasons/complimentary");
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function createComplimentaryReason(data) {
  try {
    const response = await ApiClient.post("/reasons/complimentary", data);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function updateComplimentaryReason(id, data) {
  try {
    const response = await ApiClient.put(`/reasons/complimentary/${id}`, data);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function deleteComplimentaryReason(id) {
  try {
    const response = await ApiClient.delete(`/reasons/complimentary/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
}

// Zayi Nedenleri
export async function getWasteReasons() {
  try {
    const response = await ApiClient.get("/reasons/waste");
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function createWasteReason(data) {
  try {
    const response = await ApiClient.post("/reasons/waste", data);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function updateWasteReason(id, data) {
  try {
    const response = await ApiClient.put(`/reasons/waste/${id}`, data);
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function deleteWasteReason(id) {
  try {
    const response = await ApiClient.delete(`/reasons/waste/${id}`);
    return response.data;
  } catch (error) {
    throw error;
  }
}
