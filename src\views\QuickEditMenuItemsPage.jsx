import React, { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import {
  IconSearch, IconRefresh, IconTrash, IconEdit,
  IconArrowUp, IconArrowDown, IconArrowsSort, IconX
} from "@tabler/icons-react";
import Page from "../components/Page";
import { useMenuItems, updateMenuItem, deleteMenuItem, changeMenuItemVisibility } from "../controllers/menu_item.controller";
import { useCategories, useTaxes } from "../controllers/settings.controller";
import { iconStroke } from "../config/config";
import { mutate } from "swr";
import Select from "react-select";
import "./QuickEditMenuItemsPage.css";

export default function QuickEditMenuItemsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [updatingItemId, setUpdatingItemId] = useState(null);
  const [deletingItemId, setDeletingItemId] = useState(null);
  const [formData, setFormData] = useState({});
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);

  // Sıralama durumu
  const [sortConfig, setSortConfig] = useState({
    key: null,
    direction: 'asc'
  });

  // Kategori filtresi
  const [categoryFilter, setCategoryFilter] = useState("");
  const [selectedCategoryOption, setSelectedCategoryOption] = useState(null);

  // Veri çekme
  const { APIURL, data: menuItems = [], error: menuError, isLoading: menuLoading } = useMenuItems();
  const { data: categories = [], error: catError, isLoading: catLoading } = useCategories();
  const { data: taxes = [], error: taxError, isLoading: taxLoading } = useTaxes();

  // Form verilerini başlangıçta yükle
  useEffect(() => {
    const initialFormData = {};
    menuItems.forEach(item => {
      initialFormData[item.id] = {
        title: item.title,
        description: item.description || "",
        price: item.price,
        netPrice: item.net_price || "",
        categoryId: item.category_id || "",
        taxId: item.tax_id || "",
        isVisibleInQRMenu: item.is_visible_in_qr_menu || 0,
        isEnabled: item.is_enabled || 0
      };
    });
    setFormData(initialFormData);
  }, [menuItems]);

  // Kategori seçeneğini başlangıçta yükle
  useEffect(() => {
    if (categoryFilter && categories.length > 0) {
      const selectedCategory = categories.find(cat => cat.id.toString() === categoryFilter);
      if (selectedCategory) {
        setSelectedCategoryOption({
          value: selectedCategory.id.toString(),
          label: selectedCategory.title
        });
      }
    }
  }, [categoryFilter, categories]);

  // Sütuna göre sıralama
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Sıralama temizleme
  const clearSort = () => {
    setSortConfig({
      key: null,
      direction: 'asc'
    });
  };

  // Filtreleme ve sıralama
  const getSortedAndFilteredItems = () => {
    // Önce filtrele
    let filteredData = menuItems.filter(item => {
      // İsim araması
      const titleMatch = item.title.toLowerCase().includes(searchQuery.toLowerCase());

      // Kategori filtresi
      const categoryMatch = !categoryFilter || (item.category_id && item.category_id.toString() === categoryFilter);

      return titleMatch && categoryMatch;
    });

    // Sonra sırala
    if (sortConfig.key) {
      filteredData.sort((a, b) => {
        let aValue, bValue;

        // Özel durumlar (kategori ve vergi isimleri gibi)
        if (sortConfig.key === 'categoryName') {
          const catA = categories.find(cat => cat.id === a.category_id);
          const catB = categories.find(cat => cat.id === b.category_id);
          aValue = catA ? catA.title : '';
          bValue = catB ? catB.title : '';
        } else if (sortConfig.key === 'taxName') {
          const taxA = taxes.find(tax => tax.id === a.tax_id);
          const taxB = taxes.find(tax => tax.id === b.tax_id);
          aValue = taxA ? taxA.title : '';
          bValue = taxB ? taxB.title : '';
        } else {
          // Normal durumlar (doğrudan öğe özellikleri)
          aValue = a[sortConfig.key] !== undefined ? a[sortConfig.key] : '';
          bValue = b[sortConfig.key] !== undefined ? b[sortConfig.key] : '';
        }

        // Sıralama yönü
        if (sortConfig.direction === 'asc') {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        } else {
          return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
        }
      });
    }

    return filteredData;
  };

  // Form değişikliklerini işle
  const handleChange = (itemId, field, value) => {
    setFormData(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [field]: value
      }
    }));
  };

  // Form değişti mi kontrolü
  const isItemChanged = (itemId) => {
    if (!formData[itemId] || !menuItems) return false;

    const item = menuItems.find(i => i.id === itemId);
    if (!item) return false;

    const form = formData[itemId];

    return (
      form.title !== item.title ||
      form.description !== (item.description || "") ||
      form.price !== item.price ||
      form.netPrice !== (item.net_price || "") ||
      form.categoryId !== (item.category_id || "") ||
      form.taxId !== (item.tax_id || "") ||
      form.isVisibleInQRMenu !== (item.is_visible_in_qr_menu || 0) ||
      form.isEnabled !== (item.is_enabled || 0)
    );
  };

  // Ürün güncelleme
  const handleUpdateItem = async (itemId) => {
    if (!formData[itemId]) return;

    setUpdatingItemId(itemId);
    try {
      const form = formData[itemId];
      const toastId = toast.loading("Ürün güncelleniyor...");

      const response = await updateMenuItem(
        itemId,
        form.title,
        form.description || null,
        form.price,
        form.netPrice || null,
        form.categoryId || null,
        form.taxId || null,
        form.isVisibleInQRMenu || 0
      );

      // API yanıtını kontrol et
      const isSuccess =
        (response && response.data && response.data.success) ||
        (response && response.success);

      toast.dismiss(toastId);

      if (isSuccess) {
        toast.success("Ürün başarıyla güncellendi!");

        if (APIURL) {
          await mutate(APIURL);
        }
      } else {
        const errorMessage =
          (response?.data?.message) ||
          (response?.message) ||
          "Güncelleme başarısız";

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Güncelleme hatası:", error);

      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Ürün güncellenirken hata oluştu!";

      toast.dismiss();
      toast.error(errorMessage);
    } finally {
      setUpdatingItemId(null);
    }
  };

  // Ürün silme dialogunu göster
  const showDeleteConfirmation = (item) => {
    setItemToDelete(item);
    setShowDeleteDialog(true);
  };

  // Ürün silme işlemi
  const handleDeleteItem = async () => {
    if (!itemToDelete) return;

    setDeletingItemId(itemToDelete.id);
    setShowDeleteDialog(false);

    try {
      const toastId = toast.loading("Ürün siliniyor...");

      const response = await deleteMenuItem(itemToDelete.id);

      // API yanıtını kontrol et
      const isSuccess =
        (response && response.data && response.data.success) ||
        (response && response.success);

      toast.dismiss(toastId);

      if (isSuccess) {
        toast.success("Ürün başarıyla silindi!");

        if (APIURL) {
          await mutate(APIURL);
        }
      } else {
        const errorMessage =
          (response?.data?.message) ||
          (response?.message) ||
          "Silme işlemi başarısız";

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Silme hatası:", error);

      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Ürün silinirken hata oluştu!";

      toast.dismiss();
      toast.error(errorMessage);
    } finally {
      setDeletingItemId(null);
      setItemToDelete(null);
    }
  };

  // Ürün aktif/pasif durumu değiştirme
  const handleToggleVisibility = async (itemId, currentStatus) => {
    const newStatus = currentStatus ? 0 : 1;

    try {
      const toastId = toast.loading(newStatus ? "Ürün aktif ediliyor..." : "Ürün pasif ediliyor...");

      const response = await changeMenuItemVisibility(itemId, newStatus);

      // API yanıtını kontrol et
      const isSuccess =
        (response && response.data && response.data.success) ||
        (response && response.success);

      toast.dismiss(toastId);

      if (isSuccess) {
        toast.success(newStatus ? "Ürün aktif edildi!" : "Ürün pasif edildi!");

        // Form data'yı güncelle
        setFormData(prev => ({
          ...prev,
          [itemId]: {
            ...prev[itemId],
            isEnabled: newStatus
          }
        }));

        if (APIURL) {
          await mutate(APIURL);
        }
      } else {
        const errorMessage =
          (response?.data?.message) ||
          (response?.message) ||
          "Durum değiştirme başarısız";

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Durum değiştirme hatası:", error);

      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Durum değiştirilirken hata oluştu!";

      toast.error(errorMessage);
    }
  };

  // Manuel yenileme işlevi
  const handleRefresh = async () => {
    if (APIURL) {
      toast.loading("Veriler yenileniyor...");
      await mutate(APIURL);
      toast.dismiss();
      toast.success("Veriler yenilendi!");
    }
  };

  // Sıralama ikonunu belirle
  const getSortIcon = (key) => {
    if (sortConfig.key !== key) {
      return <IconArrowsSort size={16} stroke={iconStroke} className="ml-1 inline-block" />;
    }

    return sortConfig.direction === 'asc'
      ? <IconArrowUp size={16} stroke={iconStroke} className="ml-1 inline-block" />
      : <IconArrowDown size={16} stroke={iconStroke} className="ml-1 inline-block" />;
  };

  // Tabloya uygulanacak veriyi al
  const sortedAndFilteredItems = getSortedAndFilteredItems();

  if (menuLoading || catLoading || taxLoading) {
    return <Page>Yükleniyor...</Page>;
  }

  if (menuError || catError || taxError) {
    return <Page>Veriler yüklenirken hata oluştu!</Page>;
  }

  return (
    <Page>
      <h1 className="text-2xl font-bold mb-4">Hızlı Ürün Düzenleme</h1>

      {/* Filtre ve Arama Kısmı - Tek Satırda */}
      <div className="flex flex-wrap gap-3 mb-6 bg-base-200 p-4 rounded-lg">
        {/* Ürün Adı Arama */}
        <div className="flex-1 min-w-[200px]">
          <label className="text-sm font-medium mb-1 block">Ürün Adı</label>
          <div className="relative">
            <input
              type="text"
              placeholder="Ürün ara..."
              className="input input-bordered w-full pl-10 h-12"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" stroke={iconStroke} />
          </div>
        </div>

        {/* Kategori Filtresi */}
        <div className="flex-1 min-w-[200px]">
          <label className="text-sm font-medium mb-1 block">Kategori</label>
          <Select
            className="basic-single"
            classNamePrefix="select"
            placeholder="Kategori ara veya seç..."
            isClearable={true}
            isSearchable={true}
            value={selectedCategoryOption}
            onChange={(option) => {
              setSelectedCategoryOption(option);
              setCategoryFilter(option ? option.value : "");
            }}
            options={[
              { value: "", label: "Tüm Kategoriler" },
              ...categories.map(category => ({
                value: category.id.toString(),
                label: category.title
              }))
            ]}
            styles={{
              control: (provided, state) => ({
                ...provided,
                borderColor: state.isFocused ? '#10b981' : provided.borderColor,
                boxShadow: state.isFocused ? '0 0 0 1px #10b981' : provided.boxShadow,
                '&:hover': {
                  borderColor: state.isFocused ? '#10b981' : '#d1d5db',
                },
                borderRadius: '0.5rem',
                minHeight: '48px',
                height: '48px'
              }),
              valueContainer: (provided) => ({
                ...provided,
                height: '48px',
                padding: '0 14px'
              }),
              input: (provided) => ({
                ...provided,
                margin: '0px',
              }),
              indicatorsContainer: (provided) => ({
                ...provided,
                height: '48px',
              }),
              menu: (provided) => ({
                ...provided,
                borderRadius: '0.5rem',
                overflow: 'hidden',
                zIndex: 10
              }),
            }}
          />
        </div>

        {/* Yenile Butonu */}
        <div className="flex items-end">
          <button
            onClick={handleRefresh}
            className="btn btn-outline"
          >
            <IconRefresh stroke={iconStroke} />
            Yenile
          </button>
        </div>
      </div>

      {/* Bulunan ürün sayısı ve sıralama temizle butonu */}
      <div className="flex justify-between items-center mb-4">
        <div className="text-sm text-gray-500">
          Toplam {sortedAndFilteredItems.length} ürün bulundu
          {categoryFilter ? " (kategoriye göre filtrelenmiş)" : ""}
          {sortConfig.key ? ` (${sortConfig.key === 'categoryName' ? 'kategori' :
                                 sortConfig.key === 'taxName' ? 'vergi' :
                                 sortConfig.key === 'title' ? 'ürün adı' :
                                 sortConfig.key === 'price' ? 'fiyat' : sortConfig.key}
                             alanına göre ${sortConfig.direction === 'asc' ? 'A-Z' : 'Z-A'})` : ""}
        </div>

        {sortConfig.key && (
          <button
            onClick={clearSort}
            className="btn btn-xs btn-ghost flex items-center gap-1 text-gray-500"
          >
            <IconX size={14} stroke={iconStroke} />
            Sıralamayı temizle
          </button>
        )}
      </div>

      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th className="cursor-pointer" onClick={() => requestSort('title')}>
                Ürün Adı {getSortIcon('title')}
              </th>
              <th className="cursor-pointer" onClick={() => requestSort('categoryName')}>
                Kategori {getSortIcon('categoryName')}
              </th>
              <th className="cursor-pointer" onClick={() => requestSort('price')}>
                Fiyat {getSortIcon('price')}
              </th>
              <th>
                Açıklama
              </th>
              <th className="text-center">Durum</th>
              <th className="text-center">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {sortedAndFilteredItems.length === 0 ? (
              <tr>
                <td colSpan="6" className="text-center py-8 text-gray-500">
                  Ürün bulunamadı. Lütfen farklı filtreler deneyin.
                </td>
              </tr>
            ) : (
              sortedAndFilteredItems.map(item => {
                // Form verileri
                const itemForm = formData[item.id] || {};
                // Bu ürün güncelleniyor mu?
                const isUpdating = updatingItemId === item.id;
                // Bu ürün siliniyor mu?
                const isDeleting = deletingItemId === item.id;
                // Form verileri değişti mi?
                const isChanged = isItemChanged(item.id);

                return (
                  <tr key={item.id} className={isChanged ? "bg-blue-50" : ""}>
                    <td>
                      <textarea
                        className="textarea textarea-bordered textarea-sm w-full resize-x min-h-[32px] overflow-hidden"
                        value={itemForm.title || ""}
                        onChange={(e) => handleChange(item.id, "title", e.target.value)}
                        style={{ height: '32px' }}
                        onInput={(e) => {
                          // Otomatik yükseklik ayarlama (sadece satır sayısına göre)
                          e.target.style.height = '32px';
                          const scrollHeight = e.target.scrollHeight;
                          if (scrollHeight > 32) {
                            e.target.style.height = scrollHeight + 'px';
                          }
                        }}
                      />
                    </td>
                    <td>
                      <Select
                        className="basic-single"
                        classNamePrefix="select"
                        placeholder="Kategori ara veya seç..."
                        isClearable={true}
                        isSearchable={true}
                        value={categories
                          .filter(cat => cat.id.toString() === itemForm.categoryId?.toString())
                          .map(cat => ({ value: cat.id.toString(), label: cat.title }))[0] || null
                        }
                        onChange={(option) => handleChange(item.id, "categoryId", option ? option.value : "")}
                        options={[
                          { value: "", label: "Kategori Seçin" },
                          ...categories.map(cat => ({
                            value: cat.id.toString(),
                            label: cat.title
                          }))
                        ]}
                        styles={{
                          control: (provided) => ({
                            ...provided,
                            minHeight: '32px',
                            height: '32px'
                          }),
                          valueContainer: (provided) => ({
                            ...provided,
                            height: '32px',
                            padding: '0 6px'
                          }),
                          input: (provided) => ({
                            ...provided,
                            margin: '0px',
                          }),
                          indicatorsContainer: (provided) => ({
                            ...provided,
                            height: '32px',
                          }),
                        }}
                      />
                    </td>
                    <td>
                      <input
                        type="number"
                        className="input input-bordered input-sm w-full"
                        value={itemForm.price || ""}
                        onChange={(e) => handleChange(item.id, "price", parseFloat(e.target.value))}
                      />
                    </td>
                    <td>
                      <textarea
                        className="textarea textarea-bordered textarea-sm w-full resize-x min-h-[32px] overflow-hidden"
                        value={itemForm.description || ""}
                        onChange={(e) => handleChange(item.id, "description", e.target.value)}
                        style={{ height: '32px' }}
                        onInput={(e) => {
                          // Otomatik yükseklik ayarlama (sadece satır sayısına göre)
                          e.target.style.height = '32px';
                          const scrollHeight = e.target.scrollHeight;
                          if (scrollHeight > 32) {
                            e.target.style.height = scrollHeight + 'px';
                          }
                        }}
                      />
                    </td>
                    <td className="text-center">
                      <div className="flex justify-center">
                        <label className="swap">
                          <input
                            type="checkbox"
                            checked={item.is_enabled === 1}
                            onChange={() => handleToggleVisibility(item.id, item.is_enabled === 1)}
                          />
                          <div className={`swap-on w-12 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${item.is_enabled === 1 ? 'bg-green-500' : 'bg-gray-300'}`}>
                            {item.is_enabled === 1 ? 'ON' : 'OFF'}
                          </div>
                          <div className={`swap-off w-12 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${item.is_enabled === 1 ? 'bg-green-500' : 'bg-gray-300'}`}>
                            {item.is_enabled === 1 ? 'ON' : 'OFF'}
                          </div>
                        </label>
                      </div>
                    </td>
                    <td>
                      <div className="flex gap-2 justify-end">
                        <button
                          onClick={() => handleUpdateItem(item.id)}
                          disabled={isUpdating || !isChanged}
                          className={`btn btn-sm ${isChanged ? 'bg-emerald-500 text-white hover:bg-emerald-600' : 'btn-disabled'}`}
                        >
                          {isUpdating ? (
                            <span className="loading loading-spinner loading-xs"></span>
                          ) : (
                            <>
                              <IconEdit size={16} stroke={iconStroke} />
                              <span className="hidden sm:inline">Güncelle</span>
                            </>
                          )}
                        </button>

                        <button
                          onClick={() => showDeleteConfirmation(item)}
                          disabled={isDeleting}
                          className="btn btn-sm bg-rose-500 text-white hover:bg-rose-600"
                        >
                          {isDeleting ? (
                            <span className="loading loading-spinner loading-xs"></span>
                          ) : (
                            <>
                              <IconTrash size={16} stroke={iconStroke} />
                              <span className="hidden sm:inline">Sil</span>
                            </>
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Silme Onay Dialogu */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-bold mb-4">Ürün Silme Onayı</h3>
            <p className="mb-6">
              <strong>{itemToDelete?.title}</strong> adlı ürünü silmek istediğinize emin misiniz? Bu işlem geri alınamaz.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowDeleteDialog(false)}
                className="btn btn-outline"
              >
                İptal
              </button>
              <button
                onClick={handleDeleteItem}
                className="btn bg-rose-500 text-white hover:bg-rose-600"
              >
                Ürünü Sil
              </button>
            </div>
          </div>
        </div>
      )}
    </Page>
  );
}