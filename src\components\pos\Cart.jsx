import React, { useState } from 'react';
import { IconSearch, IconArmchair2, IconChefHat, IconCash, IconBasket, IconX } from "@tabler/icons-react";
import CartItem from './CartItem';

// Masa ID'sine göre masa adını bulan yardımcı fonksiyon
const getTableName = (tableId, storeTables = []) => {
  if (!tableId) {
    return "Seçili Masa";
  }

  // storeTables boş veya tanımsızsa, sadece ID'yi göster
  if (!storeTables || storeTables.length === 0) {
    return `Masa ${tableId}`;
  }

  // storeTables içinde masa ID'sine göre masayı bul
  const selectedTable = storeTables.find(table =>
    String(table.id) === String(tableId)
  );

  // Masa bulunduysa adını döndür, bulunamadıysa ID ile birlikte varsayılan bir değer döndür
  return selectedTable ? selectedTable.table_title : `Masa ${tableId}`;
};

const Cart = ({
  customerType,
  customer,
  orderType,
  selectedTableId,
  cartItems,
  tableRef,
  currency,
  storeTables, // storeTables prop'unu ekledik
  enabledOrderTypes,
  onCustomerSearch,
  onOrderTypeChange,
  onTableSelect,
  onQuantityDecrease,
  onQuantityIncrease,
  onAddNote,
  onRemoveItem,
  onSendToKitchen,
  onPayAndSendToKitchen,
  onPriceChange
}) => {
  const [isCartOpen, setIsCartOpen] = useState(false);

  const toggleCart = () => {
    setIsCartOpen(!isCartOpen);
  };


  const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);


  return (
    <div className={`fixed md:relative md:border md:border-restro-border-green-light md:h-[calc(100vh-140px)] md:w-[550px] ${isCartOpen ? 'w-full h-full bottom-0 left-0 right-0 top-0 z-50' : 'w-0'} transition-all duration-300 ease-in-out`}>
      {/* Mobil Sepet Açma/Kapama Butonu */}
      <button
        onClick={toggleCart}
        className="fixed bottom-4 right-4 md:hidden z-50 p-3 bg-restro-green text-white rounded-full shadow-lg hover:bg-restro-green-dark transition active:scale-95"
      >
        <IconBasket size={24} />
        {totalItems > 0 && (
          <span className="absolute -top-2 -right-0 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
            {totalItems}
          </span>
        )}
      </button>

      {/* Sepet İçeriği - Sabit yükseklik ve flex yapısı */}
      <div className={`md:h-[calc(100vh-140px)] h-full bg-white flex flex-col ${isCartOpen ? 'block' : 'hidden md:flex'}`}>
        {/* Header - Sabit yükseklik */}
        <div className='w-full px-4 py-4 bg-white border-b z-30'>
          {/* Mobil Görünümde Kapatma Butonu */}
          <div className="flex justify-between items-center mb-3 md:hidden">
            <h2 className="font-bold text-lg">Sepet</h2>
            <button
              onClick={toggleCart}
              className="rounded-full w-8 h-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-700"
            >
              <IconX size={18} stroke={1.8} />
            </button>
          </div>

          {/* Customer Search */}
          <div onClick={onCustomerSearch} className="flex items-center gap-2">
            <input
              value={customerType === "WALKIN" ? "Kayıtsız Müşteri" : `${customer.name}`}
              type="text"
              placeholder='Search Customer'
              className='cursor-pointer text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light'
            />
            <button className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 flex items-center justify-center w-9 h-9">
              <IconSearch size={18} stroke={1.8} />
            </button>
          </div>

          {/* Table Selection */}
          {orderType === 'dinein' && (
            <div className='mt-3 relative'>
              <input type="hidden" ref={tableRef} />
              <div
                onClick={() => {
                  onTableSelect(null);
                  if (tableRef.current) {
                    tableRef.current.value = "";
                  }
                }}
                className={`text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light cursor-pointer flex justify-between items-center ${
                  selectedTableId ? 'hover:bg-gray-100 transition' : ''
                }`}
              >
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-700">
                    {selectedTableId ? `Masa: ${getTableName(selectedTableId, storeTables)}` : 'Masa Seçiniz'}
                  </span>
                  {selectedTableId && (
                    <button
                      className="text-xs text-white bg-blue-500 hover:bg-blue-600 px-2 py-1 rounded-md shadow-sm transition"
                      onClick={(e) => {
                        e.stopPropagation();
                        onTableSelect(null);
                        if (tableRef.current) {
                          tableRef.current.value = "";
                        }
                      }}
                    >
                      Değiştir
                    </button>
                  )}
                </div>
                <IconArmchair2 size={18} stroke={1.8} className="text-gray-500" />
              </div>
            </div>
          )}
        </div>

        {/* Cart Items - Scroll edilebilir alan */}
        <div className='flex-1 overflow-y-auto max-h-[calc(100vh-300px)] md:max-h-[calc(100vh-300px)]'>
          <div className='flex flex-col gap-4 px-4 py-4'>
            {cartItems?.length > 0 ? (
              cartItems.map((item, index) => (
                <CartItem
                  key={index}
                  item={item}
                  index={index}
                  currency={currency}
                  onQuantityDecrease={onQuantityDecrease}
                  onQuantityIncrease={onQuantityIncrease}
                  onAddNote={onAddNote}
                  onRemove={onRemoveItem}
                  onPriceChange={onPriceChange}
                />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <IconBasket size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Sepetiniz boş</p>
                <p className="text-sm mt-2">Ürün eklemek için ürünlere tıklayın</p>
              </div>
            )}
          </div>
        </div>

        {/* Actions - Sabit yükseklik */}
        <div className='w-full py-4 px-4 bg-white border-t z-40 shadow-lg'>
          <div className="flex items-center flex-col lg:flex-row gap-2">
            <button
              onClick={onSendToKitchen}
              className="font-medium rounded-lg border bg-green-600 hover:bg-green-700 transition active:scale-95 hover:shadow-lg text-white px-4 py-2 flex-1 flex justify-center items-center gap-1"
            >
              <div><IconChefHat size={18} stroke={1.8} /></div>
              <p>SİPARİŞİ GÖNDER</p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;