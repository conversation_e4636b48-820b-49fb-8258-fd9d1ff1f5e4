/**
 * <PERSON><PERSON>h formatını düzenleyen yardımcı fonksiyon
 * @param {string|Date} dateString - Formatlanacak tarih
 * @param {boolean} includeTime - Saat bilgisini dahil etme durumu
 * @returns {string} Formatlanmış tarih
 */
export function formatDate(dateString, includeTime = true) {
  if (!dateString) return "-";
  
  const date = new Date(dateString);
  
  // Geçerli bir tarih değilse
  if (isNaN(date.getTime())) return "-";
  
  // Gün, ay, yıl
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  // Saat, dakika
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  // Formatlanmış tarih
  return includeTime 
    ? `${day}.${month}.${year} ${hours}:${minutes}` 
    : `${day}.${month}.${year}`;
}
