export function getLockStateKey(tenantId) {
    return `SEWPOS__LOCK_STATE_${tenantId}`;
  }
  
  export function saveLockStateInLocalStorage(lockState, tenantId) {
    if (!tenantId) return;
    localStorage.setItem(getLockStateKey(tenantId), JSON.stringify(lockState));
  }
  
  export function getLockStateFromLocalStorage(tenantId) {
    if (!tenantId) return { isLocked: false, lockTime: null };
    const lockStateStr = localStorage.getItem(getLockStateKey(tenantId));
    return lockStateStr ? JSON.parse(lockStateStr) : {
      isLocked: false,
      lockTime: null
    };
  }
  
  export function clearLockStateInLocalStorage(tenantId) {
    if (!tenantId) return;
    localStorage.removeItem(getLockStateKey(tenantId));
  }
