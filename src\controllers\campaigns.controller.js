'use client';

import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

export function useCampaigns() {
  const APIURL = `/campaigns`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    campaigns: data?.campaigns,
    error,
    isLoading,
    APIURL,
  };
}

export async function addCampaign(campaign_name, description, start_date, end_date) {
  try {
    const response = await ApiClient.post("/campaigns/add", {
      campaign_name,
      description,
      start_date,
      end_date,
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteCampaign(campaignId) {
  try {
    const response = await ApiClient.delete(`/campaigns/delete/${campaignId}`);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateCampaign(campaignId, campaign_name, description, start_date, end_date) {
  try {
    const response = await ApiClient.post(`/campaigns/update/${campaignId}`, {
      campaign_name,
      description,
      start_date,
      end_date,
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function uploadCampaignPhoto(campaignId, image) {
  try {
    const formData = new FormData();
    formData.append("image", image);

    const response = await ApiClient.post(`/campaigns/upload-photo/${campaignId}`, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function removeCampaignPhoto(campaignId) {
  try {
    const response = await ApiClient.delete(`/campaigns/remove-photo/${campaignId}`);
    return response;
  } catch (error) {
    throw error;
  }
}