.chatbot-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.chatbot-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
  transition: all 0.3s ease;
}

.chatbot-button:hover {
  background-color: #059669;
  transform: scale(1.05);
}

.chatbot-panel {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 350px;
  height: 500px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.chatbot-header {
  padding: 15px;
  background-color: #10b981;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chatbot-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
}

.chatbot-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  display: flex;
  align-items: flex-start;
  max-width: 80%;
  animation: fadeIn 0.3s ease;
}

.bot-message {
  align-self: flex-start;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}

.bot-message .message-avatar {
  background-color: #e6f7f2;
  color: #10b981;
}

.user-message .message-avatar {
  background-color: #f0f4f8;
  color: #4b5563;
}

.message-content {
  background-color: #f0f4f8;
  padding: 10px 15px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.5;
}

.bot-message .message-content {
  border-top-left-radius: 4px;
  background-color: #e6f7f2;
}

.user-message .message-content {
  border-top-right-radius: 4px;
  background-color: #10b981;
  color: white;
}

/* Link stilleri */
.chatbot-link {
  color: #10b981;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 500;
  transition: color 0.2s;
}

.chatbot-link:hover {
  color: #059669;
  text-decoration: underline;
}

.chatbot-input {
  padding: 15px;
  display: flex;
  align-items: center;
  border-top: 1px solid #e5e7eb;
}

.chatbot-input textarea {
  flex: 1;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  padding: 10px 15px;
  font-size: 14px;
  resize: none;
  outline: none;
  max-height: 100px;
  transition: border-color 0.3s;
}

.chatbot-input textarea:focus {
  border-color: #10b981;
}

.chatbot-input button {
  margin-left: 10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.chatbot-input button:hover {
  background-color: #059669;
}

.chatbot-input button:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background-color: #10b981;
  border-radius: 50%;
  display: inline-block;
  margin: 0 2px;
  opacity: 0.6;
  animation: typing 1.4s infinite both;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive tasarım */
@media (max-width: 480px) {
  .chatbot-panel {
    width: calc(100vw - 40px);
    height: 60vh;
    bottom: 80px;
    right: 0;
  }
}
