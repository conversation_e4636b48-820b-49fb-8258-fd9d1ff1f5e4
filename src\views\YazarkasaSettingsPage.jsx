import React, { useState, useRef } from "react";
import { IconPlus, IconPencil, IconTrash, IconDeviceDesktop } from "@tabler/icons-react";
import { mutate } from "swr";
import Page from "../components/Page";
import { iconStroke } from "../config/config";
import { toast } from "react-hot-toast";
import {
  useYazarkasaSettings,
  useYazarkasaDevices,
  updateYazarkasaSettings,
  addYazarkasaDevice,
  updateYazarkasaDevice,
  deleteYazarkasaDevice,
} from "../controllers/settings.controller";

export default function YazarkasaSettingsPage() {
  const deviceNameRef = useRef();
  const deviceSerialRef = useRef();
  const applicationIdRef = useRef();
  const ipAddressRef = useRef();
  const portRef = useRef();

  const [editingDevice, setEditingDevice] = useState(null);
  const [isDeviceModalOpen, setIsDeviceModalOpen] = useState(false);

  // Yazarkasa ayarları
  const { data: settings, error: settingsError, isLoading: settingsLoading, APIURL: settingsAPIURL } = useYazarkasaSettings();
  
  // Yazarkasa cihazları
  const { data: devices, error: devicesError, isLoading: devicesLoading, APIURL: devicesAPIURL } = useYazarkasaDevices();

  // Entegrasyonu aktif/pasif et
  const handleToggleIntegration = async () => {
    try {
      const newStatus = !settings?.settings?.is_active;
      await updateYazarkasaSettings(newStatus);
      mutate(settingsAPIURL);
      toast.success(`Yazarkasa entegrasyonu ${newStatus ? 'aktif' : 'pasif'} edildi!`);
    } catch (error) {
      toast.error("Ayarlar güncellenirken bir hata oluştu!");
    }
  };

  // Cihaz ekleme/güncelleme
  const handleSaveDevice = async (e) => {
    e.preventDefault();
    
    const deviceData = {
      deviceName: deviceNameRef.current.value,
      deviceSerial: deviceSerialRef.current.value,
      applicationId: applicationIdRef.current.value,
      ipAddress: ipAddressRef.current.value,
      port: portRef.current.value,
    };

    try {
      if (editingDevice) {
        await updateYazarkasaDevice(editingDevice.id, {
          ...deviceData,
          isActive: editingDevice.is_active
        });
        toast.success("Cihaz başarıyla güncellendi!");
      } else {
        await addYazarkasaDevice(deviceData);
        toast.success("Cihaz başarıyla eklendi!");
      }
      
      mutate(devicesAPIURL);
      setIsDeviceModalOpen(false);
      setEditingDevice(null);
      e.target.reset();
    } catch (error) {
      toast.error(editingDevice ? "Cihaz güncellenirken bir hata oluştu!" : "Cihaz eklenirken bir hata oluştu!");
    }
  };

  // Cihaz silme
  const handleDeleteDevice = async (id) => {
    if (window.confirm("Bu cihazı silmek istediğinizden emin misiniz?")) {
      try {
        await deleteYazarkasaDevice(id);
        mutate(devicesAPIURL);
        toast.success("Cihaz başarıyla silindi!");
      } catch (error) {
        toast.error("Cihaz silinirken bir hata oluştu!");
      }
    }
  };

  // Cihaz düzenleme
  const handleEditDevice = (device) => {
    setEditingDevice(device);
    setIsDeviceModalOpen(true);
    
    // Form alanlarını doldur
    setTimeout(() => {
      if (deviceNameRef.current) deviceNameRef.current.value = device.device_name;
      if (deviceSerialRef.current) deviceSerialRef.current.value = device.device_serial;
      if (applicationIdRef.current) applicationIdRef.current.value = device.application_id;
      if (ipAddressRef.current) ipAddressRef.current.value = device.ip_address;
      if (portRef.current) portRef.current.value = device.port;
    }, 100);
  };

  // Yeni cihaz ekleme
  const handleAddDevice = () => {
    setEditingDevice(null);
    setIsDeviceModalOpen(true);
  };

  if (settingsLoading || devicesLoading) {
    return (
      <Page>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Yükleniyor...</div>
        </div>
      </Page>
    );
  }

  if (settingsError || devicesError) {
    return (
      <Page>
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">Veriler yüklenirken bir hata oluştu!</div>
        </div>
      </Page>
    );
  }

  return (
    <Page>
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <IconDeviceDesktop size={32} stroke={iconStroke} className="text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-900">Yazarkasa Entegrasyonu</h1>
        </div>

        {/* Entegrasyon Ayarları */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Entegrasyon Ayarları</h2>
          
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={settings?.settings?.is_active || false}
                onChange={handleToggleIntegration}
                className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
              />
              <span className="text-gray-700 font-medium">
                Yazarkasa Entegrasyonu {settings?.settings?.is_active ? 'Aktif' : 'Pasif'}
              </span>
            </label>
            
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              settings?.settings?.is_active 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {settings?.settings?.is_active ? 'Aktif' : 'Pasif'}
            </div>
          </div>
        </div>

        {/* Cihaz Yönetimi */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">POS Cihazları</h2>
            <button
              onClick={handleAddDevice}
              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <IconPlus size={20} stroke={iconStroke} />
              Cihaz Ekle
            </button>
          </div>

          {/* Cihaz Listesi */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {devices?.devices?.map((device) => (
              <div key={device.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{device.device_name}</h3>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleEditDevice(device)}
                      className="text-blue-600 hover:text-blue-800 p-1"
                      title="Düzenle"
                    >
                      <IconPencil size={16} stroke={iconStroke} />
                    </button>
                    <button
                      onClick={() => handleDeleteDevice(device.id)}
                      className="text-red-600 hover:text-red-800 p-1"
                      title="Sil"
                    >
                      <IconTrash size={16} stroke={iconStroke} />
                    </button>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <div><span className="font-medium">Sicil:</span> {device.device_serial}</div>
                  <div><span className="font-medium">App ID:</span> {device.application_id}</div>
                  <div><span className="font-medium">IP:</span> {device.ip_address}</div>
                  <div><span className="font-medium">Port:</span> {device.port}</div>
                </div>
                
                <div className="mt-3">
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                    device.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {device.is_active ? 'Aktif' : 'Pasif'}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {(!devices?.devices || devices.devices.length === 0) && (
            <div className="text-center py-12 text-gray-500">
              <IconDeviceDesktop size={48} stroke={1} className="mx-auto mb-4 text-gray-300" />
              <p>Henüz cihaz eklenmemiş</p>
              <p className="text-sm">Yazarkasa entegrasyonu için POS cihazı ekleyin</p>
            </div>
          )}
        </div>
      </div>

      {/* Cihaz Ekleme/Düzenleme Modal */}
      {isDeviceModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {editingDevice ? 'Cihaz Düzenle' : 'Yeni Cihaz Ekle'}
            </h3>
            
            <form onSubmit={handleSaveDevice} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cihaz Adı
                </label>
                <input
                  ref={deviceNameRef}
                  type="text"
                  required
                  maxLength={100}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Kasa 1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sicil Numarası
                </label>
                <input
                  ref={deviceSerialRef}
                  type="text"
                  required
                  maxLength={50}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="ABC123456"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Uygulama ID
                </label>
                <input
                  ref={applicationIdRef}
                  type="text"
                  required
                  maxLength={100}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="APP001"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  IP Adresi
                </label>
                <input
                  ref={ipAddressRef}
                  type="text"
                  required
                  maxLength={15}
                  pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="*************"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Port
                </label>
                <input
                  ref={portRef}
                  type="text"
                  required
                  maxLength={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="8080"
                />
              </div>
              
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setIsDeviceModalOpen(false);
                    setEditingDevice(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingDevice ? 'Güncelle' : 'Ekle'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Page>
  );
}
