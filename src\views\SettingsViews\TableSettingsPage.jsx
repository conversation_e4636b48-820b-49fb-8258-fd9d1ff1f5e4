import React, { useRef } from "react";
import Page from "../../components/Page";
import {
  IconPencil,
  IconPlus,
  IconTrash,
  IconArmchair2,
  IconQrcode,
  IconFileDownload,
} from "@tabler/icons-react";
import jsPDF from "jspdf";
import 'jspdf-autotable';
import { iconStroke } from "../../config/config";
import { addNewStoreTable, bulkAddStoreTables, deleteTable, updateStoreTable, useStoreSettings, useStoreTables } from "../../controllers/settings.controller";
import { useFloors } from "../../controllers/floors.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";
import { getTableQRMenuLink } from "../../helpers/QRMenuHelper";
import QRCode from "qrcode";
import topLogoUrl from "../../assets/Holly.png";
import bottomLogoUrl from "../../assets/google.png";

export default function TableSettingsPage() {

  const tableTitleRef = useRef();
  const tableFloorRef = useRef();
  const tableSeatingCapacityRef = useRef();

  const tableIdRef = useRef();
  const tableTitleUpdateRef = useRef();
  const tableFloorUpdateRef = useRef();
  const tableSeatingCapacityUpdateRef = useRef();

  // Toplu masa ekleme için ref'ler
  const bulkBaseTitleRef = useRef();
  const bulkCountRef = useRef();
  const bulkFloorRef = useRef();
  const bulkSeatingCapacityRef = useRef();

  const { data:storeSettings, error:errorStore, isLoading:isLoadingStore } = useStoreSettings();
  const { APIURL, data: storeTables, error, isLoading } = useStoreTables();
  const { data: floors, error: floorsError, isLoading: floorsLoading } = useFloors();

  if (isLoading || isLoadingStore || floorsLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }

  if (error || errorStore || floorsError) {
    console.error(error || errorStore || floorsError);
    return <Page className="px-8 py-6">Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!</Page>;
  }

  const { uniqueQRCode, isQRMenuEnabled } = storeSettings

  const btnDelete = async (id) => {
    const isConfirm = window.confirm("Emin misin! Bu süreç geri döndürülemez!");

    if(!isConfirm) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteTable(id);

      if(res.status == 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  async function btnAdd() {
    const title = tableTitleRef.current.value;
    const floor = tableFloorRef.current.value;
    const seatingCapacity = tableSeatingCapacityRef.current.value;

    if(!title) {
      toast.error("Please provide Başlık!");
      return;
    }
    if(!floor) {
      toast.error("Please provide Floor or use '-'");
      return;
    }
    if(!seatingCapacity) {
      toast.error("Please provide Seating Capacity or '0'");
      return;
    }

    if(seatingCapacity < 0) {
      toast.error("Please provide Valid Seating Capacity count or '0'");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await addNewStoreTable(title, floor, seatingCapacity);

      if(res.status == 200) {
        tableTitleRef.current.value = null;
        tableFloorRef.current.value = null;
        tableSeatingCapacityRef.current.value = null;

        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  }

  const btnShowUpdate = async (id, title, floor, seatingCapacity) => {
    // Kat adına göre kat ID'sini bul
    const selectedFloor = floors.find(f => f.title === floor);
    const floorId = selectedFloor ? selectedFloor.id : "";

    tableIdRef.current.value = id;
    tableTitleUpdateRef.current.value = title;
    tableFloorUpdateRef.current.value = floorId;
    tableSeatingCapacityUpdateRef.current.value = seatingCapacity;
    document.getElementById('modal-update').showModal();
  };

  const btnUpdate = async ()=>{
    const id = tableIdRef.current.value;
    const title = tableTitleUpdateRef.current.value;
    const floor = tableFloorUpdateRef.current.value;
    const seatingCapacity = tableSeatingCapacityUpdateRef.current.value;

    if(!title) {
      toast.error("Please provide Başlık!");
      return;
    }
    if(!floor) {
      toast.error("Please provide Floor or use '-'");
      return;
    }
    if(!seatingCapacity) {
      toast.error("Please provide Seating Capacity or '0'");
      return;
    }

    if(seatingCapacity < 0) {
      toast.error("Please provide Valid Seating Capacity count or '0'");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateStoreTable(id, title, floor, seatingCapacity);

      if(res.status == 200) {
        tableIdRef.current.value = null;
        tableTitleUpdateRef.current.value = null;
        tableFloorUpdateRef.current.value = null;
        tableSeatingCapacityUpdateRef.current.value = null;

        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  // Toplu masa ekleme fonksiyonu
  const btnBulkAdd = async () => {
    const baseTitle = bulkBaseTitleRef.current.value;
    const count = parseInt(bulkCountRef.current.value);
    const floor = bulkFloorRef.current.value;
    const seatingCapacity = parseInt(bulkSeatingCapacityRef.current.value);

    if(!baseTitle) {
      toast.error("Lütfen temel masa adını girin!");
      return;
    }
    if(!count || count <= 0) {
      toast.error("Lütfen geçerli bir masa sayısı girin!");
      return;
    }
    if(!floor) {
      toast.error("Lütfen bir kat seçin!");
      return;
    }
    if(isNaN(seatingCapacity) || seatingCapacity < 0) {
      toast.error("Lütfen geçerli bir oturma kapasitesi girin!");
      return;
    }

    try {
      toast.loading("Masalar ekleniyor...");
      const res = await bulkAddStoreTables(baseTitle, count, floor, seatingCapacity);

      if(res.status === 200) {
        bulkBaseTitleRef.current.value = "";
        bulkCountRef.current.value = "";
        bulkFloorRef.current.value = "";
        bulkSeatingCapacityRef.current.value = "";

        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
        document.getElementById("modal-bulk-add").close();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Toplu masa eklenirken bir hata oluştu!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  const btnDownloadTableMenuQR = async (tableId, title) => {
    try {
      if(!isQRMenuEnabled) {
        toast.error("Please enable QR menu from store settings!");
        return;
      }

      const QR_MENU_LINK = getTableQRMenuLink(uniqueQRCode, tableId)
      const qrDataURL = await QRCode.toDataURL(QR_MENU_LINK, {width: 1080});
      const link = document.createElement("a");

      const fileName = title.replace(/[^a-z0-9]/gi, '_').toLowerCase()

      link.download=`${fileName}-qr.png`;
      link.href=qrDataURL;
      link.click();
      link.remove();
    } catch (error) {
      console.error(error);
    }
  }

  // Toplu QR kodlarını PDF olarak indirme fonksiyonu
  const btnDownloadAllTableQRs = async (floorId = null) => {
    try {
      if(!isQRMenuEnabled) {
        toast.error("Lütfen mağaza ayarlarından QR menüyü etkinleştirin!");
        return;
      }
      
      if(!storeTables || storeTables.length === 0) {
        toast.error("QR kodları oluşturmak için en az bir masa olmalıdır!");
        return;
      }
      
      // Modalı kapat
      document.getElementById("modal-select-floor-for-qr").close();
      
      // Seçilen kata göre masaları filtrele
      let filteredTables = storeTables;
      let floorTitle = "Tüm Katlar";
      
      if (floorId) {
        filteredTables = storeTables.filter(table => parseInt(table.floor) === parseInt(floorId));
        const selectedFloor = floors.find(f => f.id === parseInt(floorId));
        floorTitle = selectedFloor ? selectedFloor.title : "Seçili Kat";
        
        if (filteredTables.length === 0) {
          toast.error(`${floorTitle} katında masa bulunamadı!`);
          return;
        }
      }
      
      toast.loading("QR kodları oluşturuluyor...");
      
      // PDF oluştur (A4 boyutunda)
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4"
      });
      
      // Sayfa boyutları (A4: 210mm x 297mm)
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      
      // QR kod boyutları ve yerleşim
      const qrBoxWidth = 60; // 6cm = 60mm
      const qrBoxHeight = 90; // 9cm = 90mm
      const qrCodeSize = 45; // QR kodun gerçek boyutu (mm)
      const margin = 3; // Sayfa kenar boşluğu (mm)
      
      // Türkçe karakter sorunları için bilgi metnini düzelt
      const hollyStoneInfo = "Uygulamayi indir ve kazanmaya basla!";
      
      // Her sayfaya sığacak QR kod sayısını hesapla
      const colsPerPage = Math.floor((pageWidth - (2 * margin)) / qrBoxWidth);
      const rowsPerPage = Math.floor((pageHeight - (2 * margin)) / qrBoxHeight);
      const qrPerPage = colsPerPage * rowsPerPage;
      
      // Her masa için QR kod oluştur
      for (let i = 0; i < filteredTables.length; i++) {
        // Yeni sayfa ekle (ilk sayfa hariç)
        if (i > 0 && i % qrPerPage === 0) {
          pdf.addPage();
        }
        
        const table = filteredTables[i];
        const { table_title, encrypted_id } = table;
        
        // QR kod konumunu hesapla
        const pageIndex = i % qrPerPage;
        const row = Math.floor(pageIndex / colsPerPage);
        const col = pageIndex % colsPerPage;
        
        const x = margin + (col * qrBoxWidth);
        const y = margin + (row * qrBoxHeight);
        
        // Kesim çizgilerini çiz (6x9 cm kutu)
        pdf.setDrawColor(200, 200, 200); // Açık gri
        pdf.setLineWidth(0.2);
        pdf.rect(x, y, qrBoxWidth, qrBoxHeight);
        
        // İçeriği kutunun içinde tutmak için alanlar belirle
        const contentMargin = 3; // Kutunun iç kenarlarına mesafe
        const contentWidth = qrBoxWidth - (2 * contentMargin);
        
        // QR kod oluştur
        const QR_MENU_LINK = getTableQRMenuLink(uniqueQRCode, encrypted_id);
        const qrDataURL = await QRCode.toDataURL(QR_MENU_LINK, { width: 1080 });
        
        // Boşlukları ayarla
        const qrToTableNameSpace = 5; // QR ile masa adı arası
        const tableNameHeight = 7; // Masa adı yüksekliği
        const tableNameToInfoSpace = 7; // Masa adı ile info arası
        const katInfoHeight = 5; // Kat bilgisi yüksekliği (varsa)
        
        // İçeriğin toplam yüksekliğini hesapla
        let totalHeight = qrCodeSize + qrToTableNameSpace + tableNameHeight;
        
        // Kat bilgisi varsa onu da ekle
        if (!floorId) {
          totalHeight += katInfoHeight;
        }
        
        // Bilgi metni için yükseklik ekle
        totalHeight += tableNameToInfoSpace;
        
        // İçeriği dikey olarak ortala
        const startY = y + (qrBoxHeight - totalHeight) / 2;
        
        try {
          // QR kodu yerleştir
          const qrX = x + (qrBoxWidth - qrCodeSize) / 2;
          const qrY = startY;
          pdf.addImage(qrDataURL, "PNG", qrX, qrY, qrCodeSize, qrCodeSize);
          
          // Masa adını QR kodun altına ekle
          pdf.setFontSize(14);
          pdf.setFont("helvetica", "bold");
          const tableNameY = qrY + qrCodeSize + qrToTableNameSpace;
          pdf.text(table_title, x + (qrBoxWidth / 2), tableNameY, { align: "center" });
          
          // Kat bilgisini ekle (tüm katlar seçildiğinde)
          let infoStartY = tableNameY + tableNameToInfoSpace;
          
          if (!floorId) {
            const floorName = floors.find(f => f.id === parseInt(table.floor))?.title || table.floor;
            pdf.setFontSize(9);
            pdf.setFont("helvetica", "normal");
            const katY = tableNameY + 7; // Masa adının biraz altında
            pdf.text(`Kat: ${floorName}`, x + (qrBoxWidth / 2), katY, { align: "center" });
            infoStartY += katInfoHeight;
          }
          
          // Holly Stone bilgi metnini ekle
          pdf.setFontSize(7);
          pdf.setFont("helvetica", "bold");
          
          // Metni daha iyi ortalaması için alignment özelliğini doğru ayarla
          pdf.text(hollyStoneInfo, x + (qrBoxWidth / 2), infoStartY, { 
            align: "center",
            renderingMode: "fill" // Bu özellik daha iyi ortalamasını sağlar
          });
          
        } catch (imageError) {
          console.error("Resim ekleme hatası:", imageError);
          // Resim yüklenemezse sadece metin ekle
          pdf.setFontSize(8);
          pdf.setTextColor(255, 0, 0);
          pdf.text("Resim yüklenemedi", x + (qrBoxWidth / 2), y + 10, { align: "center" });
          pdf.setTextColor(0, 0, 0);
        }
      }
      
      // PDF'i indir
      const fileName = floorId 
        ? `${floorTitle.toLowerCase().replace(/\s+/g, '-')}-masa-qr-kodlari.pdf` 
        : "tum-katlar-masa-qr-kodlari.pdf";
      
      pdf.save(fileName);
      toast.dismiss();
      toast.success(`${floorTitle} QR kodları başarıyla indirildi!`);
    } catch (error) {
      console.error(error);
      toast.dismiss();
      toast.error("QR kodları oluşturulurken bir hata oluştu!");
    }
  }
  return (
    <Page className="px-8 py-6">
      <div className="flex items-center gap-6">
        <h3 className="text-3xl font-light">Masa Yönetimi</h3>
        <button
          onClick={() => document.getElementById("modal-add").showModal()}
          className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
        >
          <IconPlus size={22} stroke={iconStroke} /> Yeni
        </button>
        <button
          onClick={() => document.getElementById("modal-bulk-add").showModal()}
          className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
        >
          <IconPlus size={22} stroke={iconStroke} /> Toplu Ekle
        </button>
        <button
          onClick={() => document.getElementById("modal-select-floor-for-qr").showModal()}
          className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
          disabled={!isQRMenuEnabled}
          title={isQRMenuEnabled ? "QR kodlarını PDF olarak indir" : "QR menü etkin değil"}
        >
          <IconFileDownload size={22} stroke={iconStroke} /> QR Kodlarını İndir
        </button>
      </div>

      <div className="mt-8 w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {storeTables.map((storeTable, index) => {
          const { id, table_title, seating_capacity, floor, encrypted_id } = storeTable;

          return (
            <div
              key={id}
              className="border px-4 py-3 rounded-2xl flex flex-col gap-4 text-sm"
            >
              <div className="flex items-center gap-2">
                <div className="w-12 h-12 rounded-full flex items-center justify-center bg-gray-100 text-gray-400">
                  <IconArmchair2 />
                </div>
                <div>
                  <p>
                    {table_title} - {floors.find(f => f.id === parseInt(floor))?.title || floor}
                  </p>
                  <p className="text-gray-400">
                    Masa Kapatisesi: {seating_capacity}
                  </p>
                </div>
                <div className="flex gap-0">
                  <button
                    onClick={() => {
                      btnShowUpdate(id, table_title, floor, seating_capacity);
                    }}
                    className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-100 transition active:scale-95"
                  >
                    <IconPencil stroke={iconStroke} />
                  </button>
                  <button
                    onClick={() => {btnDelete(id);}}
                    className="w-8 h-8 rounded-full flex items-center justify-center text-red-500 hover:bg-gray-100 transition active:scale-95"
                  >
                    <IconTrash stroke={iconStroke} />
                  </button>
                </div>
              </div>

              <button onClick={()=>{
                btnDownloadTableMenuQR(encrypted_id, table_title);
              }} className="btn btn-xs bg-gray-100 text-gray-500"><IconQrcode size={18} stroke={iconStroke} /> QR Kodunu İndir</button>
            </div>
          );
        })}
      </div>

      <dialog id="modal-add" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Masa Ekle</h3>

          <div className="mt-4">
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">Başlık</label>
            <input ref={tableTitleRef} type="text" name="title" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Masa adı " />
          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label htmlFor="floor" className="mb-1 block text-gray-500 text-sm">Kat</label>
              <select ref={tableFloorRef} name="floor" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light">
                <option value="">Kat Seçin</option>
                {floors && floors.map((floor) => (
                  <option key={floor.id} value={floor.id}>{floor.title}</option>
                ))}
              </select>
            </div>
            <div className="flex-1">
              <label htmlFor="capacity" className="mb-1 block text-gray-500 text-sm">Masa Kapasitesi</label>
              <input ref={tableSeatingCapacityRef} type="number" min={0} name="capacity" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Masa kaç kişilik" />
            </div>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
              <button onClick={()=>{btnAdd();}} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
            </form>
          </div>
        </div>
      </dialog>

      <dialog id="modal-update" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Masa Güncelle</h3>

          <div className="mt-4">
            <input type="hidden" ref={tableIdRef} />
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">Başlık</label>
            <input ref={tableTitleUpdateRef} type="text" name="title" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Masa adı" />
          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label htmlFor="floor" className="mb-1 block text-gray-500 text-sm">Kat</label>
              <select ref={tableFloorUpdateRef} name="floor" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light">
                <option value="">Kat Seçin</option>
                {floors && floors.map((floor) => (
                  <option key={floor.id} value={floor.id}>{floor.title}</option>
                ))}
              </select>
            </div>
            <div className="flex-1">
              <label htmlFor="capacity" className="mb-1 block text-gray-500 text-sm">Masa Kapasitesi</label>
              <input ref={tableSeatingCapacityUpdateRef} type="number" name="capacity" min={0} className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Masa kaç kişilik" />
            </div>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
              <button onClick={()=>{btnUpdate();}} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
            </form>
          </div>

        </div>
      </dialog>

      {/* Toplu Masa Ekleme Modalı */}
      <dialog id="modal-bulk-add" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Toplu Masa Ekle</h3>

          <div className="mt-4">
            <label htmlFor="baseTitle" className="mb-1 block text-gray-500 text-sm">Temel Masa Adı</label>
            <input ref={bulkBaseTitleRef} type="text" name="baseTitle" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Örn: Masa" />
            <p className="text-xs text-gray-500 mt-1">Masalar "Temel Ad - 1", "Temel Ad - 2" şeklinde isimlendirilecektir.</p>
          </div>

          <div className="mt-4">
            <label htmlFor="count" className="mb-1 block text-gray-500 text-sm">Masa Sayısı</label>
            <input ref={bulkCountRef} type="number" min="1" name="count" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Örn: 10" />
          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label htmlFor="floor" className="mb-1 block text-gray-500 text-sm">Kat</label>
              <select ref={bulkFloorRef} name="floor" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light">
                <option value="">Kat Seçin</option>
                {floors && floors.map((floor) => (
                  <option key={floor.id} value={floor.id}>{floor.title}</option>
                ))}
              </select>
            </div>
            <div className="flex-1">
              <label htmlFor="capacity" className="mb-1 block text-gray-500 text-sm">Masa Kapasitesi</label>
              <input ref={bulkSeatingCapacityRef} type="number" min={0} name="capacity" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Masa kaç kişilik" />
            </div>
          </div>

          <div className="modal-action">
            <form method="dialog">
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
              <button onClick={()=>{btnBulkAdd();}} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Toplu Ekle</button>
            </form>
          </div>
        </div>
      </dialog>

      {/* Kat Seçimi Modalı */}
      <dialog id="modal-select-floor-for-qr" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">QR Kodlarını İndirmek İçin Kat Seçin</h3>

          <div className="mt-4">
            <p className="text-sm text-gray-500 mb-4">Lütfen QR kodlarını indirmek istediğiniz katı seçin. Seçilen kattaki tüm masaların QR kodları PDF olarak indirilecektir.</p>

            <div className="grid grid-cols-1 gap-3">
              <button
                onClick={() => btnDownloadAllTableQRs(null)}
                className="btn btn-outline"
              >
                Tüm Katlar
              </button>

              {floors && floors.map((floor) => (
                <button
                  key={floor.id}
                  onClick={() => btnDownloadAllTableQRs(floor.id)}
                  className="btn btn-outline"
                >
                  {floor.title}
                </button>
              ))}
            </div>
          </div>

          <div className="modal-action">
            <form method="dialog">
              <button className="btn">Kapat</button>
            </form>
          </div>
        </div>
      </dialog>
    </Page>
  );
}
