import React from 'react';
import { IconX, IconCash, IconCreditCard, IconArrowUp, IconArrowDown, IconReceipt, IconCheck } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { getUserDetailsInLocalStorage } from "../../helpers/UserDetails";
import { PAYMENT_ICONS } from "../../config/payment_icons";

const LastTransactionsModal = ({ activeSession }) => {
  const user = getUserDetailsInLocalStorage();
  const currency = user?.currency || "₺";

  // Veri zaten sipariş bazında geliyor, sadece son ödeme tarihini hesaplayalım
  const processOrders = (orders) => {
    if (!orders || orders.length === 0) return [];

    return orders.map(order => ({
      ...order,
      // En son ödeme tarihini hesapla
      latest_payment_date: order.payments && order.payments.length > 0
        ? order.payments.reduce((latest, payment) =>
            new Date(payment.created_at) > new Date(latest) ? payment.created_at : latest
          , order.payments[0].created_at)
        : order.order_date,
      // Son ödeme yapan kullanıcıyı bul
      last_payment_user: order.payments && order.payments.length > 0
        ? order.payments.reduce((latest, payment) =>
            new Date(payment.created_at) > new Date(latest.created_at) ? payment : latest
          ).user_name
        : null
    })).sort((a, b) =>
      new Date(b.latest_payment_date) - new Date(a.latest_payment_date)
    );
  };

  const processedOrders = processOrders(activeSession?.lastTransactions);

  // Payment icon'unu al
  const getPaymentIcon = (iconName) => {
    // PAYMENT_ICONS zaten JSX elemanları içeriyor, direkt kullan
    const icon = PAYMENT_ICONS[iconName];
    if (icon) {
      // Mevcut ikonu klonla ve size prop'unu ekle
      return React.cloneElement(icon, { size: 16 });
    }
    // Varsayılan ikon
    return <IconCash size={16} stroke={iconStroke} />;
  };

  // Transaction type icon'unu al
  const getTransactionTypeIcon = (transactionType) => {
    switch (transactionType) {
      case 'payment':
        return <IconReceipt size={16} className="text-green-600" />;
      case 'deposit':
        return <IconArrowUp size={16} className="text-green-600" />;
      case 'withdrawal':
        return <IconArrowDown size={16} className="text-red-600" />;
      default:
        return <IconCash size={16} className="text-gray-600" />;
    }
  };

  // Tarih formatla (UTC+3 Türkiye saati)
  const formatDate = (dateString) => {
    if (!dateString) return "-";

    // UTC tarihini ayrıştır
    const date = new Date(dateString);

    // UTC+3 (Türkiye saati) için saat farkını ekle
    date.setHours(date.getHours() + 3);

    return date.toLocaleString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <dialog id="modal-last-transactions" className="modal">
      <div className="modal-box w-11/12 max-w-4xl p-0 flex flex-col max-h-[90vh]">
        {/* Sabit Başlık */}
        <div className="flex items-center justify-between p-4 border-b bg-white">
          <h3 className="font-bold text-lg">Son 3 Sipariş</h3>
          <form method="dialog">
            <button className="btn btn-sm btn-circle btn-ghost">
              <IconX size={18} stroke={iconStroke} />
            </button>
          </form>
        </div>

        {/* Scroll Edilebilir İçerik */}
        <div className="flex-1 overflow-y-auto p-4">
          {activeSession?.register_name && (
            <div className="mb-4 p-3 bg-base-200 rounded-lg">
              <h4 className="font-semibold text-sm text-gray-600">Kasa: {activeSession.register_name}</h4>
              {activeSession.register_location && (
                <p className="text-xs text-gray-500">{activeSession.register_location}</p>
              )}
            </div>
          )}

          {processedOrders && processedOrders.length > 0 ? (
            <div className="space-y-3">
              {processedOrders.map((order, index) => (
                <div key={order.id || index} className="border rounded-lg p-3 hover:bg-gray-50 transition">
                  {/* Sipariş Başlığı - Kompakt */}
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <IconReceipt size={14} className="text-blue-600" />
                      <span className="font-medium text-sm">Sipariş #{order.order_id}</span>
                      <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                        {order.table_title || "Masa Yok"}
                      </span>
                    </div>
                    <div className="text-right">
                      <span className="text-xs text-gray-600">{formatDate(order.order_date)}</span>
                    </div>
                  </div>

                  {/* Mali Durum - Tek Satır */}
                  <div className="flex items-center justify-between mb-2 p-2 bg-gray-50 rounded">
                    <div className="flex items-center gap-4 text-xs">
                      <span>Toplam: <strong>{parseFloat(order.order_total).toFixed(2)} {currency}</strong></span>
                      <span>Ödenen: <strong className="text-green-600">{parseFloat(order.total_payments).toFixed(2)} {currency}</strong></span>
                      {parseFloat(order.total_discounts || 0) > 0 && (
                        <span>İndirim: <strong className="text-orange-600">{parseFloat(order.total_discounts).toFixed(2)} {currency}</strong></span>
                      )}
                    </div>
                    <div>
                      {parseFloat(order.remaining_amount) === 0 ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <IconCheck size={10} className="mr-1" />
                          Tam Ödendi
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Kalan: {parseFloat(order.remaining_amount).toFixed(2)} {currency}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Ürünler Listesi */}
                  {order.items && order.items.length > 0 && (
                    <div className="mb-2">
                      <p className="text-xs text-gray-500 mb-1">Ürünler ({order.items.length} adet)</p>
                      <div className="grid grid-cols-1 gap-1">
                        {order.items.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex items-center justify-between text-xs bg-white p-2 rounded border">
                            <div className="flex-1">
                              <span className="font-medium">{item.item_title}</span>
                              {item.variant_title && (
                                <span className="text-gray-500 ml-1">({item.variant_title})</span>
                              )}
                              <span className="text-gray-500 ml-2">x{item.quantity}</span>
                            </div>
                            <span className="font-medium">{parseFloat(item.total_price).toFixed(2)} {currency}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Ödemeler - Kompakt */}
                  {order.payments && order.payments.length > 0 && (
                    <div>
                      <p className="text-xs text-gray-500 mb-1">Ödemeler ({order.payments.length} adet)</p>
                      <div className="space-y-1">
                        {order.payments
                          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                          .map((payment) => (
                            <div key={payment.id} className="flex items-center justify-between text-xs bg-green-50 p-2 rounded">
                              <div className="flex items-center gap-2">
                                {getPaymentIcon(payment.payment_icon)}
                                <span>{payment.payment_type}</span>
                                <span className="text-gray-500">{formatDate(payment.created_at)}</span>
                              </div>
                              <span className="font-bold text-green-600">
                                +{parseFloat(payment.amount).toFixed(2)} {currency}
                              </span>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <IconReceipt size={48} stroke={1} className="mx-auto" />
              </div>
              <p className="text-gray-500">Henüz işlem bulunmuyor</p>
            </div>
          )}
        </div>

        {/* Sabit Alt Bar */}
        <div className="border-t bg-white p-4">
          <form method="dialog" className="flex justify-end">
            <button className="btn">Kapat</button>
          </form>
        </div>
      </div>
    </dialog>
  );
};

export default LastTransactionsModal;
