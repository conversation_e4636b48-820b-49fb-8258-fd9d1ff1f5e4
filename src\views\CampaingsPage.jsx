import React, { useRef } from 'react';
import Page from "../components/Page";
import { IconPlus, IconUpload } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { toast } from "react-hot-toast";
import { mutate } from "swr";
import { getImageURL } from '../helpers/ImageHelper';
import { useCampaigns, addCampaign, deleteCampaign, updateCampaign, uploadCampaignPhoto } from "../controllers/campaigns.controller";

export default function CampaignPage() {
  const { campaigns, isLoading, error, APIURL } = useCampaigns();

  const campaignNameRef = useRef(null);
  const descriptionRef = useRef(null);
  const startDateRef = useRef(null);
  const endDateRef = useRef(null);
  const imageRef = useRef(null);

  const updateCampaignIdRef = useRef(null);
  const updateCampaignNameRef = useRef(null);
  const updateDescriptionRef = useRef(null);
  const updateStartDateRef = useRef(null);
  const updateEndDateRef = useRef(null);
  const updateImageRef = useRef(null);

  const btnAddCampaign = async () => {
    const campaign_name = campaignNameRef.current?.value;
    const description = descriptionRef.current?.value;
    const start_date = startDateRef.current?.value;
    const end_date = endDateRef.current?.value;
    const image = imageRef.current?.files[0];

    if (!campaign_name || !start_date || !end_date) {
      toast.error("Lütfen tüm zorunlu alanları doldurun!");
      return;
    }

    try {
      toast.loading("Kampanya ekleniyor...");
      const res = await addCampaign(campaign_name, description, start_date, end_date);

      if (res.status === 200 && image) {
        await uploadCampaignPhoto(res.data.campaignId, image);
      }

      await mutate(APIURL);
      toast.dismiss();
      toast.success(res.data.message);
      document.getElementById("modal-add")?.close();
    } catch (err) {
      const message = err?.response?.data?.message || "Bir hata oluştu!";
      toast.dismiss();
      toast.error(message);
    }
  };

  const btnDeleteCampaign = async (campaignId) => {
    const isConfirm = window.confirm("Bu kampanyayı silmek istediğinizden emin misiniz?");
    if (!isConfirm) return;

    try {
      toast.loading("Kampanya siliniyor...");
      const res = await deleteCampaign(campaignId);
      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (err) {
      const message = err?.response?.data?.message || "Bir hata oluştu!";
      toast.dismiss();
      toast.error(message);
    }
  };

  const btnShowUpdate = (campaign) => {
    if (updateCampaignIdRef.current) updateCampaignIdRef.current.value = campaign.id;
    if (updateCampaignNameRef.current) updateCampaignNameRef.current.value = campaign.campaign_name;
    if (updateDescriptionRef.current) updateDescriptionRef.current.value = campaign.description || "";
    if (updateStartDateRef.current) updateStartDateRef.current.value = campaign.start_date.slice(0, 10);
    if (updateEndDateRef.current) updateEndDateRef.current.value = campaign.end_date.slice(0, 10);
    document.getElementById("modal-update")?.showModal();
  };

  const btnUpdateCampaign = async () => {
    const campaignId = updateCampaignIdRef.current?.value;
    const campaign_name = updateCampaignNameRef.current?.value;
    const description = updateDescriptionRef.current?.value;
    const start_date = updateStartDateRef.current?.value;
    const end_date = updateEndDateRef.current?.value;
    const image = updateImageRef.current?.files[0];

    if (!campaignId || !campaign_name || !start_date || !end_date) {
      toast.error("Lütfen tüm zorunlu alanları doldurun!");
      return;
    }

    try {
      toast.loading("Kampanya güncelleniyor...");
      const res = await updateCampaign(campaignId, campaign_name, description, start_date, end_date);

      if (res.status === 200 && image) {
        await uploadCampaignPhoto(campaignId, image);
      }

      await mutate(APIURL);
      toast.dismiss();
      toast.success("Kampanya başarıyla güncellendi!");
      document.getElementById("modal-update")?.close();
    } catch (err) {
      const message = err?.response?.data?.message || "Bir hata oluştu!";
      toast.dismiss();
      toast.error(message);
    }
  };

  if (isLoading) {
    return <Page>Lütfen bekleyin...</Page>;
  }

  if (error) {
    return <Page>Hata: {error.message}</Page>;
  }

  return (
    <Page>
      <div className="flex flex-wrap gap-4 flex-col md:flex-row md:items-center md:justify-between">
        <div className="flex gap-6">
          <h3 className="text-2xl">Kampanyalar</h3>
          <button
            onClick={() => document.getElementById("modal-add")?.showModal()}
            className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
          >
            <IconPlus size={22} stroke={iconStroke} /> Yeni Kampanya
          </button>
        </div>
      </div>

      {/* Kampanya Listesi */}
      <div className="mt-6">
        {campaigns.length === 0 ? (
          <div className="text-center w-full h-[50vh] flex flex-col items-center justify-center text-gray-500">
            <p>Henüz kampanya bulunmuyor!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {campaigns.map((campaign) => (
              <div
                key={campaign.id}
                className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition"
              >
                {campaign.image_url && (
                  <img
                    src={getImageURL(campaign.image_url)} // getImageURL burada kullanılıyor
                    alt={campaign.campaign_name}
                    className="w-full h-32 object-cover rounded-md mb-2"
                  />
                )}
                <h4 className="text-lg font-semibold text-gray-dark">{campaign.campaign_name}</h4>
                <p className="text-sm text-gray-600 mt-1">{campaign.description || "Açıklama yok"}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Başlangıç: {new Date(campaign.start_date).toLocaleDateString("tr-TR")}
                </p>
                <p className="text-sm text-gray-500">
                  Bitiş: {new Date(campaign.end_date).toLocaleDateString("tr-TR")}
                </p>
                <div className="flex gap-2 mt-3">
                  <button
                    onClick={() => btnShowUpdate(campaign)}
                    className="text-blue-600 hover:underline text-sm"
                  >
                    Güncelle
                  </button>
                  <button
                    onClick={() => btnDeleteCampaign(campaign.id)}
                    className="text-red-600 hover:underline text-sm"
                  >
                    Sil
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Kampanya Ekleme Modal'ı */}
      <dialog id="modal-add" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Kampanya Ekle</h3>

          <div className="mt-4">
            <label className="mb-1 block text-gray-500 text-sm">
              Kampanya Adı <span className="text-xs text-gray-400">- (Zorunlu)</span>
            </label>
            <input
              ref={campaignNameRef}
              type="text"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Kampanya adını girin"
            />
          </div>

          <div className="mt-4">
            <label className="mb-1 block text-gray-500 text-sm">Açıklama</label>
            <textarea
              ref={descriptionRef}
              className="w-full block h-24 text-sm border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Kampanya açıklamasını girin"
            />
          </div>

          <div className="mt-4">
            <label className="mb-1 block text-gray-500 text-sm">Resim</label>
            <input
              ref={imageRef}
              type="file"
              accept="image/*"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50"
            />
          </div>

          <div className="flex gap-4 mt-4">
            <div className="flex-1">
              <label className="mb-1 block text-gray-500 text-sm">
                Başlangıç Tarihi <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={startDateRef}
                type="date"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              />
            </div>
            <div className="flex-1">
              <label className="mb-1 block text-gray-500 text-sm">
                Bitiş Tarihi <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={endDateRef}
                type="date"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              />
            </div>
          </div>

          <div className="modal-action">
            <button
              type="button"
              onClick={() => document.getElementById("modal-add")?.close()}
              className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500"
            >
              Kapat
            </button>
            <button
              type="button"
              onClick={btnAddCampaign}
              className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
            >
              Kaydet
            </button>
          </div>
        </div>
      </dialog>

      {/* Kampanya Güncelleme Modal'ı */}
      <dialog id="modal-update" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Kampanya Güncelle</h3>
          <input type="hidden" ref={updateCampaignIdRef} />

          <div className="mt-4">
            <label className="mb-1 block text-gray-500 text-sm">
              Kampanya Adı <span className="text-xs text-gray-400">- (Zorunlu)</span>
            </label>
            <input
              ref={updateCampaignNameRef}
              type="text"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Kampanya adını girin"
            />
          </div>

          <div className="mt-4">
            <label className="mb-1 block text-gray-500 text-sm">Açıklama</label>
            <textarea
              ref={updateDescriptionRef}
              className="w-full block h-24 text-sm border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Kampanya açıklamasını girin"
            />
          </div>

          <div className="mt-4">
            <label className="mb-1 block text-gray-500 text-sm">Resim</label>
            <input
              ref={updateImageRef}
              type="file"
              accept="image/*"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50"
            />
          </div>

          <div className="flex gap-4 mt-4">
            <div className="flex-1">
              <label className="mb-1 block text-gray-500 text-sm">
                Başlangıç Tarihi <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={updateStartDateRef}
                type="date"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              />
            </div>
            <div className="flex-1">
              <label className="mb-1 block text-gray-500 text-sm">
                Bitiş Tarihi <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={updateEndDateRef}
                type="date"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              />
            </div>
          </div>

          <div className="modal-action">
            <button
              type="button"
              onClick={() => document.getElementById("modal-update")?.close()}
              className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500"
            >
              Kapat
            </button>
            <button
              type="button"
              onClick={btnUpdateCampaign}
              className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
            >
              Güncelle
            </button>
          </div>
        </div>
      </dialog>
    </Page>
  );
}