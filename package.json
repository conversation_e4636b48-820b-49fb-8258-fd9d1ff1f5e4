{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^1.7.18", "@json2csv/plainjs": "^7.0.6", "@tabler/icons-react": "^3.31.0", "apexcharts": "^4.0.0", "axios": "^1.6.7", "browser-image-compression": "^2.0.2", "clsx": "^2.1.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "i18next": "^24.2.2", "i18next-http-backend": "^3.0.2", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^5.0.2", "moment": "^2.30.1", "pdfmake": "^0.2.18", "qrcode": "^1.5.3", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-google-multi-lang": "^1.0.7", "react-hot-toast": "^2.4.1", "react-i18next": "^15.4.1", "react-qr-barcode-scanner": "^2.1.1", "react-router-dom": "^6.21.3", "react-select": "^5.8.1", "socket.io-client": "^4.7.5", "swr": "^2.2.4", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "daisyui": "^4.6.1", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "^5.0.8", "vite-plugin-pwa": "^0.19.8"}}