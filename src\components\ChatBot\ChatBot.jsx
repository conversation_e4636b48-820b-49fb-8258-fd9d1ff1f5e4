import React, { useState, useRef, useEffect } from 'react';
import { IconSend, IconRobot, IconUser, IconX } from '@tabler/icons-react';
import { iconStroke } from '../../config/config';
import { useNavigate } from 'react-router-dom';
import './ChatBot.css';

// OpenAI API'si için gerekli fonksiyonlar (örnek)
import { sendMessageToAI, markdownToHtml } from '../../services/aiService';

const ChatBot = () => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Merhab<PERSON> ben <PERSON>lim! SewPOS sistemi hakkında sorularınızı yanıtlamak için buradayım. Nasıl yardımcı olabilirim?",
      sender: 'bot'
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Otomatik scroll
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Chatbot açıldığında input'a focus
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Markdown linklerini işlenebilir HTML'e çevirme
  const processLinks = (text) => {
    if (!text) return '';

    // Markdown linklerini işle
    const processedText = markdownToHtml(text);

    // Özel link işleme: [link text](url) formatını işle
    return processedText.replace(
      /\[([^\]]+)\]\(([^)]+)(\?action=([^)]+))?\)/g,
      (match, text, url, _, action) => {
        // Eğer action parametresi varsa, özel bir işlev ekle
        const actionAttr = action
          ? `data-action="${action}" onclick="window.openModalAfterNavigation = true;"`
          : '';

        // Dahili link mi kontrol et
        const isInternalLink = url.startsWith('/');

        if (isInternalLink) {
          return `<a href="javascript:void(0)" class="chatbot-link" data-url="${url}" ${actionAttr}>${text}</a>`;
        } else {
          return `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`;
        }
      }
    );
  };

  // Link tıklamalarını işle
  useEffect(() => {
    const handleLinkClick = (e) => {
      if (e.target.classList.contains('chatbot-link')) {
        const url = e.target.getAttribute('data-url');
        const action = e.target.getAttribute('data-action');

        if (url) {
          // Chatbot'u kapat
          setIsOpen(false);

          // Sayfaya yönlendir
          navigate(url);

          // Eğer action parametresi varsa, localStorage'a kaydet
          // (sayfa yüklendiğinde modal açılması için)
          if (action) {
            localStorage.setItem('openModal', action);
          }
        }
      }
    };

    // Olay dinleyicisini ekle
    document.addEventListener('click', handleLinkClick);

    // Temizleme fonksiyonu
    return () => {
      document.removeEventListener('click', handleLinkClick);
    };
  }, [navigate]);

  const toggleChatBot = () => {
    setIsOpen(!isOpen);
  };

  const handleInputChange = (e) => {
    setInputMessage(e.target.value);
  };

  const handleSendMessage = async () => {
    if (inputMessage.trim() === '') return;

    // Kullanıcı mesajını ekle
    const userMessage = {
      id: messages.length + 1,
      text: inputMessage,
      sender: 'user'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // AI'dan cevap al
      const response = await sendMessageToAI(inputMessage);

      // Bot mesajını ekle
      const botMessage = {
        id: messages.length + 2,
        text: response,
        sender: 'bot'
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('AI yanıt hatası:', error);

      // Hata mesajı ekle
      const errorMessage = {
        id: messages.length + 2,
        text: "Üzgünüm, şu anda yanıt veremiyorum. Lütfen daha sonra tekrar deneyin.",
        sender: 'bot'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="chatbot-container">
      {/* Chatbot Butonu */}
      <button
        className="chatbot-button"
        onClick={toggleChatBot}
      >
        {isOpen ? <IconX stroke={iconStroke} /> : <IconRobot stroke={iconStroke} />}
      </button>

      {/* Chatbot Panel */}
      {isOpen && (
        <div className="chatbot-panel">
          <div className="chatbot-header">
            <h3>Selime Sor</h3>
            <button onClick={toggleChatBot} className="close-button">
              <IconX size={18} stroke={iconStroke} />
            </button>
          </div>

          <div className="chatbot-messages">
            {messages.map(message => (
              <div
                key={message.id}
                className={`message ${message.sender === 'bot' ? 'bot-message' : 'user-message'}`}
              >
                <div className="message-avatar">
                  {message.sender === 'bot'
                    ? <IconRobot size={20} stroke={iconStroke} />
                    : <IconUser size={20} stroke={iconStroke} />
                  }
                </div>
                <div
                  className="message-content"
                  dangerouslySetInnerHTML={{
                    __html: message.sender === 'bot'
                      ? processLinks(message.text)
                      : message.text
                  }}
                />
              </div>
            ))}

            {isLoading && (
              <div className="message bot-message">
                <div className="message-avatar">
                  <IconRobot size={20} stroke={iconStroke} />
                </div>
                <div className="message-content typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          <div className="chatbot-input">
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Bir soru sorun..."
              rows={1}
            />
            <button
              onClick={handleSendMessage}
              disabled={inputMessage.trim() === '' || isLoading}
            >
              <IconSend size={20} stroke={iconStroke} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatBot;
