import React, { useState, useEffect } from 'react';
import { Tabs, Tab } from '../../components/Tabs';
import {
  getCancellationReasons,
  createCancellationReason,
  updateCancellationReason,
  deleteCancellationReason,
  getComplimentaryReasons,
  createComplimentaryReason,
  updateComplimentaryReason,
  deleteComplimentaryReason,
  getWasteReasons,
  createWasteReason,
  updateWasteReason,
  deleteWasteReason
} from '../../controllers/reasons.controller';
import { toast } from 'react-hot-toast';
import { IconEdit, IconTrash, IconPlus } from '@tabler/icons-react';

const ReasonsSettingsPage = () => {
  const [activeTab, setActiveTab] = useState('cancellation');
  const [cancellationReasons, setCancellationReasons] = useState([]);
  const [complimentaryReasons, setComplimentaryReasons] = useState([]);
  const [wasteReasons, setWasteReasons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [newReason, setNewReason] = useState('');
  const [editingReason, setEditingReason] = useState(null);

  // Tab değiştiğinde düzenleme modunu kapat
  useEffect(() => {
    setEditingReason(null);
    setNewReason('');
  }, [activeTab]);

  // Verileri yükle
  useEffect(() => {
    fetchAllReasons();
  }, []);

  const fetchAllReasons = async () => {
    setLoading(true);
    try {
      const [cancellationRes, complimentaryRes, wasteRes] = await Promise.all([
        getCancellationReasons(),
        getComplimentaryReasons(),
        getWasteReasons()
      ]);

      // API'den gelen verinin array olduğundan emin ol ve veri yapısını düzenle


      setCancellationReasons(Array.isArray(cancellationRes.data) ? cancellationRes.data.map(item => ({
        id: item.id,
        name: item.title,
        description: item.description || "",
        isActive: item.is_active === 1 || item.is_active === true
      })) : []);

      setComplimentaryReasons(Array.isArray(complimentaryRes.data) ? complimentaryRes.data.map(item => ({
        id: item.id,
        name: item.title,
        description: item.description || "",
        isActive: item.is_active === 1 || item.is_active === true
      })) : []);

      setWasteReasons(Array.isArray(wasteRes.data) ? wasteRes.data.map(item => ({
        id: item.id,
        name: item.title,
        description: item.description || "",
        isActive: item.is_active === 1 || item.is_active === true
      })) : []);
    } catch (error) {
      console.error('Nedenler yüklenirken hata oluştu:', error);
      toast.error('Nedenler yüklenirken bir hata oluştu.');
      // Hata durumunda boş array'ler ata
      setCancellationReasons([]);
      setComplimentaryReasons([]);
      setWasteReasons([]);
    } finally {
      setLoading(false);
    }
  };

  // Yeni neden ekleme
  const handleAddReason = async () => {
    if (!newReason.trim()) {
      toast.error('Lütfen bir neden girin.');
      return;
    }

    setLoading(true);
    try {
      // Backend'e gönderilecek veri yapısını düzenle
      const data = {
        title: newReason.trim(),
        description: '',
        is_active: 1
      };
      let response;

      if (activeTab === 'cancellation') {
        response = await createCancellationReason(data);
        if (response.success && response.data && response.data.id) {
          // Yeni eklenen veriyi frontend formatına dönüştür
          const newItem = {
            id: response.data.id,
            name: data.title,
            description: data.description || "",
            isActive: data.is_active === 1 || data.is_active === true
          };
          setCancellationReasons(prev => [...prev, newItem]);
        }
      } else if (activeTab === 'complimentary') {
        response = await createComplimentaryReason(data);
        if (response.success && response.data && response.data.id) {
          const newItem = {
            id: response.data.id,
            name: data.title,
            description: data.description || "",
            isActive: data.is_active === 1 || data.is_active === true
          };
          setComplimentaryReasons(prev => [...prev, newItem]);
        }
      } else if (activeTab === 'waste') {
        response = await createWasteReason(data);
        if (response.success && response.data && response.data.id) {
          const newItem = {
            id: response.data.id,
            name: data.title,
            description: data.description || "",
            isActive: data.is_active === 1 || data.is_active === true
          };
          setWasteReasons(prev => [...prev, newItem]);
        }
      }

      toast.success('Neden başarıyla eklendi.');
      setNewReason('');
    } catch (error) {
      console.error('Neden eklenirken hata oluştu:', error);
      toast.error('Neden eklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Neden düzenleme
  const handleEditReason = (reason) => {
    setEditingReason(reason);
    setNewReason(reason.name);
  };

  // Neden güncelleme
  const handleUpdateReason = async () => {
    if (!newReason.trim() || !editingReason) {
      toast.error('Lütfen bir neden girin.');
      return;
    }

    setLoading(true);
    try {
      // Backend'e gönderilecek veri yapısını düzenle
      const data = {
        title: newReason.trim(),
        description: editingReason.description || '',
        is_active: editingReason.isActive ? 1 : 0
      };
      let response;

      if (activeTab === 'cancellation') {
        response = await updateCancellationReason(editingReason.id, data);
        if (response.success) {
          // Güncellenmiş veriyi frontend formatına dönüştür
          const updatedItem = {
            ...editingReason,
            name: data.title,
            description: data.description || "",
            isActive: data.is_active === 1 || data.is_active === true
          };
          setCancellationReasons(prev => prev.map(r => r.id === editingReason.id ? updatedItem : r));
        }
      } else if (activeTab === 'complimentary') {
        response = await updateComplimentaryReason(editingReason.id, data);
        if (response.success) {
          const updatedItem = {
            ...editingReason,
            name: data.title,
            description: data.description || "",
            isActive: data.is_active === 1 || data.is_active === true
          };
          setComplimentaryReasons(prev => prev.map(r => r.id === editingReason.id ? updatedItem : r));
        }
      } else if (activeTab === 'waste') {
        response = await updateWasteReason(editingReason.id, data);
        if (response.success) {
          const updatedItem = {
            ...editingReason,
            name: data.title,
            description: data.description || "",
            isActive: data.is_active === 1 || data.is_active === true
          };
          setWasteReasons(prev => prev.map(r => r.id === editingReason.id ? updatedItem : r));
        }
      }

      toast.success('Neden başarıyla güncellendi.');
      setNewReason('');
      setEditingReason(null);
    } catch (error) {
      console.error('Neden güncellenirken hata oluştu:', error);
      toast.error('Neden güncellenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Neden silme
  const handleDeleteReason = async (id) => {
    if (!window.confirm('Bu nedeni silmek istediğinizden emin misiniz?')) {
      return;
    }

    setLoading(true);
    try {
      let response;

      if (activeTab === 'cancellation') {
        response = await deleteCancellationReason(id);
        if (response.success) {
          setCancellationReasons(prev => prev.filter(r => r.id !== id));
          toast.success('İptal nedeni başarıyla silindi.');
        }
      } else if (activeTab === 'complimentary') {
        response = await deleteComplimentaryReason(id);
        if (response.success) {
          setComplimentaryReasons(prev => prev.filter(r => r.id !== id));
          toast.success('İkram nedeni başarıyla silindi.');
        }
      } else if (activeTab === 'waste') {
        response = await deleteWasteReason(id);
        if (response.success) {
          setWasteReasons(prev => prev.filter(r => r.id !== id));
          toast.success('Fire nedeni başarıyla silindi.');
        }
      }
    } catch (error) {
      console.error('Neden silinirken hata oluştu:', error);
      // Kullanımda olduğu için silinememe durumunu kontrol et
      if (error.response && error.response.data && error.response.data.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Neden silinirken bir hata oluştu.');
      }
    } finally {
      setLoading(false);
    }
  };

  // İptal işlemini iptal et
  const handleCancelEdit = () => {
    setEditingReason(null);
    setNewReason('');
  };

  // Aktif sekmeye göre nedenleri getir - güvenli bir şekilde
  const getActiveReasons = () => {
    switch (activeTab) {
      case 'cancellation':
        return Array.isArray(cancellationReasons) ? cancellationReasons : [];
      case 'complimentary':
        return Array.isArray(complimentaryReasons) ? complimentaryReasons : [];
      case 'waste':
        return Array.isArray(wasteReasons) ? wasteReasons : [];
      default:
        return [];
    }
  };

  // Güvenli şekilde array'i kullan
  const activeReasons = getActiveReasons();
  const hasReasons = activeReasons.length > 0;

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Nedenler Yönetimi</h1>

      <Tabs activeTab={activeTab} onChange={setActiveTab}>
        <Tab id="cancellation" title="İptal Nedenleri" />
        <Tab id="complimentary" title="İkram Nedenleri" />
        <Tab id="waste" title="Zayi Nedenleri" />
      </Tabs>

      <div className="mt-6">
        <div className="flex items-center mb-4">
          <input
            type="text"
            value={newReason}
            onChange={(e) => setNewReason(e.target.value)}
            placeholder="Yeni neden ekleyin..."
            className="flex-1 border rounded-lg px-4 py-2 mr-2"
          />
          {editingReason ? (
            <>
              <button
                onClick={handleUpdateReason}
                disabled={loading}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg mr-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Güncelle
              </button>
              <button
                onClick={handleCancelEdit}
                disabled={loading}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                İptal
              </button>
            </>
          ) : (
            <button
              onClick={handleAddReason}
              disabled={loading}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <IconPlus size={18} className="mr-1" />
              Ekle
            </button>
          )}
        </div>

        {loading && <p className="text-gray-500 mb-4">Yükleniyor...</p>}

        <div className="bg-white rounded-lg shadow">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Neden
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {!hasReasons ? (
                <tr>
                  <td colSpan="2" className="px-6 py-4 text-center text-gray-500">
                    Henüz neden eklenmemiş.
                  </td>
                </tr>
              ) : (
                activeReasons.map((reason) => (
                  <tr key={reason.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{reason.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleEditReason(reason)}
                        className="text-blue-600 hover:text-blue-900 mr-3 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={loading}
                      >
                        <IconEdit size={18} />
                      </button>
                      <button
                        onClick={() => handleDeleteReason(reason.id)}
                        className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={loading}
                      >
                        <IconTrash size={18} />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ReasonsSettingsPage;