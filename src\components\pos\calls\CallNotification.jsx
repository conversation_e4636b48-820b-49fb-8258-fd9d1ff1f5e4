import React from 'react';
import { IconPhone, IconPhoneOff } from "@tabler/icons-react";

export const CallNotification = ({ call }) => {
  const { phoneNumber, callType, deviceInfo, timestamp } = call;
  
  const formattedTime = new Date(timestamp).toLocaleTimeString();

  return (
    <div className="alert alert-info shadow-lg mb-2">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2">
          {callType === 'INCOMING' ? (
            <IconPhone className="text-green-500" size={24} />
          ) : (
            <IconPhoneOff className="text-red-500" size={24} />
          )}
          <div>
            <div className="font-bold">{phoneNumber}</div>
            <div className="text-xs">
              {deviceInfo?.model || 'Bilinmeyen Cihaz'} - {formattedTime}
            </div>
          </div>
        </div>
        <div className="badge badge-sm">
          {callType === 'INCOMING' ? 'Gelen' : 'Giden'}
        </div>
      </div>
    </div>
  );
};