import React from 'react';

export const Tabs = ({ children, activeTab, onChange }) => {
  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex space-x-8">
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child, {
              active: activeTab === child.props.id,
              onClick: () => onChange(child.props.id),
            });
          }
          return child;
        })}
      </nav>
    </div>
  );
};

export const Tab = ({ id, title, active, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
        active
          ? 'border-restro-green text-restro-green'
          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
      }`}
    >
      {title}
    </button>
  );
};
