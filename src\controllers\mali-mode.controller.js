import ApiClient from "../helpers/ApiClient";

// Mali mode durumunu kontrol et
export async function getMaliModeStatus() {
  try {
    const response = await ApiClient.get("/settings/mali-mode/status");
    return response;
  } catch (error) {
    throw error;
  }
}

// Mali modu aktifleştir
export async function enableMaliMode() {
  try {
    const response = await ApiClient.post("/settings/mali-mode/enable");
    return response;
  } catch (error) {
    throw error;
  }
}

// Mali modu deaktifleştir
export async function disableMaliMode() {
  try {
    const response = await ApiClient.post("/settings/mali-mode/disable");
    return response;
  } catch (error) {
    throw error;
  }
}
