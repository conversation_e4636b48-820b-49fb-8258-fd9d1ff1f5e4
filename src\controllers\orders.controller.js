import ApiClient from "../helpers/ApiClient";

export async function getOrders() {
    try {
        const res = await ApiClient.get("/orders");
        return res;
    } catch (error) {
        throw error;
    }
}

export async function updateOrderTable(orderId, newTableId) {

    try {
        const response = await ApiClient.post(`/orders/update-order-table`, {
            orderId,
            newTableId,
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function updateOrderItemAsComplimentary(orderItemId, reasonId) {
    try {
        const response = await ApiClient.post(`/orders/update-complimentary/${orderItemId}`, {
            status: "complimentary",
            reasonId: reasonId
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function updateOrderItemAsWaste(orderItemId, reasonId) {
    try {
        const response = await ApiClient.post(`/orders/update-waste/${orderItemId}`, {
            status: "waste",
            reasonId: reasonId
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function moveOrderItemToTable(orderItemId, targetTableId) {
    try {
        const response = await ApiClient.post(`/orders/move-order-item`, {
            orderItemId,
            targetTableId
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function mergeTables(sourceTableId, targetTableId) {
    try {
        const response = await ApiClient.post(`/orders/merge-tables`, {
            sourceTableId,
            targetTableId
        });
        return response;
    } catch (error) {
        throw error;
    }
}

// services/order.js
export async function updateOrderTableStatus(tableId, status) {
    try {
      return await ApiClient.patch(`/orders/${tableId}/status`, { status });
    } catch (error) {
      console.error("API çağrısı hatası:", error);
      throw error;
    }
}


export async function savePartialPayment({ payments }) {
    try {
      const response = await ApiClient.post('/orders/save-partial-payment', { payments });
      return response;
    } catch (error) {
      console.error("API çağrısı hatası:", error);
      throw error;
    }
  }

  export async function applyDiscount(discountData) {
    try {
        const response = await ApiClient.post('/orders/apply-discount', discountData);
        return response;
    } catch (error) {
        throw error;
    }
}
export async function getOrdersInit() {
    try {
        const res = await ApiClient.get("/orders/init");
        return res;
    } catch (error) {
        throw error;
    }
}

/**
 * @param {Number} orderItemId - order item id to update status
 * @param {String} status - new status for order item
 * @param {Number} reasonId - reason id for status change (optional, required for cancel status)
 */
export async function updateKitchenOrderItemStatus(
    orderItemId,
    status,
    reasonId = null
) {
    try {
        const response = await ApiClient.post(`/orders/update-status/${orderItemId}`, {
            status,
            reasonId // İptal nedeni ID'sini de gönder
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function updateOrderItemPrice(
    orderItemId,
    newPrice
) {
    try {
        const response = await ApiClient.post(`/orders/update-price/${orderItemId}`, {
            newPrice
        });
        return response;
    } catch (error) {
        throw error;
    }
}

/**
 * @param {Array<Number>} orderIds - list of orders to cancel
 * @param {Number} reasonId - reason id for cancellation
 *  */
export async function cancelKitchenOrder(
    orderIds,
    reasonId
) {
    try {
        const response = await ApiClient.post(`/orders/cancel`, {
            orderIds,
            reasonId: reasonId
        });
        return response;
    } catch (error) {
        throw error;
    }
}

/**
 * @param {Array<Number>} orderIds - list of orders to cancel
 *  */
export async function completeKitchenOrder(
    orderIds,
) {
    try {
        const response = await ApiClient.post(`/orders/complete`, {
            orderIds
        });
        return response;
    } catch (error) {
        throw error;
    }
}

export async function getCompleteOrderPaymentSummary(orderIds) {
    try {
        const res = await ApiClient.post("/orders/complete-order-payment-summary", {
            orderIds
        });
        return res;
    } catch (error) {
        throw error;
    }
}

/**
 * @param {Array<Number>} orderIds - list of orders to cancel
 * @param {Number} subTotal
 * @param {Number} taxTotal
 * @param {Number} total
 *  */
export async function payAndCompleteKitchenOrder(
    orderIds,
    subTotal,
    taxTotal,
    total,
    selectedPaymentType,
    payments,
    customer
) {
    try {
        const response = await ApiClient.post(`/orders/complete-and-pay-order`, {
            orderIds,
            subTotal,
            taxTotal,
            total,
            selectedPaymentType,
            payments,
            customer
        });
        return response;
    } catch (error) {
        throw error;
    }
}

/**
 * @param {Array<Number>} orderIds - list of orders to cancel
 * @param {Number} subTotal
 * @param {Number} taxTotal
 * @param {Number} total
 *  */
export async function payAndCompleteCariOrder(
    orderIds,
    subTotal,
    taxTotal,
    total,
    selectedPaymentType,
    payments,
    customer
) {
    try {
        const response = await ApiClient.post(`/orders/complete-and-cari-order`, {
            orderIds,
            subTotal,
            taxTotal,
            total,
            selectedPaymentType,
            payments,
            customer
        });
        return response;
    } catch (error) {
        throw error;
    }
}
