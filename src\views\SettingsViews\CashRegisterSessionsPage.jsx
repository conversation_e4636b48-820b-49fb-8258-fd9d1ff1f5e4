import React, { useRef, useState } from "react";
import Page from "../../components/Page";
import {
  IconCalendar,
  IconCash,
  IconClock,
  IconEye,
  IconFilter,
  IconSearch,
  IconX,
} from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { 
  useCashRegisterSessions, 
  useCashRegisters 
} from "../../controllers/cash-register.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { getUserDetailsInLocalStorage } from "../../helpers/UserDetails";

export default function CashRegisterSessionsPage() {
  const [filters, setFilters] = useState({
    status: "",
    cashRegisterId: "",
    username: "",
    startDate: "",
    endDate: "",
  });

  const { data: sessions, error, isLoading, APIURL } = useCashRegisterSessions(filters);
  const { data: cashRegisters } = useCashRegisters();
  const user = getUserDetailsInLocalStorage();
  const currency = user?.currency || "₺";

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      status: "",
      cashRegisterId: "",
      username: "",
      startDate: "",
      endDate: "",
    });
  };

  // View session details
  const viewSessionDetails = (sessionId) => {
    window.location.href = `/dashboard/cash-register-session/${sessionId}`;
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "dd MMMM yyyy HH:mm", { locale: tr });
    } catch (error) {
      return dateString;
    }
  };

  if (isLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }

  if (error) {
    console.error(error);
    return <Page className="px-8 py-6">Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!</Page>;
  }

  return (
    <Page className="px-8 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Kasa Oturumları</h1>
      </div>

      {/* Filters */}
      <div className="bg-base-200 p-4 rounded-lg mb-6">
        <div className="flex items-center mb-2">
          <IconFilter size={20} stroke={iconStroke} className="mr-2" />
          <h2 className="text-lg font-semibold">Filtreler</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="form-control">
            <label className="label">
              <span className="label-text">Durum</span>
            </label>
            <select
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className="select select-bordered w-full"
            >
              <option value="">Tümü</option>
              <option value="open">Açık</option>
              <option value="closed">Kapalı</option>
            </select>
          </div>
          <div className="form-control">
            <label className="label">
              <span className="label-text">Kasa</span>
            </label>
            <select
              name="cashRegisterId"
              value={filters.cashRegisterId}
              onChange={handleFilterChange}
              className="select select-bordered w-full"
            >
              <option value="">Tümü</option>
              {cashRegisters &&
                cashRegisters.map((register) => (
                  <option key={register.id} value={register.id}>
                    {register.name}
                  </option>
                ))}
            </select>
          </div>
          <div className="form-control">
            <label className="label">
              <span className="label-text">Başlangıç Tarihi</span>
            </label>
            <input
              type="date"
              name="startDate"
              value={filters.startDate}
              onChange={handleFilterChange}
              className="input input-bordered w-full"
            />
          </div>
          <div className="form-control">
            <label className="label">
              <span className="label-text">Bitiş Tarihi</span>
            </label>
            <input
              type="date"
              name="endDate"
              value={filters.endDate}
              onChange={handleFilterChange}
              className="input input-bordered w-full"
            />
          </div>
          <div className="form-control mt-8">
            <button
              onClick={clearFilters}
              className="btn btn-outline btn-sm"
            >
              <IconX size={18} stroke={iconStroke} />
              Filtreleri Temizle
            </button>
          </div>
        </div>
      </div>

      {/* Sessions List */}
      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th>Kasa</th>
              <th>Açılış Zamanı</th>
              <th>Kapanış Zamanı</th>
              <th>Açan Kullanıcı</th>
              <th>Kapatan Kullanıcı</th>
              <th>Açılış Tutarı</th>
              <th>Kapanış Tutarı</th>
              <th>Beklenen Tutar</th>
              <th>Fark</th>
              <th>Durum</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {sessions && sessions.length > 0 ? (
              sessions.map((session) => (
                <tr key={session.id}>
                  <td>{session.register_name}</td>
                  <td>{formatDate(session.opened_at)}</td>
                  <td>{formatDate(session.closed_at)}</td>
                  <td>{session.opener_name}</td>
                  <td>{session.closer_name || "-"}</td>
                  <td>{parseFloat(session.opening_amount).toFixed(2)} {currency}</td>
                  <td>
                    {session.closing_amount
                      ? `${parseFloat(session.closing_amount).toFixed(2)} ${currency}`
                      : "-"}
                  </td>
                  <td>
                    {session.expected_amount
                      ? `${parseFloat(session.expected_amount).toFixed(2)} ${currency}`
                      : "-"}
                  </td>
                  <td>
                    {session.difference_amount ? (
                      <span
                        className={
                          parseFloat(session.difference_amount) < 0
                            ? "text-error"
                            : parseFloat(session.difference_amount) > 0
                            ? "text-success"
                            : ""
                        }
                      >
                        {parseFloat(session.difference_amount).toFixed(2)} {currency}
                      </span>
                    ) : (
                      "-"
                    )}
                  </td>
                  <td>
                    <span
                      className={`badge ${
                        session.status === "open" ? "badge-success" : "badge-info"
                      }`}
                    >
                      {session.status === "open" ? "Açık" : "Kapalı"}
                    </span>
                  </td>
                  <td>
                    <button
                      onClick={() => viewSessionDetails(session.id)}
                      className="btn btn-sm btn-outline btn-info"
                    >
                      <IconEye size={18} stroke={iconStroke} />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="11" className="text-center py-4">
                  Kasa oturumu bulunamadı.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </Page>
  );
}
