import React, { useEffect, useState, useContext } from "react";
import { SocketContext } from "../../../contexts/SocketContext";
import { useNavbarVisibility } from "../../../contexts/NavbarVisibilityContext";
import { getUserDetailsInLocalStorage } from "../../../helpers/UserDetails";
import { toast } from "react-hot-toast";
import {
  getOrdersInit,
  getOrders,
  payAndCompleteKitchenOrder,
  payAndCompleteCariOrder,
  updateOrderItemPrice,
  getCompleteOrderPaymentSummary,
  cancelKitchenOrder,
  updateKitchenOrderItemStatus,
  updateOrderTable,
  updateOrderTableStatus,
  updateOrderItemAsComplimentary,
  updateOrderItemAsWaste,
  moveOrderItemToTable,
  mergeTables
} from "../../../controllers/orders.controller";
import {
  getCancellationReasons,
  getComplimentaryReasons,
  getWasteReasons
} from "../../../controllers/reasons.controller";
import { savePrintDataRecipet } from "../../../controllers/pos.controller";
import { SCOPES } from "../../../config/scopes";
import AsyncCreatableSelect from "react-select/async-creatable";
import { searchCustomer } from '../../../controllers/customers.controller';
import { useUserActiveSession } from "../../../controllers/cash-register.controller";
import { PaymentModal } from "./PaymentModal";
import { CURRENCIES } from "../../../config/currencies.config";
import { IconCash, IconCopyPlus, IconEye, IconReceipt, IconClock, IconArmchair, IconChecks, IconX, IconLockOpen2, IconPencil, IconGift, IconTrash,  } from "@tabler/icons-react";
import { iconStroke } from "../../../config/config";
import { PAYMENT_ICONS } from "../../../config/payment_icons";
import RequireScopes from "../../RequireScopes";
import { saveSelectedFloor, getSelectedFloor } from "../../../helpers/FloorSelectionHelper";

// Sipariş tarihini formatlama fonksiyonu (UTC+3 Türkiye saati)
const formatOrderDate = (dateString) => {
  if (!dateString) return "";

  // Tarihi UTC olarak ayrıştır
  const date = new Date(dateString);

  // UTC+3 (Türkiye saati) için saat farkını ekle
  date.setHours(date.getHours() + 3);

  // Saat ve dakikayı formatla
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${hours}:${minutes}`;
};

// Sipariş 10 dakikadan eski mi kontrolü
const isOrderOlderThan10Minutes = (dateString) => {
  if (!dateString) return false;

  // Sipariş tarihini ayrıştır (UTC olarak)
  const orderDate = new Date(dateString);

  // Şu anki zamanı al (yerel saat diliminde)
  const now = new Date();

  // Şu anki UTC zamanını al
  const nowUTC = new Date(
    now.getUTCFullYear(),
    now.getUTCMonth(),
    now.getUTCDate(),
    now.getUTCHours(),
    now.getUTCMinutes(),
    now.getUTCSeconds()
  );

  // Zaman farkını dakika olarak hesapla (her iki tarih de UTC olarak)
  const diffInMinutes = (nowUTC - orderDate) / (1000 * 60);


  // 10 dakikadan fazla geçmiş mi kontrol et
  return diffInMinutes > 10;
};


const TableSelection = ({ tables, refreshOrders, onTableSelect, onFloorSelect }) => {
  const { setShowNavbar } = useNavbarVisibility();
  const { socket, isSocketConnected } = useContext(SocketContext);
  const user = getUserDetailsInLocalStorage();

  // Aktif kasa oturumu için hook
  const { mutate: mutateSession } = useUserActiveSession();

  const { role: userRole, scope, } = user;
  const userScopes = scope?.split(",");

  const [selectedFloor, setSelectedFloor] = useState(() => {
    // localStorage'dan kaydedilen floor seçimini al
    return getSelectedFloor(tables);
  });

  // Masa filtresi için state
  const [tableFilter, setTableFilter] = useState("all"); // "all", "busy", "empty"

  // Floor seçimi değiştiğinde localStorage'a kaydet
  useEffect(() => {
    if (selectedFloor) {
      saveSelectedFloor(selectedFloor);
    }
  }, [selectedFloor]);

  // Tables prop'u değiştiğinde selectedFloor'u güncelle
  useEffect(() => {
    if (tables.length > 0) {
      const savedFloor = getSelectedFloor(tables);
      // Hem number hem string karşılaştırması yap
      if (savedFloor !== selectedFloor && Number(savedFloor) !== Number(selectedFloor)) {
        console.log("Floor güncelleniyor:", savedFloor, "mevcut:", selectedFloor);
        setSelectedFloor(savedFloor);
      }
    }
  }, [tables, selectedFloor]);



  // Neden listeleri için state
  const [cancellationReasons, setCancellationReasons] = useState([]);
  const [complimentaryReasons, setComplimentaryReasons] = useState([]);
  const [wasteReasons, setWasteReasons] = useState([]);
  const [selectedReason, setSelectedReason] = useState(null);
  const [loadingReasons, setLoadingReasons] = useState(false);

  const [state, setState] = useState({
    printSettings: null,
    storeSettings: null,
    paymentTypes: [],
    isLoading: true,
    orderIds: [],
    orders: [],
    completeOrderIds: [],
    table: [],
    completeTokenIds: "",
    currency: '₺',
    summaryNetTotal: 0,
    summaryTaxTotal: 0,
    summaryTotal: 0,
    summaryOrders: [],
    order: null,
    customer: [],
    isLocked: false,
    selectedPaymentType: null,
  });

  const {
    table,
    orderIds,
    orders,
    currency,
    storeSettings,
    paymentTypes
  } = state;

  const isLocked = table?.table_status === "locked"; // Masa kilitli mi?



// MASAYI KİLİTTEN ÇIKARMA FONKSİYONU
const unlockTable = async () => {
  try {
    toast.loading("Masa kilidi kaldırılıyor...");
    const res = await updateOrderTableStatus(table.id, "busy"); // Backend'e istek at
    toast.dismiss();

    if (res.status === 200) {
      toast.success("Masa kilidi kaldırıldı!");
      setState({ ...state, isLocked: false }); // Local state güncelle
      await refreshOrders(); // Masaları tekrar yükle
      sendOrderUpdateEvent();
      document.getElementById("modal-setting").close(); // Modalı kapat
    }
  } catch (error) {
    toast.dismiss();
    const message = error?.response?.data?.message || "Masa kilidi kaldırılamadı!";
    toast.error(message);
  }
};
  const searchCustomersAsync = async (inputValue) => {
      try {
        if(inputValue) {
          const resp = await searchCustomer(inputValue);
          if(resp.status == 200) {
            console.log(resp.data.map)
            return resp.data.map((data)=>( {
              label: `${data.name} - Tel: ${data.phone} ${data.address ? ` Adres: ${data.address}` : ''}`,
              value: data.phone
            }));
          }
        }
      } catch (error) {
        console.log(error);
      }
    }

    // Masa taşıma işlemi için global state
    const [tableMovingState, setTableMovingState] = useState({
      isMoving: false,
      orderId: null,
      sourceTableTitle: '',
      targetTableId: null,
      targetTableTitle: '',
      showConfirmation: false
    });

    // Masa taşıma işlemini başlat
    const startTableMove = (orderId, tableTitle) => {
      setTableMovingState({
        isMoving: true,
        orderId: orderId,
        sourceTableTitle: tableTitle,
        targetTableId: null,
        targetTableTitle: '',
        showConfirmation: false
      });

      // Modalı kapat
      document.getElementById("modal-setting").close();

      // Kullanıcıya bilgi ver
      toast.success("Lütfen taşımak istediğiniz boş masayı seçin", {
        duration: 5000,
        icon: '🔄'
      });
    };

    // Masa taşıma işlemini iptal et
    const cancelTableMove = () => {
      setTableMovingState({
        isMoving: false,
        orderId: null,
        sourceTableTitle: '',
        targetTableId: null,
        targetTableTitle: '',
        showConfirmation: false
      });
      toast.success("Masa taşıma işlemi iptal edildi");
    };

    // Masa taşıma onay modalını göster
    const showTableMoveConfirmation = (orderId, targetTableId, targetTableTitle) => {
      setTableMovingState(prev => ({
        ...prev,
        targetTableId,
        targetTableTitle,
        showConfirmation: true
      }));
    };

    // Masa taşıma işlemini gerçekleştir
    const handleChangeTable = async (orderId, newTableId) => {
      try {
        toast.loading("Masa değiştiriliyor...");
        const res = await updateOrderTable(orderId, newTableId);

        toast.dismiss();
        if (res.status === 200) {
          toast.success(res.data.message);
          await refreshOrders();  // Masalar yeniden yükleniyor

          // Masa taşıma modunu kapat
          setTableMovingState({
            isMoving: false,
            orderId: null,
            sourceTableTitle: '',
            targetTableId: null,
            targetTableTitle: '',
            showConfirmation: false
          });
        }
      } catch (error) {
        toast.dismiss();
        const message = error?.response?.data?.message || "Masa değiştirilemedi!";
        toast.error(message);
        console.error(error);

        // Hata durumunda da masa taşıma modunu kapat
        setTableMovingState({
          isMoving: false,
          orderId: null,
          sourceTableTitle: '',
          targetTableId: null,
          targetTableTitle: '',
          showConfirmation: false
        });
      }
    };



  const setCustomer = (customer) => {

    if(customer) {
      setState({
        ...state,
        customer: {phone: customer.value, name:customer.label},
      })
    } else {
      btnClearSearchCustomer();
    }
  }

  const btnClearSearchCustomer = () => {
    setState({
      ...state,
      customer: null
    })
  }

  const btnPrintReceipt = async () => {
    try {
        toast.loading("Lütfen bekleyin...");
        const res = await getCompleteOrderPaymentSummary(table.order_ids);
        toast.dismiss();

        if (res.status == 200) {
            const { subtotal, taxTotal, total, discountTotal, totalPaid, orders: ordersArr } = res.data;

            const orders = [];
            const orderIds = table.order_ids;

            // Ürünleri hazırla
            for (const o of ordersArr) {
                const items = o.items;
                items.forEach((i) => {
                    const variant = i.variant_id
                        ? {
                            id: i.variant_id,
                            title: i.variant_title,
                            price: i.variant_price,
                        }
                        : null;

                    // **Güvenli Addon İşlemi**
                    const addons = Array.isArray(i.addons) ? i.addons.map(addon => ({
                        title: addon.title,
                        price: addon.price
                    })) : [];

                    orders.push({
                        ...i,
                        title: i.item_title,
                        price: i.price,
                        notes: i.notes,
                        variant,
                        addons
                    });
                });
            }

            const {
                customer_id,
                customer_type,
                customer_name,
                date,
                delivery_type,
                table_title,
            } = ordersArr[0];

            const floorObj = tables.find(f =>
              f.tables.some(t => t.id === table.id)
            );
            const floorId = floorObj?.floor_id || null;

            // Print verilerini hazırla
            const printData = {
                storeName: storeSettings.store_name,
                table: table_title || '',
                orderId: orderIds,
                table_id: table.id || '',
                serverName: user.name,
                floor_id: floorId,
                orderType: delivery_type,
                items: orders,
                tokenNo: '',
                customerType: customer_type,
                customer: { id: customer_id, name: customer_name },
                paymentTotal: total,
                subtotal: subtotal,
                taxTotal: taxTotal,
                discountTotal: discountTotal,
                totalPaid: totalPaid
            };

            try {
                await savePrintDataRecipet(printData);
                toast.success("Yazdırma verileri gönderildi");


            } catch (error) {
                console.error("Yazdırma hatası:", error);
                toast.error("Yazıcıya ulaşılamıyor");
            }
            document.getElementById("modal-setting").close();
            await refreshOrders();
            sendOrderUpdateEvent();
        }
    } catch (error) {
        const message =
            error?.response?.data?.message ||
            "Error processing your request, Please try later!";
        toast.dismiss();
        console.error(error);
        toast.error(message);
    }
};

const btnUpdateOrderItemPrice = async (orderItemId, newPrice) => {
  try {
    toast.loading("Fiyat güncelleniyor...");
    const res = await updateOrderItemPrice(orderItemId, newPrice);
    toast.dismiss();

    if (res.status === 200) {
      sendOrderUpdateEvent();
      await refreshOrders();
      toast.success("Fiyat başarıyla güncellendi!");
      document.getElementById("modal-order-items-view").close();
    }
  } catch (error) {
    toast.dismiss();
    const message = error?.response?.data?.message || "Fiyat güncellenemedi!";
    toast.error(message);
    console.error(error);
  }
};

const [editingOrderItem, setEditingOrderItem] = useState(null);
const [newPrice, setNewPrice] = useState("");

const handleEditPrice = (orderItem) => {
  setEditingOrderItem(orderItem);
  setNewPrice(orderItem.price.toString());
  document.getElementById("modal-edit-price").showModal();
};


const [movingOrderItem, setMovingOrderItem] = useState(null);

// Zayi işlemi için fonksiyon
const handleWasteItem = async (orderItemId) => {
  // Zayi nedenlerini yükle
  try {
    setLoadingReasons(true);
    const reasons = await getWasteReasons();
    setWasteReasons(reasons.data || []);
    setLoadingReasons(false);

    // İşlem tipini zayi olarak ayarla ve seçilen ürün ID'sini kaydet
    setState(prev => ({
      ...prev,
      reasonType: "waste",
      selectedOrderItemId: orderItemId
    }));

    // Neden seçme modalını göster
    const modalSelectReason = document.getElementById("modal-select-reason");
    if (modalSelectReason) {
      modalSelectReason.showModal();
    }
  } catch (error) {
    console.error("Zayi nedenleri yüklenirken hata oluştu:", error);
    setLoadingReasons(false);
    toast.error("Zayi nedenleri yüklenemedi!");
  }
};

// Neden seçildikten sonra zayi işlemini gerçekleştir
const completeWasteItem = async () => {
  if (!selectedReason) {
    toast.error("Lütfen bir zayi nedeni seçin!");
    return;
  }

  try {
    toast.loading("Ürün zayi işlemi yapılıyor...");
    const res = await updateOrderItemAsWaste(state.selectedOrderItemId, selectedReason);
    toast.dismiss();
    if (res.status === 200) {
      sendOrderUpdateEvent();
      await refreshOrders();
      toast.success("Ürün zayi işlemi tamamlandı!");
      document.getElementById("modal-order-items-view").close();
      // Seçili nedeni sıfırla
      setSelectedReason(null);
    }
  } catch (error) {
    toast.dismiss();
    const message = error?.response?.data?.message || "Ürün zayi işlemi yapılamadı!";
    toast.error(message);
  }
};

// Ürün taşıma işlemi için fonksiyon
const handleMoveOrderItem = (orderItem) => {
  setMovingOrderItem(orderItem);
  document.getElementById("modal-move-order-item").showModal();
};

// Ürün taşıma işlemini gerçekleştiren fonksiyon
const moveOrderItemToAnotherTable = async (targetTableId) => {
  if (!movingOrderItem || !targetTableId) return;

  try {
    toast.loading("Ürün taşınıyor...");
    const response = await moveOrderItemToTable(movingOrderItem.id, targetTableId);
    toast.dismiss();

    if (response.status === 200) {
      toast.success("Ürün başarıyla taşındı");
      setMovingOrderItem(null);
      document.getElementById("modal-move-order-item").close();
      document.getElementById("modal-order-items-view").close();
      await refreshOrders();
      sendOrderUpdateEvent();
    }
  } catch (error) {
    toast.dismiss();
    const message = error?.response?.data?.message || "Ürün taşıma işlemi başarısız oldu!";
    toast.error(message);
  }
};

// Masa birleştirme için state
const [tableMergeState, setTableMergeState] = useState({
  showConfirmation: false,
  sourceTableId: null,
  sourceTableTitle: '',
  targetTableId: null,
  targetTableTitle: '',
  isMerging: false
});

// Masa birleştirme onay modalını göster
const showTableMergeConfirmation = (sourceTableId, sourceTableTitle, targetTableId, targetTableTitle) => {
  setTableMergeState({
    showConfirmation: true,
    sourceTableId,
    sourceTableTitle,
    targetTableId,
    targetTableTitle,
    isMerging: true
  });
};

// Masa birleştirme işlemini iptal et
const cancelTableMerge = () => {
  setTableMergeState({
    showConfirmation: false,
    sourceTableId: null,
    sourceTableTitle: '',
    targetTableId: null,
    targetTableTitle: '',
    isMerging: false
  });
  toast.success("Masa birleştirme işlemi iptal edildi");
};

// Masa birleştirme işlemini gerçekleştiren fonksiyon
const handleMergeTables = async (sourceTableId, targetTableId) => {
  if (!sourceTableId || !targetTableId) return;

  try {
    toast.loading("Masalar birleştiriliyor...");
    const response = await mergeTables(sourceTableId, targetTableId);
    toast.dismiss();

    if (response.status === 200) {
      toast.success("Masalar başarıyla birleştirildi");

      // Masa birleştirme state'ini temizle
      setTableMergeState({
        showConfirmation: false,
        sourceTableId: null,
        sourceTableTitle: '',
        targetTableId: null,
        targetTableTitle: '',
        isMerging: false
      });

      await refreshOrders();
      sendOrderUpdateEvent();
    }
  } catch (error) {
    toast.dismiss();
    const message = error?.response?.data?.message || "Masa birleştirme işlemi başarısız oldu!";
    toast.error(message);

    // Hata durumunda da state'i temizle
    setTableMergeState({
      showConfirmation: false,
      sourceTableId: null,
      sourceTableTitle: '',
      targetTableId: null,
      targetTableTitle: '',
      isMerging: false
    });
  }
};

  // Modal'da gösterilecek masa bilgisi
  const [modalData, setModalData] = useState(null);

  // HIZLI ODEME MODALI
  const btnShowPayAndComplete = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await getCompleteOrderPaymentSummary(table.order_ids);
      toast.dismiss();
      document.getElementById("modal-setting").close();

      if (res.status == 200) {
        const { subtotal, taxTotal, total, orders, yazarkasaDevices } = res.data;

        const tokenNoArray = orders.map(o=>o.token_no);
        const tokens = tokenNoArray.join(",");

        setQuickPaymentState((prev) => ({
          ...prev,
          summaryNetTotal: subtotal,
          summaryTaxTotal: taxTotal,
          summaryTotal: total,
          summaryOrders: orders,
          completeOrderIds: state.orderIds,
          completeTokenIds: tokens,
          order: table,
          yazarkasaDevices: yazarkasaDevices || [],
          selectedYazarkasaDevice: null, // Reset seçimi
        }));

        document.getElementById("modal-order-summary-complete").showModal();
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  // GELMİMİŞ ODEME MODALI
  const btnShowPayAndCompleteAdvanced = async () => {
    try {
      setModalData({
        ...table,
        showDetails: true, // Masa detaylarını göstermek için bir flag
      });

      toast.loading("Lütfen bekleyin...");
      const res = await getCompleteOrderPaymentSummary(table.order_ids);
      toast.dismiss();
      const modalSetting = document.getElementById("modal-setting");
      if (modalSetting) {
        modalSetting.close();
      }

      if (res.status == 200) {
        const { subtotal, taxTotal, total, orders } = res.data;

        const tokenNoArray = orders.map(o=>o.token_no);
        const tokens = tokenNoArray.join(",");

        setQuickPaymentState((prev) => ({
          ...prev,
          summaryNetTotal: subtotal,
          summaryTaxTotal: taxTotal,
          summaryTotal: total,
          summaryOrders: orders,
          completeOrderIds: state.orderIds,
          completeTokenIds: tokens,
          order: table,
        }));

        const modalAdvanced = document.getElementById("modal-order-summary-complete-advanced");
        if (modalAdvanced) {
          modalAdvanced.showModal();
        }
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };


  // Ödeme için gereken state ve metodlar
  const [quickPaymentState, setQuickPaymentState] = useState({
    summaryNetTotal: 0,
    summaryTaxTotal: 0,
    summaryTotal: 0,
    summaryOrders: [],
    completeOrderIds: [],
    completeTokenIds: "",
    selectedPaymentType: null, // Kullanıcının seçtiği ödeme türü
    yazarkasaDevices: [], // Yazarkasa cihazları
    selectedYazarkasaDevice: null, // Seçilen yazarkasa cihazı
  });

  const [paymentList, setPaymentList] = useState([]); // Parçalı ödemeleri eklediğiniz liste


  // Kat isimlerini ayrı diziye çekme (tab butonlarında kullanmak için)
  const floorOptions = tables.map((floor) => ({
    id: floor.floor_id,
    name: floor.floor_name,
  }));

  // Seçili kattaki masaları bulma
  const filteredFloor = tables.find((floor) => floor.floor_id === selectedFloor);

  const calculateTableTotal = (orders) => {
    if (!orders || orders.length === 0) return 0;

    return orders
      .filter(order => order.payment_status !== "paid") // Ödemesi tamamlanmamış siparişler
      .reduce((total, order) => {
        // 1️⃣ Ürünlerden gelen toplam
        const itemsTotal = (order.items || [])
          .filter(item => !['cancelled', 'complimentary', 'waste'].includes(item.status)) // İptal, ikram ve zayi edilenler hariç
          .reduce((sum, item) => sum + parseFloat(item.price) * item.quantity, 0);

        // 2️⃣ İndirimler toplamı (calculated_discount kullan)
        const discountsTotal = (order.discounts || [])
          .reduce((sum, discount) => sum + parseFloat(discount.calculated_discount || 0), 0);

        // 3️⃣ Parçalı ödemeler toplamı
        const partialPaymentsTotal = (order.partialPayments || [])
          .reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);

        // 4️⃣ Ödenecek kalan miktarı hesapla (ürünler - indirimler - ödemeler)
        const remainingAmount = Math.max(0, itemsTotal - discountsTotal - partialPaymentsTotal);

        return total + remainingAmount;
      }, 0);
  };



  // Ödeme türü seçme (PaymentModal'da radio'ya tıklanınca çalışacak)
  const handlePaymentTypeSelect = (typeId) => {
    setQuickPaymentState((prev) => ({
      ...prev,
      selectedPaymentType: typeId,
    }));
  };

  // Quick pay (tüm sipariş kapama) örnek fonksiyonu
  const handleQuickPayAndComplete = async (tableId, paymentsArray) => {
    try {
      toast.loading("Lütfen bekleyin...");

      // Burada backend'e istek atıyorsunuz
      const res = await payAndCompleteKitchenOrder(
        quickPaymentState.completeOrderIds,
        quickPaymentState.summaryNetTotal,
        quickPaymentState.summaryTaxTotal,
        quickPaymentState.summaryTotal,
        quickPaymentState.selectedPaymentType,
        paymentsArray, // PaymentModal'dan gelen payments array'i
        state.customer
      );

      toast.dismiss();

      if (res.status === 200) {
        // Socket ile sipariş güncellenmesini tetikleyin
        if (isSocketConnected) {
          socket.emit("order_update_backend", {}, user.tenant_id);
        }

        // Ödeme başarılı olduğunda aktif kasa oturumunu yenile
        mutateSession();

        toast.success("Ödeme başarıyla alındı!");
        document.getElementById("modal-order-summary-complete").close();

        setQuickPaymentState((prev) => ({
          ...prev,
          selectedPaymentType: null,
        }));

        await refreshOrders();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Ödeme işlemi başarısız!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };



  const sendOrderUpdateEvent = () => {
    const user = getUserDetailsInLocalStorage();

    if (isSocketConnected) {
      socket.emit("order_update_backend", {}, user.tenant_id);
    } else {
      // Handle disconnected state (optional)
      initSocket();
      socket.emit("order_update_backend", {}, user.tenant_id);
    }
  };


  const btnShowSettingModal = (order_ids, table) => {
    setState({
      ...state,
      orderIds: order_ids,
      table: {
        ...table,
        user_name: table.orders?.length > 0 ? table.orders[0].user_name : "", // Kullanıcı adını ekle
      },
      orders: table.orders,
    });

    document.getElementById("modal-setting").showModal();
  };

  const handleComplimentaryItem = async (orderItemId) => {
    // İkram nedenlerini yükle
    try {
      setLoadingReasons(true);
      const reasons = await getComplimentaryReasons();
      setComplimentaryReasons(reasons.data || []);
      setLoadingReasons(false);

      // İşlem tipini ikram olarak ayarla ve seçilen ürün ID'sini kaydet
      setState(prev => ({
        ...prev,
        reasonType: "complimentary",
        selectedOrderItemId: orderItemId
      }));

      // Neden seçme modalını göster
      const modalSelectReason = document.getElementById("modal-select-reason");
      if (modalSelectReason) {
        modalSelectReason.showModal();
      }
    } catch (error) {
      console.error("İkram nedenleri yüklenirken hata oluştu:", error);
      setLoadingReasons(false);
      toast.error("İkram nedenleri yüklenemedi!");
    }
  };

  // Neden seçildikten sonra ikram işlemini gerçekleştir
  const completeComplimentaryItem = async () => {
    if (!selectedReason) {
      toast.error("Lütfen bir ikram nedeni seçin!");
      return;
    }

    try {
      toast.loading("Ürün ikram ediliyor...");
      const res = await updateOrderItemAsComplimentary(state.selectedOrderItemId, selectedReason);
      toast.dismiss();
      if (res.status === 200) {
        sendOrderUpdateEvent();
        await refreshOrders();
        toast.success("Ürün ikram edildi!");
        const modalItemsView = document.getElementById("modal-order-items-view");
        if (modalItemsView) {
          modalItemsView.close();
        }
        // Seçili nedeni sıfırla
        setSelectedReason(null);
      }
    } catch (error) {
      toast.dismiss();
      const message = error?.response?.data?.message || "Ürün ikram edilemedi!";
      toast.error(message);
    }
  };



  const btnShowCancelOrderModal = async () => {
    const modalSetting = document.getElementById("modal-setting");
    if (modalSetting) {
      modalSetting.close();
    }

    // İptal nedenlerini yükle
    try {
      setLoadingReasons(true);
      const reasons = await getCancellationReasons();
      setCancellationReasons(reasons.data || []);
      setLoadingReasons(false);

      // Neden seçme modalını göster
      const modalSelectReason = document.getElementById("modal-select-reason");
      if (modalSelectReason) {
        // İşlem tipini iptal olarak ayarla
        setState(prev => ({ ...prev, reasonType: "cancellation" }));
        modalSelectReason.showModal();
      }
    } catch (error) {
      console.error("İptal nedenleri yüklenirken hata oluştu:", error);
      setLoadingReasons(false);
      toast.error("İptal nedenleri yüklenemedi!");
    }
  };

  // Neden seçildikten sonra iptal modalını göster
  const showCancelOrderModalAfterReason = () => {
    const modalOrderCancel = document.getElementById("modal-order-cancel");
    if (modalOrderCancel) {
      modalOrderCancel.showModal();
    }
  };

  const btnCancelOrder = async () => {
    if (!selectedReason) {
      toast.error("Lütfen bir iptal nedeni seçin!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      // İptal nedenini de gönder
      const res = await cancelKitchenOrder(state.orderIds, selectedReason);
      toast.dismiss();
      if (res.status == 200) {
        sendOrderUpdateEvent();
        await refreshOrders();
        toast.success(res.data.message);
        const modalOrderCancel = document.getElementById("modal-order-cancel");
        if (modalOrderCancel) {
          modalOrderCancel.close();
        }
        setState({
          ...state,
          orderIds: [],
          table: [],
        });
        // Seçili nedeni sıfırla
        setSelectedReason(null);
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  // Sipariş ürünü durumunu değiştirmek için modal göster
  const showChangeOrderItemStatusModal = (orderItemId, status) => {
    // Eğer durum "cancelled" ise iptal nedeni seçme modalını göster
    if (status === "cancelled") {
      setState(prev => ({
        ...prev,
        reasonType: "cancellation",
        selectedOrderItemId: orderItemId,
        selectedOrderItemStatus: status
      }));

      // İptal nedenlerini yükle
      loadCancellationReasons();

      // Neden seçme modalını göster
      document.getElementById("modal-select-reason").showModal();
    } else {
      // Diğer durumlar için direkt değiştir
      btnChangeOrderItemStatus(orderItemId, status);
    }
  };

  // Sipariş ürünü durumunu değiştir
  const btnChangeOrderItemStatus = async (orderItemId, status, reasonId = null) => {
    try {
      toast.loading("Lütfen bekleyin...");
      // İptal nedeni ID'sini de gönder
      const res = await updateKitchenOrderItemStatus(orderItemId, status, reasonId);
      toast.dismiss();
      if (res.status == 200) {
        sendOrderUpdateEvent();
        await refreshOrders();
        toast.success(res.data.message);
        const modalOrderCancel = document.getElementById("modal-order-cancel");
        if (modalOrderCancel) {
          modalOrderCancel.close();
        }

        // İptal işlemi tamamlandıktan sonra seçili değerleri sıfırla
        if (status === "cancelled") {
          setSelectedReason(null);
        }
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };




  // Navbar gizlemek istiyorsanız (kullanılmıyorsa kaldırabilirsiniz)
  useEffect(() => {
    _init();
    setShowNavbar(false);
    return () => setShowNavbar(false);
  }, [setShowNavbar]);

  // Nedenleri yükleme fonksiyonları
  const loadCancellationReasons = async () => {
    try {
      setLoadingReasons(true);
      const reasons = await getCancellationReasons();
      setCancellationReasons(reasons.data || []);
      setLoadingReasons(false);
    } catch (error) {
      console.error("İptal nedenleri yüklenirken hata oluştu:", error);
      setLoadingReasons(false);
    }
  };

  const loadComplimentaryReasons = async () => {
    try {
      setLoadingReasons(true);
      const reasons = await getComplimentaryReasons();
      setComplimentaryReasons(reasons.data || []);
      setLoadingReasons(false);
    } catch (error) {
      console.error("İkram nedenleri yüklenirken hata oluştu:", error);
      setLoadingReasons(false);
    }
  };

  const loadWasteReasons = async () => {
    try {
      setLoadingReasons(true);
      const reasons = await getWasteReasons();
      setWasteReasons(reasons.data || []);
      setLoadingReasons(false);
    } catch (error) {
      console.error("Zayi nedenleri yüklenirken hata oluştu:", error);
      setLoadingReasons(false);
    }
  };

  const _init = async () => {
    try {
      const [ordersResponse, ordersInitResponse] = await Promise.all([
        getOrders(),
        getOrdersInit(),
      ]);

      if (ordersResponse.status === 200 && ordersInitResponse.status === 200) {
        const ordersInit = ordersInitResponse.data;
        const currency = CURRENCIES.find(c => c.cc === ordersInit?.storeSettings?.currency);

        setState({
          printSettings: ordersInit.printSettings || {},
          storeSettings: ordersInit.storeSettings || {},
          paymentTypes: ordersInit.paymentTypes || {},
          currency: currency?.symbol,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error(error);
      toast.error("Error loading orders! Please try later!");
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }

   // CariOdeme
   const btnPayAndCompleteCari = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      const customer = parseInt(state.customer.phone, 10);

      const res = await payAndCompleteCariOrder(
        quickPaymentState.completeOrderIds,
        quickPaymentState.summaryNetTotal,
        quickPaymentState.summaryTaxTotal,
        quickPaymentState.summaryTotal,
        "cari",
        paymentList,
        customer
      );
      toast.dismiss();
      if (res.status === 200) {
        sendOrderUpdateEvent();

        // Ödeme başarılı olduğunda aktif kasa oturumunu yenile
        mutateSession();

        await refreshOrders();
        toast.success(res.data.message);
        const modalComplete = document.getElementById("modal-order-summary-complete");
        if (modalComplete) {
          modalComplete.close();
        }

        const modalAdvanced = document.getElementById("modal-order-summary-complete-advanced");
        if (modalAdvanced) {
          modalAdvanced.close();
        }
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Cari ödeme işlemi başarısız!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };


  // ÖDEME AKSYNU TAMAMLAMA
  const btnPayAndComplete = async () => {

    if (!state.selectedPaymentType) {
      toast.error("Lütfen bir ödeme yöntemi seçin!");
      return; // Ödeme yöntemi seçilmediyse fonksiyonu durdur
    }

    try {
      toast.loading("Lütfen bekleyin...");

      // Seçilen yazarkasa cihazının tüm verilerini bul
      const selectedDevice = quickPaymentState.selectedYazarkasaDevice
        ? quickPaymentState.yazarkasaDevices.find(device => device.id === quickPaymentState.selectedYazarkasaDevice)
        : null;

      const paymentData = {
        orderIds: quickPaymentState.completeOrderIds,
        summaryNetTotal: quickPaymentState.summaryNetTotal,
        summaryTaxTotal: quickPaymentState.summaryTaxTotal,
        summaryTotal: quickPaymentState.summaryTotal,
        paymentType: state.selectedPaymentType,
        paymentList: paymentList,
        customer: state.customer,
        yazarkasaDevice: selectedDevice // Seçilen yazarkasa cihazının tüm verisi
      };

      const res = await payAndCompleteKitchenOrder(
        paymentData.orderIds,
        paymentData.summaryNetTotal,
        paymentData.summaryTaxTotal,
        paymentData.summaryTotal,
        paymentData.paymentType,
        paymentData.paymentList,
        paymentData.customer,
        paymentData.yazarkasaDevice
      );
      toast.dismiss();
      if (res.status == 200) {
        sendOrderUpdateEvent();

        // Ödeme başarılı olduğunda aktif kasa oturumunu yenile
        mutateSession();

        await refreshOrders();
        toast.success(res.data.message);
        const modalComplete = document.getElementById("modal-order-summary-complete");
        if (modalComplete) {
          modalComplete.close();
        }

        const modalAdvanced = document.getElementById("modal-order-summary-complete-advanced");
        if (modalAdvanced) {
          modalAdvanced.close();
        }
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  return (
    <div className="flex flex-col w-full">
      {/* Kat (Floor) Sekmeleri ve Filtreler */}
      <div className="sticky top-0 bg-white z-10">
        {/* Masa taşıma modu aktifse uyarı göster */}
        {tableMovingState.isMoving && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 p-3 mb-2 flex justify-between items-center rounded-lg">
            <div>
              <p className="font-bold">Masa Taşıma Modu Aktif</p>
              <p className="text-sm">Lütfen taşımak istediğiniz boş masayı seçin</p>
            </div>
            <button
              onClick={cancelTableMove}
              className="btn btn-sm bg-yellow-500 text-white hover:bg-yellow-600"
            >
              İptal Et
            </button>
          </div>
        )}

        {/* Masa birleştirme modu aktifse uyarı göster */}
        {tableMergeState.isMerging && !tableMergeState.showConfirmation && (
          <div className="bg-purple-100 border border-purple-400 text-purple-800 p-3 mb-2 flex justify-between items-center rounded-lg">
            <div>
              <p className="font-bold">Masa Birleştirme Modu Aktif</p>
              <p className="text-sm">Lütfen birleştirmek istediğiniz dolu masayı seçin</p>
            </div>
            <button
              onClick={cancelTableMerge}
              className="btn btn-sm bg-purple-500 text-white hover:bg-purple-600"
            >
              İptal Et
            </button>
          </div>
        )}

        <div className="flex flex-nowrap overflow-x-auto px-4 items-center py-2 space-x-2 rounded-lg">
          {floorOptions.map((floor) => {
            // Bu floor'daki dolu masa sayısını hesapla
            const floorData = tables.find(f => f.floor_id === floor.id);
            const busyTablesCount = floorData ? floorData.tables.filter(t => t.table_status === "busy" || t.table_status === "locked").length : 0;

            return (
              <button
                key={floor.id}
                onClick={() => {
                  console.log("Floor seçildi:", floor.id, "mevcut:", selectedFloor);
                  setSelectedFloor(floor.id);
                }}
                className={`px-8 py-2 rounded-md whitespace-nowrap
           font-semibold relative ${
                  selectedFloor === floor.id
                    ? "bg-restro-green text-white shadow-lg transform scale-105"
                    : "bg-white text-gray-700 border border-gray-300 hover:bg-gray-200"
                }`}
              >
                {floor.name}
                {/* Dolu masa sayısı - kırmızı kutu */}
                {busyTablesCount > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                    {busyTablesCount}
                  </span>
                )}
              </button>
            );
          })}
        </div>

        {/* Masa Filtreleri - Her katın altında */}
        {filteredFloor && (
          <div className="flex px-4 pb-2 border-b">
            <div className="flex items-center space-x-2">
              <div className="flex border rounded-md overflow-hidden">
                <button
                  onClick={() => setTableFilter("all")}
                  className={`px-4 py-2 text-sm font-medium transition-colors ${
                    tableFilter === "all"
                      ? "bg-gray-800 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  Tümü ({filteredFloor.tables.length})
                </button>

                <button
                  onClick={() => setTableFilter("busy")}
                  className={`px-4 py-2 text-sm font-medium transition-colors ${
                    tableFilter === "busy"
                      ? "bg-yellow-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <span className="inline-block w-2 h-2 rounded-full bg-yellow-500 mr-1"></span>
                  Dolu ({filteredFloor.tables.filter(t => t.table_status === "busy" || t.table_status === "locked").length})
                </button>

                <button
                  onClick={() => setTableFilter("empty")}
                  className={`px-4 py-2 text-sm font-medium transition-colors ${
                    tableFilter === "empty"
                      ? "bg-green-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                  Boş ({filteredFloor.tables.filter(t => t.table_status === "empty").length})
                </button>
              </div>
            </div>
          </div>
        )}
      </div>


      {/* Masalar */}
      <div className="overflow-y-auto h-[calc(100vh-240px)]">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 w-full px-4 pb-4">
          {filteredFloor?.tables
            .filter(table => {
              // Filtre uygulanmış masaları göster
              if (tableFilter === "all") return true;
              if (tableFilter === "busy") return table.table_status === "busy" || table.table_status === "locked";
              if (tableFilter === "empty") return table.table_status === "empty";
              return true;
            })
            .map((table) => {
              const tableTotal = calculateTableTotal(table.orders);
              const tableColor =
              table.table_status === "locked"
                ? "bg-gray-500"
                : table.table_status === "busy"
                ? "bg-yellow-500"
                : "bg-green-500";

              return (
                <div
                  key={table.id}
                  className={`p-4 rounded-lg shadow-md text-white
                    ${table.table_status === "busy" && table.orders && table.orders.length > 0 && isOrderOlderThan10Minutes(table.orders[0]?.date) ? "bg-red-600 animate-pulse" : tableColor}
                    ${tableMovingState.isMoving && table.table_status === "empty" ? "animate-table-pulse shadow-lg z-10" : ""}
                    ${tableMergeState.isMerging && table.table_status === "busy" && table.id !== tableMergeState.sourceTableId ? "animate-table-pulse shadow-lg z-10" : ""}
                    ${tableMovingState.isMoving ? (table.table_status === "empty" ? "hover:scale-110 hover:shadow-xl" : "opacity-75") : ""}
                    ${tableMergeState.isMerging ? (table.table_status === "busy" && table.id !== tableMergeState.sourceTableId ? "hover:scale-110 hover:shadow-xl" : "opacity-75") : ""}
                    relative cursor-pointer transition-all duration-300`}
                  onClick={() => {
                    // Masa taşıma modu aktifse ve boş bir masa seçildiyse
                    if (tableMovingState.isMoving && table.table_status === "empty") {
                      // Onay modalını göstermek için state'i güncelle
                      setTableMovingState(prev => ({
                        ...prev,
                        targetTableId: table.id,
                        targetTableTitle: table.table_title,
                        showConfirmation: true
                      }));
                    }
                    // Masa birleştirme modu aktifse ve dolu bir masa seçildiyse
                    else if (tableMergeState.isMerging && table.table_status === "busy" && table.id !== tableMergeState.sourceTableId) {
                      // Onay modalını göstermek için state'i güncelle
                      setTableMergeState(prev => ({
                        ...prev,
                        targetTableId: table.id,
                        targetTableTitle: table.table_title,
                        showConfirmation: true
                      }));
                    }
                    // Normal mod - boş masa seçilirse ürün ekleme
                    else if (table.table_status === "empty") {
                      console.log("TableSelection: Boş masa seçildi, ID:", table.id);
                      // Masa ID'sini string'e çevirerek gönderelim
                      onTableSelect(String(table.id));
                    }
                    // Normal mod - dolu masa seçilirse ayarlar modalı
                    else {
                      btnShowSettingModal(
                        table.order_ids,
                        table
                      );
                    }
                  }}
                >
                  <div className="flex justify-between items-center">
                    <div className="font-bold truncate mr-2">{table.table_title}</div>
                    {/* "..." butonu */}
                    <button
                      className="text-black bg-white rounded-md text-xl w-10 h-10 flex-shrink-0"
                      onClick={(e) => {
                        e.stopPropagation(); // Masa onClick'ini engeller
                        btnShowSettingModal(
                          table.order_ids,
                          table
                        );
                      }}
                    >
                      &#x22EE;
                    </button>
                  </div>
                  <div className="text-sm mt-2">
                    {tableTotal.toFixed(2)} {state.currency}
                  </div>
                  {table.orders && table.orders.length > 0 && (
                    <div className="text-xs mt-1 bg-black bg-opacity-20 p-1 rounded">
                      <span className="font-semibold">Sipariş: </span>
                      {formatOrderDate(table.orders[0]?.date)}
                    </div>
                  )}
                </div>
              );
            })}
        </div>
      </div>

      <PaymentModal
        table={modalData}
        paymentState={quickPaymentState}
        currency={state.currency}
        paymentList={paymentList}
        setPaymentList={setPaymentList}
        onOrderUpdate={async (tableId) => {
          await refreshOrders();

          // Sipariş güncellendikten sonra, güncel sipariş özetini al
          try {
            const res = await getCompleteOrderPaymentSummary([tableId]);
            if (res.status === 200) {
              const { subtotal, taxTotal, total, discountTotal, orders } = res.data;

              // QuickPaymentState'i güncelle
              setQuickPaymentState(prev => ({
                ...prev,
                subtotal,
                taxTotal,
                summaryTotal: total,
                discountTotal,
                summaryOrders: orders
              }));

              return res.data;
            }
          } catch (error) {
            console.error('Sipariş güncellenirken hata:', error);
          }

          return null;
        }}
        paymentTypes={state.paymentTypes}
        onPaymentTypeSelect={handlePaymentTypeSelect}
        onPaymentComplete={handleQuickPayAndComplete}
      />

      {/* Neden Seçme Modalı */}
      <dialog id="modal-select-reason" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">
            {state.reasonType === "cancellation" && "İptal Nedeni Seçin"}
            {state.reasonType === "complimentary" && "İkram Nedeni Seçin"}
            {state.reasonType === "waste" && "Zayi Nedeni Seçin"}
          </h3>

          <div className="my-4">
            {loadingReasons ? (
              <div className="flex justify-center">
                <span className="loading loading-spinner loading-md"></span>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-2">
                {state.reasonType === "cancellation" && cancellationReasons.map((reason) => (
                  <label key={reason.id} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="reason"
                      value={reason.id}
                      className="radio radio-sm mr-3"
                      onChange={() => setSelectedReason(reason.id)}
                      checked={selectedReason === reason.id}
                    />
                    <span>{reason.title}</span>
                  </label>
                ))}

                {state.reasonType === "complimentary" && complimentaryReasons.map((reason) => (
                  <label key={reason.id} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="reason"
                      value={reason.id}
                      className="radio radio-sm mr-3"
                      onChange={() => setSelectedReason(reason.id)}
                      checked={selectedReason === reason.id}
                    />
                    <span>{reason.title}</span>
                  </label>
                ))}

                {state.reasonType === "waste" && wasteReasons.map((reason) => (
                  <label key={reason.id} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="reason"
                      value={reason.id}
                      className="radio radio-sm mr-3"
                      onChange={() => setSelectedReason(reason.id)}
                      checked={selectedReason === reason.id}
                    />
                    <span>{reason.title}</span>
                  </label>
                ))}
              </div>
            )}
          </div>

          <div className="modal-action">
            <form method="dialog" className="flex gap-2 w-full">
              <button className="btn flex-1">İptal</button>
              <button
                onClick={() => {
                  if (!selectedReason) {
                    toast.error("Lütfen bir neden seçin!");
                    return;
                  }

                  // İşlem tipine göre ilgili fonksiyonu çağır
                  if (state.reasonType === "cancellation") {
                    if (state.selectedOrderItemId && state.selectedOrderItemStatus) {
                      // Ürün iptal işlemi
                      btnChangeOrderItemStatus(state.selectedOrderItemId, state.selectedOrderItemStatus, selectedReason);
                    } else {
                      // Sipariş iptal işlemi
                      showCancelOrderModalAfterReason();
                    }
                  } else if (state.reasonType === "complimentary") {
                    completeComplimentaryItem();
                  } else if (state.reasonType === "waste") {
                    completeWasteItem();
                  }

                  document.getElementById("modal-select-reason").close();
                }}
                className="btn bg-restro-green text-white hover:bg-restro-green-dark flex-1"
              >
                Devam Et
              </button>
            </form>
          </div>
        </div>
      </dialog>

      {/* dialog: cancel order */}
      <dialog id="modal-order-cancel" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Dikkat!</h3>
          <p className="py-4">
            Siparişi İptal Etmek İstediğinize Emin misiniz? 🛑✋
          </p>

          {/* Sipariş Ürünleri Listesi */}
          <div className="max-h-60 overflow-y-auto my-4 border rounded-lg">
            {state.orders && state.orders.length > 0 ? (
              state.orders.map((order) =>
                order.items && order.items.length > 0 ? (
                  order.items.map((orderItem) => {
                    // Ekstraların metin haline getirilmesi:
                    const addonsText =
                      orderItem.addons && orderItem.addons.length > 0
                        ? orderItem.addons.map((a) => a.title).join(", ")
                        : null;

                    return (
                      <div
                        key={orderItem.id}
                        className="flex items-center gap-2 py-2 px-3 border-b border-gray-200"
                      >
                        {/* Durum İkonları */}
                        {orderItem.status === "preparing" && (
                          <IconClock stroke={iconStroke} className="text-amber-500" />
                        )}
                        {orderItem.status === "completed" && (
                          <IconChecks stroke={iconStroke} className="text-green-500" />
                        )}
                        {orderItem.status === "cancelled" && (
                          <IconX stroke={iconStroke} className="text-red-500" />
                        )}
                        {orderItem.status === "delivered" && (
                          <IconChecks stroke={iconStroke} className="text-green-500" />
                        )}

                        {/* Ürün Detayları */}
                        <div className="flex-1">
                          <p>
                            {orderItem.item_title} {orderItem.variant_title} x {orderItem.quantity}
                          </p>
                          {addonsText && (
                            <p className="text-sm text-gray-700">Ekstralar: {addonsText}</p>
                          )}
                          {orderItem.notes && (
                            <p className="text-sm text-gray-700">Not: {orderItem.notes}</p>
                          )}
                        </div>

                        {/* İptal Butonu: Eğer ürün zaten iptal edilmişse buton gösterilmez */}
                        {orderItem.status !== "cancelled" && (
                          <button
                            onClick={() =>
                              showChangeOrderItemStatusModal(orderItem.id, "cancelled")
                            }
                            className="btn btn-sm bg-red-500 hover:bg-red-600 text-white flex items-center gap-1"
                          >
                            <IconX size={16} stroke={iconStroke} />
                            İptal
                          </button>
                        )}
                      </div>
                    );
                  })
                ) : null
              )
            ) : (
              <p className="text-gray-500 p-4 text-center">Sipariş ürünleri bulunamadı.</p>
            )}
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn">Vazgeç</button>
              <button
                onClick={() => {
                  btnCancelOrder();
                }}
                className="ml-2 btn hover:bg-red-700 bg-red-500 text-white"
              >
                Bütün Siparişi İptal Et!
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: cancel order */}

      {/* dialog: setting order */}
      <dialog id="modal-setting" className="modal">
        <div className="modal-box ">
        <h2 className="font-bold text-lg">
      {state.table?.table_title || "Seçenekler"}

    </h2>
    <h4 className="font-bold py-2" >
    {state.table?.user_name ? `Garson : ${state.table.user_name}` : ""}
    </h4>


          {/* Butonlar */}
          <div className="grid grid-cols-2 gap-4">

            <RequireScopes requiredScopes={[SCOPES.KASIYER]} userScopes={userScopes} userRole={userRole}    >

              <button
                onClick={() => btnShowPayAndComplete()}
                className="btn bg-green-500 hover:bg-green-600 text-white flex items-center gap-2"
              >
                <IconCash size={18} stroke={1.5} />
                Hızlı Ödeme
              </button>

              <button
                onClick={() => btnShowPayAndCompleteAdvanced()}
                className="btn bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
              >
                <IconCash size={18} stroke={1.5} />
                Gelişmiş Ödeme
              </button>

              {/* Masa kilitli değilse Masa Değiştir ve Masa Birleştir butonlarını göster */}
              {!isLocked && (
                <>
                  <button
                    onClick={() => {
                      // Masa taşıma işlemini başlat
                      startTableMove(state.orderIds[0], state.table?.table_title);
                    }}
                    className="btn bg-yellow-500 hover:bg-yellow-600 text-white flex items-center gap-2"
                  >
                    <IconArmchair size={18} stroke={1.5} />
                    Masa Değiştir
                  </button>

                  <button
                    onClick={() => {
                      // Masa birleştirme modunu aktif et
                      setTableMergeState({
                        showConfirmation: false,
                        sourceTableId: state.table?.id,
                        sourceTableTitle: state.table?.table_title,
                        targetTableId: null,
                        targetTableTitle: '',
                        isMerging: true
                      });

                      // Modalı kapat
                      document.getElementById("modal-setting").close();

                      // Kullanıcıya bilgi ver
                      toast.success("Lütfen birleştirmek istediğiniz dolu masayı seçin", {
                        duration: 5000,
                        icon: '🔄'
                      });
                    }}
                    className="btn bg-purple-500 hover:bg-purple-600 text-white flex items-center gap-2"
                  >
                    <IconChecks size={18} stroke={1.5} />
                    Masa Birleştir
                  </button>
                </>
              )}

            </RequireScopes>

            <RequireScopes requiredScopes={[SCOPES.SIPARIS_IPTAL]} userScopes={userScopes} userRole={userRole}>
              <button
                onClick={() => btnShowCancelOrderModal()}
                className="btn bg-red-500 hover:bg-red-600 text-white flex items-center gap-2"
              >
                <IconX size={18} stroke={1.5} />
                Sipariş İptali
              </button>
            </RequireScopes>
            {!isLocked && (

            <RequireScopes requiredScopes={[SCOPES.GARSON]} userScopes={userScopes} userRole={userRole}>
              <button
                onClick={() => {
                  console.log("TableSelection: Ürün Ekle butonuna tıklandı, Masa ID:", table.id);
                  // Masa ID'sini string'e çevirerek gönderelim
                  onTableSelect(String(table.id));
                }}
                className="btn bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
              >
                <IconCopyPlus size={18} stroke={1.5} />
                Ürün Ekle
              </button>

              <button
                onClick={() => btnPrintReceipt(table.id)}
                className="btn bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2"
              >
                <IconReceipt size={18} stroke={1.5} />
                Hesap Yazdır
              </button>

            </RequireScopes>
            )}

             {/* "Ürünleri Gör" Butonu */}
      <button
        onClick={() => {
          const modalItemsView = document.getElementById("modal-order-items-view");
          if (modalItemsView) {
            modalItemsView.showModal();
          }
        }}
        className="btn bg-indigo-500 hover:bg-indigo-600 text-white flex items-center gap-2"
      >
        <IconEye size={18} stroke={1.5} />
        Ürünleri Gör
      </button>

       {/* Eğer masa kilitliyse sadece kasiyer görebilir */}
       {isLocked && (
            <RequireScopes requiredScopes={[SCOPES.KASIYER]} userScopes={userScopes} userRole={userRole}>
              <button
                onClick={unlockTable}
                className="btn bg-red-500 hover:bg-red-600 text-white flex items-center  w-full"
              >
                <IconLockOpen2 size={18} />
                Kilidi Kaldır
              </button>
            </RequireScopes>
          )}

          </div>


          {/* Kapatma ve İptal */}
          <div className="modal-action mt-4">
            <button onClick={() => {
                const modalSetting = document.getElementById("modal-setting");
                if (modalSetting) {
                  modalSetting.close();
                }
              }} className="btn" formMethod="dialog">Kapat</button>
          </div>
        </div>
      </dialog>
      {/* dialog: setting order */}

      {/* dialog: complete order & payment summary */}
      <dialog id="modal-order-summary-complete" className="modal">
        <div className="modal-box">
          <div className="flex items-center justify-between">
            <h3 className="font-bold text-lg">Ödeme Al ve Siparişi Kapat!</h3>
            <form method='dialog'>
              <button className="hover:bg-red-100 border-none transition active:scale-95 bg-red-50 text-red-500 btn btn-sm btn-circle"><IconX size={18} stroke={iconStroke} /></button>
            </form>
          </div>


          <div className="my-6">

            <div className="flex w-full items-center divide-x gap-x-4">
              <div className="flex-1 text-center">
                <p>Ara Toplam</p>
                <p className="text-2xl">
                  {Number(quickPaymentState.summaryNetTotal).toFixed(2)}
                  {currency}
                </p>
              </div>

              <div className="flex-1 text-center">
                <p>KDV</p>
                <p className="text-2xl">
                  {Number(quickPaymentState.summaryTaxTotal).toFixed(2)}
                  {currency}
                </p>
              </div>

              <div className="flex-1 text-center">
                <p>Toplam</p>
                <p className="text-2xl text-restro-green font-bold">
                  {Number(quickPaymentState.summaryTotal).toFixed(2)}
                  {currency}
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
  {paymentTypes.map((paymentType, i) => (
    <label key={i} className="block">
      <input
        checked={state?.selectedPaymentType === paymentType?.id}
        onChange={e => {
          setState({
            ...state,
            selectedPaymentType: Number(e.target.value), // Eğer id sayısal ise
          });
        }}
        type="radio"
        name="payment_type"
        id={paymentType?.icon}
        value={paymentType?.id}
        className="peer hidden"
      />
      <label
        htmlFor={paymentType?.icon}
        className="border rounded-2xl flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-restro-green peer-checked:text-restro-green peer-checked:font-bold cursor-pointer transition"
      >
        {paymentType?.icon ? (
          <div>{PAYMENT_ICONS[paymentType?.icon]}</div>
        ) : null}
        <p className="text-xs">{paymentType.title}</p>
      </label>
    </label>
  ))}

  {/* Yeni "Cari" Ödeme Seçeneği: id olarak -1 kullanıyoruz */}
  <label key="cari" className="block">
    <input
      checked={state?.selectedPaymentType === -1}
      onChange={() => {
        setState({
          ...state,
          selectedPaymentType: -1,
        });
      }}
      type="radio"
      name="payment_type"
      id="cari"
      value={-1}
      className="peer hidden"
    />
    <label
      htmlFor="cari"
      className="border rounded-2xl flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-restro-green peer-checked:text-restro-green peer-checked:font-bold cursor-pointer transition"
    >
      <div>Cari</div>
      <p className="text-xs">Cari Ödeme</p>
    </label>
  </label>
</div>

          {/* Yazarkasa Cihazları - Sadece nakit ödeme seçildiğinde ve cihaz varsa göster */}
          {quickPaymentState.yazarkasaDevices && quickPaymentState.yazarkasaDevices.length > 0 && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Yazarkasa Cihazı Seçin:
              </label>
              <div className="grid grid-cols-3 gap-2">
                {quickPaymentState.yazarkasaDevices.map((device) => (
                  <label key={device.id} className="block">
                    <input
                      checked={quickPaymentState.selectedYazarkasaDevice === device.id}
                      onChange={() => {
                        setQuickPaymentState(prev => ({
                          ...prev,
                          selectedYazarkasaDevice: device.id
                        }));
                      }}
                      type="radio"
                      name="yazarkasa_device"
                      value={device.id}
                      className="peer hidden"
                    />
                    <div className="border rounded-2xl flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-restro-green peer-checked:text-restro-green peer-checked:font-bold cursor-pointer transition">
                      
                      <p className="text-xs font-medium text-center">{device.device_name}</p>
                      
                    </div>
                  </label>
                ))}
              </div>
            </div>
          )}

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700">
              Müşteri Seçimi (Zorunlu değil. Cari ödeme için zorunlu):
            </label>
            <AsyncCreatableSelect
                  menuPlacement='auto'
                  loadOptions={searchCustomersAsync}
                  isClearable
                  placeholder="Müşteri adı veya telefon numarası..."
                  formatCreateLabel={(inputValue) => `"${inputValue}" ile yeni müşteri ekle`}
                  noOptionsMessage={()=>{return "İsim veya telefon numarası..."}}
                  onChange={setCustomer}

                />

          </div>

          <div className="modal-action">
  <form method="dialog" className="w-full">
    {state.selectedPaymentType === -1 ? (
      <button
        onClick={() => {
          btnPayAndCompleteCari();
        }}
        className="w-full btn hover:bg-restro-green-dark bg-restro-green text-white"
      >
        Cari Ödeme ile Kapat
      </button>
    ) : (
      <button
        onClick={() => {
          btnPayAndComplete();
        }}
        className="w-full btn hover:bg-restro-green-dark bg-restro-green text-white"
      >
        Ödemeyi Al ve Siparişi Kapat
      </button>
    )}
  </form>
</div>

        </div>
      </dialog>


      {/* dialog: complete order & payment summary */}


<dialog id="modal-order-items-view" className="modal">
  <div className="modal-box max-w-4xl p-6">
    <div className="flex items-center justify-between mb-4">
      <h3 className="font-bold text-2xl">Ürün Detayları</h3>
      <button
        onClick={() => document.getElementById("modal-order-items-view").close()}
        className="btn btn-circle btn-ghost text-gray-500 hover:bg-gray-100"
      >
        <IconX size={24} stroke={iconStroke} />
      </button>
    </div>

    <div className="max-h-[70vh] overflow-y-auto my-4">
      {state.orders && state.orders.length > 0 ? (
        state.orders.map((order) =>
          order.items && order.items.length > 0 ? (
            <div className="border rounded-lg overflow-hidden mb-4">
              <table className="w-full text-sm">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left w-24 p-3">Ürün</th>
                    <th className="text-center w-16">Adet</th>
                    <th className="text-center w-24">Fiyat</th>
                    <th className="text-center w-20">Durum</th>
                    <th className="text-center w-40">İşlemler</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {order.items.map((orderItem) => {
                    const addonsText =
                      orderItem.addons && orderItem.addons.length > 0
                        ? orderItem.addons.map((a) => a.title).join(", ")
                        : null;

                    return (
                      <tr key={orderItem.id} className="hover:bg-gray-50">
                        <td className="p-3">
                          <div className={`font-medium ${orderItem.status === "cancelled" || orderItem.status === "complimentary" || orderItem.status === "waste" ? "line-through text-gray-500" : ""}`}>
                            {orderItem.item_title}
                          </div>
                          {orderItem.variant_title && (
                            <div className={`text-sm text-gray-700 ${orderItem.status === "cancelled" || orderItem.status === "complimentary" || orderItem.status === "waste" ? "line-through text-gray-500" : ""}`}>
                              {orderItem.variant_title}
                            </div>
                          )}
                          {addonsText && (
                            <div className={`text-xs text-gray-500 ${orderItem.status === "cancelled" || orderItem.status === "complimentary" || orderItem.status === "waste" ? "line-through" : ""}`}>
                              Ekstralar: {addonsText}
                            </div>
                          )}
                          {orderItem.notes && (
                            <div className={`text-xs text-gray-500 ${orderItem.status === "cancelled" || orderItem.status === "complimentary" || orderItem.status === "waste" ? "line-through" : ""}`}>
                              Not: {orderItem.notes}
                            </div>
                          )}
                        </td>
                        <td className="text-center p-3">
                          <span className={`inline-block bg-gray-100 px-2 py-1 rounded-full ${orderItem.status === "cancelled" || orderItem.status === "complimentary" || orderItem.status === "waste" ? "line-through text-gray-500" : ""}`}>
                            {orderItem.quantity}
                          </span>
                        </td>
                        <td className={`text-center p-3 font-medium ${orderItem.status === "cancelled" || orderItem.status === "complimentary" || orderItem.status === "waste" ? "line-through text-gray-500" : ""}`}>
                          {Number(orderItem.price).toFixed(2)} {state.currency}
                        </td>
                        <td className="text-center p-3">
                          {orderItem.status === "preparing" && (
                            <span className="inline-block w-full px-2 py-1 text-xs rounded bg-amber-100 text-amber-800">
                              Hazırlanıyor
                            </span>
                          )}
                          {orderItem.status === "completed" && (
                            <span className="inline-block w-full px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">
                              Hazır
                            </span>
                          )}
                          {orderItem.status === "cancelled" && (
                            <span className="inline-block w-full px-2 py-1 text-xs rounded bg-red-100 text-red-800">
                              İptal Edildi
                            </span>
                          )}
                          {orderItem.status === "complimentary" && (
                            <span className="inline-block w-full px-2 py-1 text-xs rounded bg-green-100 text-green-800">
                              İkram Edildi
                            </span>
                          )}
                          {orderItem.status === "waste" && (
                            <span className="inline-block w-full px-2 py-1 text-xs rounded bg-red-200 text-red-800">
                              Zayi
                            </span>
                          )}

                        </td>
                        <td className="p-3">
                          <div className="flex justify-center space-x-1">
                            {orderItem.status !== "cancelled" && orderItem.status !== "complimentary" && orderItem.status !== "waste" ? (
                              <>
                                {/* İptal Butonu - Herkes görebilir */}
                                <button
                                  onClick={() => showChangeOrderItemStatusModal(orderItem.id, "cancelled")}
                                  className="btn btn-sm bg-red-50 hover:bg-red-100 text-red-600"
                                  title="İptal Et"
                                >
                                  <IconX size={16} />
                                  İptal
                                </button>

                                {/* Kasiyer Yetkileri */}
                                <RequireScopes requiredScopes={[SCOPES.KASIYER]} userScopes={userScopes} userRole={userRole}>
                                  <button
                                    onClick={() => handleComplimentaryItem(orderItem.id)}
                                    className="btn btn-sm bg-green-50 hover:bg-green-100 text-green-600"
                                    title="İkram Et"
                                  >
                                    <IconGift size={16} />
                                    İkram
                                  </button>
                                  <button
                                    onClick={() => handleWasteItem(orderItem.id)}
                                    className="btn btn-sm bg-red-50 hover:bg-red-100 text-red-600"
                                    title="Zayi"
                                  >
                                    <IconTrash size={16} />
                                    Zayi
                                  </button>
                                  <button
                                    onClick={() => handleMoveOrderItem(orderItem)}
                                    className="btn btn-sm bg-purple-50 hover:bg-purple-100 text-purple-600"
                                    title="Ürünü Taşı"
                                  >
                                    <IconArmchair size={16} />
                                    Taşı
                                  </button>
                                </RequireScopes>
                              </>
                            ) : (
                              <span className="text-sm text-gray-500 italic">
                                {orderItem.status === "cancelled" ? "İptal edildi" :
                                 orderItem.status === "complimentary" ? "İkram edildi" : "Zayi edildi"}
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : null
        )
      ) : (
        <div className="text-center py-10">
          <p className="text-gray-500 text-lg">Sipariş ürünleri bulunamadı.</p>
        </div>
      )}
    </div>
  </div>
  <form method="dialog" className="modal-backdrop">
    <button>Kapat</button>
  </form>
</dialog>




<dialog id="modal-edit-price" className="modal">
  <div className="modal-box">
    <h3 className="font-bold text-lg mb-4">Fiyat Düzenle</h3>

    {editingOrderItem && (
      <>
        <div className="mb-4">
          <p className="font-medium">{editingOrderItem.item_title}</p>
          {editingOrderItem.variant_title && (
            <p className="text-sm text-gray-600">{editingOrderItem.variant_title}</p>
          )}
        </div>

        <div className="form-control w-full">
          <label className="label">
            <span className="label-text">Yeni Fiyat</span>
          </label>
          <input
            type="number"
            value={newPrice}
            onChange={(e) => setNewPrice(e.target.value)}
            className="input input-bordered w-full"
            step="0.01"
            min="0"
          />
        </div>

        <div className="modal-action">
          <button
            onClick={() => document.getElementById("modal-edit-price").close()}
            className="btn"
          >
            İptal
          </button>
          <button
            onClick={async () => {
              try {
                const numPrice = parseFloat(newPrice);
                if (isNaN(numPrice)) {
                  toast.error("Geçerli bir fiyat giriniz!");
                  return;
                }

                await btnUpdateOrderItemPrice(editingOrderItem.id, numPrice);
                document.getElementById("modal-edit-price").close();
              } catch (error) {
                console.error(error);
              }
            }}
            className="btn bg-blue-500 hover:bg-blue-600 text-white"
          >
            Kaydet
          </button>
        </div>
      </>
    )}
  </div>
</dialog>

{/* Ürün Taşıma Modalı */}
<dialog id="modal-move-order-item" className="modal">
  <div className="modal-box">
    <h3 className="font-bold text-lg mb-4">Ürünü Başka Masaya Taşı</h3>

    {movingOrderItem && (
      <>
        <div className="mb-4">
          <p className="font-medium">{movingOrderItem.item_title}</p>
          {movingOrderItem.variant_title && (
            <p className="text-sm text-gray-600">{movingOrderItem.variant_title}</p>
          )}
          <p className="text-sm text-gray-500 mt-1">Miktar: {movingOrderItem.quantity}</p>
        </div>

        <div className="form-control w-full mb-4">
          <label className="label">
            <span className="label-text">Hedef Masa</span>
          </label>
          <select
            className="select select-bordered w-full"
            onChange={(e) => setState({ ...state, selectedTargetTableId: e.target.value })}
          >
            <option value="">Bir masa seçin...</option>
            {tables
              .flatMap((floor) => floor.tables)
              .filter(table => table.id !== state.table?.id) // Mevcut masayı filtrele
              .map((table) => (
                <option key={table.id} value={table.id}>
                  {table.table_title}
                </option>
              ))}
          </select>
        </div>

        <div className="modal-action">
          <button
            onClick={() => document.getElementById("modal-move-order-item").close()}
            className="btn"
          >
            İptal
          </button>
          <button
            onClick={() => {
              if (state.selectedTargetTableId) {
                moveOrderItemToAnotherTable(state.selectedTargetTableId);
              } else {
                toast.error("Lütfen hedef masa seçin!");
              }
            }}
            className="btn bg-purple-500 hover:bg-purple-600 text-white"
          >
            Ürünü Taşı
          </button>
        </div>
      </>
    )}
  </div>
</dialog>




      {/* Masa Taşıma Onay Modalı */}
      {tableMovingState.showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="font-bold text-lg mb-4">Masa Taşıma Onayı</h3>

            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
              <p className="text-yellow-800">
                <span className="font-bold">{tableMovingState.sourceTableTitle}</span> masasındaki siparişleri
                <span className="font-bold"> {tableMovingState.targetTableTitle}</span> masasına taşımak istediğinize emin misiniz?
              </p>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setTableMovingState(prev => ({ ...prev, showConfirmation: false }))}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none"
              >
                İptal
              </button>
              <button
                onClick={() => handleChangeTable(tableMovingState.orderId, tableMovingState.targetTableId)}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 focus:outline-none"
              >
                Evet, Taşı
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Masa Birleştirme Onay Modalı */}
      {tableMergeState.showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="font-bold text-lg mb-4">Masa Birleştirme Onayı</h3>

            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200 mb-4">
              <p className="text-purple-800">
                <span className="font-bold">{tableMergeState.sourceTableTitle}</span> masasındaki siparişleri
                <span className="font-bold"> {tableMergeState.targetTableTitle}</span> masasına birleştirmek istediğinize emin misiniz?
              </p>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setTableMergeState(prev => ({ ...prev, showConfirmation: false }))}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none"
              >
                İptal
              </button>
              <button
                onClick={() => handleMergeTables(tableMergeState.sourceTableId, tableMergeState.targetTableId)}
                className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 focus:outline-none"
              >
                Evet, Birleştir
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TableSelection;
