import React, { useEffect, useState } from "react";
import {
  getQRMenuInit,
  sendFeedback,
} from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import {
  IconX,
  IconCategory,
  IconMenuDeep,
  IconInfoCircle,
  IconLanguage,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandInstagram,
  IconBrandWhatsapp,
} from "@tabler/icons-react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";


export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [isComplaintModalOpen, setComplaintModalOpen] = useState(false);
  const [isSuggestionModalOpen, setSuggestionModalOpen] = useState(false);
  const [isThankYouModalOpen, setThankYouModalOpen] = useState(false);
  const [selectedEmoji, setSelectedEmoji] = useState(null);
  const emojis = ["😃", "😊", "😐", "😞", "😡"];

  const [feedbackType, setFeedbackType] = useState("");
  const [feedbackMessage, setFeedbackMessage] = useState("");
  const [isSendingFeedback, setIsSendingFeedback] = useState(false);

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    campaigns: null,
  });

  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [isCampaignModalOpen, setCampaignModalOpen] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  const handleSendFeedback = async () => {
    if (!feedbackType || (!feedbackMessage.trim() && feedbackType !== "thankyou")) {
      toast.error(t("fillFieldsError"));
      return;
    }

    try {
      setIsSendingFeedback(true);
      const message =
        feedbackType === "thankyou"
          ? `${t("thankYou")}! ${t("selectedEmoji")}: ${emojis[selectedEmoji]}`
          : feedbackMessage;

      await sendFeedback(qrcode, feedbackType, message);

      toast.success(t("feedbackSuccess"));
      setFeedbackType("");
      setFeedbackMessage("");
      setSelectedEmoji(null);
      setComplaintModalOpen(false);
      setSuggestionModalOpen(false);
      setThankYouModalOpen(false);
    } catch (error) {
      toast.error(t("feedbackError"));
    } finally {
      setIsSendingFeedback(false);
    }
  };

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("dynamic");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
                  updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          campaigns: data.campaigns,
          storeSettings: data?.storeSettings,
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - SewPOS`;
        }
        // İlk yüklemede kampanya varsa modal aç
        if (isFirstLoad && data.campaigns && data.campaigns.length > 0) {
          setSelectedCampaign(data.campaigns[0]);
          setCampaignModalOpen(true);
          setIsFirstLoad(false);
        }
      }
    } catch (error) {

    }
  };

  const { isLoading, storeSettings, campaigns } = state;
  const storeName = storeSettings?.store_name || t("storeName");
  const slides = storeSettings?.slides?.split(",") || [];



  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [slides.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const [isInfoModalOpen, setInfoModalOpen] = useState(false);
  const [isLanguageModalOpen, setLanguageModalOpen] = useState(false);

  const languages = [
    { code: "tr", label: "Türkçe" },
    { code: "en", label: "English" },
    { code: "fr", label: "Français" },
    { code: "de", label: "Deutsch" },
  ];

  if (isLoading) {
    return <QRMenuLoading />;
  }

  const bodyStyles = {
    backgroundColor: storeSettings?.background_color || "#f3f4f6",
    color: storeSettings?.text_color || "#000000",
  };

  const headerStyles = {
    backgroundColor: storeSettings?.header_color || "#047857",
    color: storeSettings?.header_text_color || "#ffffff",
  };

  const footerStyles = {
    backgroundColor: storeSettings?.footer_background_color || "#111827",
    color: storeSettings?.footer_text_color || "#ffffff",
  };

  const MenuButton = {
    backgroundColor: storeSettings?.menu_button_background_color || "#111827",
    color: storeSettings?.menu_button_text_color || "#ffffff",
  };

  const SikayetButon = {
    backgroundColor: storeSettings?.complaint_button_background_color || "#111827",
    color: storeSettings?.complaint_button_text_color || "#ffffff",
  };

  const OneriButon = {
    backgroundColor: storeSettings?.suggestion_button_background_color || "#111827",
    color: storeSettings?.suggestion_button_text_color || "#ffffff",
  };

  const TesekkurButon = {
    backgroundColor: storeSettings?.thank_you_button_background_color || "#111827",
    color: storeSettings?.thank_you_button_text_color || "#ffffff",
  };

  return (
    <div className="w-full min-h-screen" style={bodyStyles}>
      <header className="w-full py-4" style={headerStyles}>
        <div className="container mx-auto px-4 flex items-center justify-between">
          {storeSettings?.store_image ? (
            <img
              src={getImageURL(storeSettings.store_image)}
              alt={storeName}
              className="h-10 object-contain"
            />
          ) : (
            <h1 className="text-xl font-bold">{storeName}</h1>
          )}
          <IconMenuDeep size={28} stroke={2} className="cursor-pointer hidden" />
        </div>
      </header>

      <div className="w-full">
        <div className="container mx-auto">
          <div className="h-60 bg-gray-200 flex items-center justify-center overflow-hidden relative">
            {slides.length > 0 ? (
              <>
                <div
                  className="flex transition-transform duration-500"
                  style={{
                    transform: `translateX(-${currentSlide * 100}%)`,
                    width: `${slides.length * 100}%`,
                  }}
                >
                  {slides.map((slide, index) => (
                    <img
                      key={index}
                      src={getImageURL(slide)}
                      alt={`Slide ${index + 1}`}
                      className="w-full h-60 object-cover"
                      style={{ flex: "0 0 100%" }}
                    />
                  ))}
                </div>
                <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-2">
                  {slides.map((_, index) => (
                    <button
                      key={index}
                      className={`w-3 h-3 rounded-full ${
                        currentSlide === index ? "bg-gray-800" : "bg-gray-400"
                      }`}
                      onClick={() => goToSlide(index)}
                    />
                  ))}
                </div>
              </>
            ) : (
              <p className="text-gray-500">{t("noSlides", { ns: "dynamic" })}</p>
            )}
          </div>
        </div>
      </div>

      {/* Google Play ve App Store Logoları 
      <div className="w-full">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center mt-8">
            <div className="text-center mb-7">
              <p className="text-base font-bold">Masanızdan sipariş vererek puan kazanmak, kampanyalardan haberdar olmak ve bir çok fırsattan faydalanmak için uygulamamızı indirin!</p>
            </div>
            <div className="flex justify-center space-between gap-6">
              <a href="https://play.google.com/store/apps/details?id=com.hollystone" target="_blank" rel="noopener noreferrer" className="transition-transform hover:scale-105">
                <img src="/assets/<EMAIL>" alt="Google Play" className="h-14" />
              </a>
              <a href="https://apps.apple.com/tr/app/hollystone/id6740208510?l=tr" target="_blank" rel="noopener noreferrer" className="transition-transform hover:scale-105">
                <img src="/assets/<EMAIL>" alt="App Store" className="h-14" />
              </a>
            </div>
          </div>
        </div>
      </div>
      */}

      <div className="w-full">
        <div className="container mx-auto px-4">
          <div className="flex justify-center gap-6 mt-2 mb-6">
            {storeSettings?.facebook && (
              <a href={storeSettings.facebook} target="_blank" rel="noopener noreferrer">
                <IconBrandFacebook size={32} stroke={1.5} className="text-blue-600 hover:text-blue-800" />
              </a>
            )}
            {storeSettings?.twitter && (
              <a href={storeSettings.twitter} target="_blank" rel="noopener noreferrer">
                <IconBrandTwitter size={32} stroke={1.5} className="text-blue-400 hover:text-blue-600" />
              </a>
            )}
            {storeSettings?.instagram && (
              <a href={storeSettings.instagram} target="_blank" rel="noopener noreferrer">
                <IconBrandInstagram size={32} stroke={1.5} className="text-pink-500 hover:text-pink-700" />
              </a>
            )}
            {storeSettings?.whatsapp && (
              <a href={storeSettings.whatsapp} target="_blank" rel="noopener noreferrer">
                <IconBrandWhatsapp size={32} stroke={1.5} className="text-green-500 hover:text-green-700" />
              </a>
            )}
          </div>
        </div>
      </div>

      <div className="w-full">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-4 mt-4">
            <button
              onClick={() =>
                navigate(
                  encryptedTableId
                    ? `/m/${qrcode}/category?table=${encryptedTableId}`
                    : `/m/${qrcode}/category`
                )
              }
              className="flex h-20 items-center justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
              style={MenuButton}
            >
              <IconCategory size={24} stroke={1.5} /> {t("menuButton")}
            </button>
          </div>
        </div>
      </div>

      <div className="w-full">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 gap-4 mt-4">
            <button
              onClick={() => setComplaintModalOpen(true)}
              className="flex items-center justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
              style={SikayetButon}
            >
              <IconX size={24} stroke={1.5} /> {t("complaint")}
            </button>
            <button
              onClick={() => setSuggestionModalOpen(true)}
              className="flex items-center justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
              style={OneriButon}
            >
              <IconInfoCircle size={24} stroke={1.5} /> {t("suggestion")}
            </button>
          </div>
        </div>
      </div>

      <div className="w-full">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-4 mt-4">
            <button
              onClick={() => setThankYouModalOpen(true)}
              className="flex items-center h-20 justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
              style={TesekkurButon}
            >
              😊 {t("thankYou")}
            </button>
          </div>
        </div>
      </div>

      {/* Kampanyalar Bölümü */}
      <div className="w-full mt-8 pb-48">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">{t("campaigns")}</h2>
          {campaigns && campaigns.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {campaigns.map((campaign) => (
                <div
                  key={campaign.id}
                  className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer"
                  onClick={() => {
                    setSelectedCampaign(campaign);
                    setCampaignModalOpen(true);
                  }}
                >
                  <div className="h-40 bg-gray-100 flex items-center justify-center">
                    {campaign.image_url ? (
                      <img
                        src={getImageURL(campaign.image_url)}
                        alt={campaign.campaign_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <p className="text-gray-500 text-sm">{t("noCampaignImage")}</p>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-gray-800 truncate">{campaign.campaign_name}</h3>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {campaign.description || t("noDescription", { ns: "dynamic" })}
                    </p>
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{t("startDate")}: {new Date(campaign.start_date).toLocaleDateString("tr-TR")}</p>
                      <p>{t("endDate")}: {new Date(campaign.end_date).toLocaleDateString("tr-TR")}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center text-gray-500 text-lg">{t("noCampaigns")}</p>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="fixed bottom-0 left-0 w-full py-4 shadow-lg flex justify-between items-center px-6" style={footerStyles}>
        <button onClick={() => setInfoModalOpen(true)} className="hover:text-gray-400 transition">
          <IconInfoCircle size={28} />
        </button>
        <button onClick={() => setLanguageModalOpen(true)} className="hover:text-gray-400 transition">
          <IconLanguage size={28} />
        </button>
      </div>

      {/* Kampanya Detay Modal */}
      {isCampaignModalOpen && selectedCampaign && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50"
          onClick={() => setCampaignModalOpen(false)}
        >
          <div
            className="bg-white rounded-xl w-full max-w-md p-6 shadow-2xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-500 hover:text-red-500 transition"
              onClick={() => setCampaignModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <h2 className="text-xl font-bold text-gray-800 mb-4">{selectedCampaign.campaign_name}</h2>

            {/* Kampanya Resmi */}
            <div className="mb-4">
              {selectedCampaign.image_url ? (
                <img
                  src={getImageURL(selectedCampaign.image_url)}
                  alt={selectedCampaign.campaign_name}
                  className="w-full h-48 object-cover rounded-lg"
                />
              ) : (
                <div className="w-full h-48 bg-gray-100 flex items-center justify-center rounded-lg">
                  <p className="text-gray-500">{t("noCampaignImage")}</p>
                </div>
              )}
            </div>

            <p className="text-gray-600 text-sm mb-4">
              {selectedCampaign.description || t("noDescription", { ns: "dynamic" })}
            </p>
            <div className="text-sm text-gray-500">
              <p>{t("startDate")}: {new Date(selectedCampaign.start_date).toLocaleDateString("tr-TR")}</p>
              <p>{t("endDate")}: {new Date(selectedCampaign.end_date).toLocaleDateString("tr-TR")}</p>
            </div>
          </div>
        </div>
      )}

      {/* Diğer Modallar */}
      {isInfoModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50"
          onClick={() => setInfoModalOpen(false)}
        >
          <div
            className="bg-white rounded-xl w-96 p-8 shadow-2xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-500 hover:text-red-500 transition"
              onClick={() => setInfoModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <h2 className="text-xl font-bold text-gray-800 mb-4">{storeName}</h2>
            <p className="text-gray-600 text-sm">
              {storeSettings.address && (
                <>
                  <span className="block font-medium text-gray-700">{t("address")}:</span>
                  {storeSettings.address}
                </>
              )}
              {storeSettings.phone && (
                <>
                  <span className="block font-medium text-gray-700 mt-4">{t("phone")}:</span>
                  {storeSettings.phone}
                </>
              )}
              {storeSettings.email && (
                <>
                  <span className="block font-medium text-gray-700 mt-4">{t("email")}:</span>
                  {storeSettings.email}
                </>
              )}
            </p>
          </div>
        </div>
      )}

      {isLanguageModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
          onClick={() => setLanguageModalOpen(false)}
        >
          <div
            className="bg-white rounded-lg w-96 p-6 shadow-xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-800 transition"
              onClick={() => setLanguageModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">{t("language")}</h2>
            <div className="flex justify-center">
              <select
                className="text-sm border border-gray-300 rounded-lg py-2 px-4 text-gray-700"
                value={i18n.language}
                onChange={(e) => {
                  i18n.changeLanguage(e.target.value);
                  setLanguageModalOpen(false);
                }}
              >
                {languages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {isComplaintModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
          onClick={() => setComplaintModalOpen(false)}
        >
          <div
            className="bg-white rounded-lg w-96 p-6 shadow-xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-800 transition"
              onClick={() => setComplaintModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">{t("complaint")}</h2>
            <textarea
              className="w-full border border-gray-300 rounded-lg p-2"
              rows="5"
              placeholder={t("complaintPlaceholder")}
              value={feedbackMessage}
              onChange={(e) => {
                setFeedbackMessage(e.target.value);
                setFeedbackType("complaint");
              }}
            />
            <button
              className={`mt-4 w-full bg-red-500 text-white py-2 rounded-lg ${isSendingFeedback ? "opacity-50" : ""}`}
              disabled={isSendingFeedback}
              onClick={handleSendFeedback}
            >
              {isSendingFeedback ? t("sending") : t("send")}
            </button>
          </div>
        </div>
      )}

      {isSuggestionModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
          onClick={() => setSuggestionModalOpen(false)}
        >
          <div
            className="bg-white rounded-lg w-96 p-6 shadow-xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-800 transition"
              onClick={() => setSuggestionModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">{t("suggestion")}</h2>
            <textarea
              className="w-full border border-gray-300 rounded-lg p-2"
              rows="5"
              placeholder={t("suggestionPlaceholder")}
              value={feedbackMessage}
              onChange={(e) => {
                setFeedbackMessage(e.target.value);
                setFeedbackType("suggestion");
              }}
            />
            <button
              className={`mt-4 w-full bg-blue-500 text-white py-2 rounded-lg ${isSendingFeedback ? "opacity-50" : ""}`}
              disabled={isSendingFeedback}
              onClick={handleSendFeedback}
            >
              {isSendingFeedback ? t("sending") : t("send")}
            </button>
          </div>
        </div>
      )}

      {isThankYouModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
          onClick={() => setThankYouModalOpen(false)}
        >
          <div
            className="bg-white rounded-lg w-96 p-6 shadow-xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-800 transition"
              onClick={() => setThankYouModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">{t("thankYou")}</h2>
            <p className="text-gray-600 text-center mb-4">{t("thankYouMessage")}</p>
            <div className="flex justify-center gap-4">
              {emojis.map((emoji, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedEmoji(index)}
                  className={`text-3xl ${
                    selectedEmoji === index ? "scale-125 text-green-500" : "text-gray-500"
                  } transition-transform duration-300`}
                >
                  {emoji}
                </button>
              ))}
            </div>
            {selectedEmoji !== null && (
              <div className="mt-4 text-center text-sm text-gray-600">
                {t("selectedEmoji")}: {emojis[selectedEmoji]}
              </div>
            )}
            <button
              className={`mt-6 w-full bg-green-500 text-white py-2 rounded-lg ${isSendingFeedback ? "opacity-50" : ""}`}
              disabled={isSendingFeedback}
              onClick={() => {
                if (selectedEmoji === null) {
                  toast.error(t("selectEmojiError"));
                  return;
                }
                setFeedbackType("thankyou");
                handleSendFeedback();
              }}
            >
              {isSendingFeedback ? t("sending") : t("send")}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}