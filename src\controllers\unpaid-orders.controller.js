import ApiClient from "../helpers/ApiClient";
import useSWR from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

export function useUnpaidOrders({ type, from = null, to = null }) {
  // Parametreleri kontrol et ve URL oluştur
  let params = `type=${type}`;
  if (from) params += `&from=${encodeURIComponent(from)}`;
  if (to) params += `&to=${encodeURIComponent(to)}`;
  
  const APIURL = `/unpaid-orders?${params}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}
