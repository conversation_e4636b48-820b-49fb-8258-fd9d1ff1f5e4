import React, { useRef, useState } from "react";
import Page from "../components/Page";
import { IconCarrot, IconFilter, IconPrinter, IconCash, IconSearch } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { useReports } from "../controllers/reports.controller";
import { CURRENCIES } from "../config/currencies.config";
import { savePrintDataReport } from "../controllers/pos.controller";
import { toast } from "react-hot-toast";
import { useActiveCashRegisterSessions } from "../controllers/cash-register.controller";
import { formatDate } from "../helpers/formatDate";
import { useNavigate } from "react-router-dom";

// Sayısal değerleri formatlamak için yardımcı fonksiyon
const formatNumber = (value) => {
  if (!value) return "0";
  // String ise sayıya çevirelim
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return numValue.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};


export default function ReportsPage() {
  const navigate = useNavigate();
  const filters = [
    { key: "today", value: "Bugün" },
    { key: "yesterday", value: "Dün" },
    { key: "last_7days", value: "Son 7 Gün" },
    { key: "this_month", value: "Bu Ay" },
    { key: "last_month", value: "Geçen Ay" },
    { key: "custom", value: "Özel Tarih" },
  ];

  const fromDateRef = useRef();
  const toDateRef = useRef();
  const fromTimeRef = useRef();
  const toTimeRef = useRef();
  const filterTypeRef = useRef();

  const now = new Date();
  const defaultDateFrom = `${now.getFullYear()}-${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;
  const defaultDateTo = `${now.getFullYear()}-${(now.getMonth() + 2)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;
  const defaultTimeFrom = "00:00";
  const defaultTimeTo = "23:59";

  const [state, setState] = useState({
    filter: filters[0].key,
    fromDate: null,
    toDate: null,
    fromTime: null,
    toTime: null
  });

  // Raporları getir
  const {data, error, isLoading} = useReports({
    type: state.filter,
    from: state.fromDate ? `${state.fromDate}T${state.fromTime || defaultTimeFrom}` : null,
    to: state.toDate ? `${state.toDate}T${state.toTime || defaultTimeTo}` : null,
  });

  // Açık kasa oturumlarını getir
  const { data: activeCashRegisterSessions, isLoading: isLoadingActiveSessions } = useActiveCashRegisterSessions();

  if(isLoading || isLoadingActiveSessions) {
    return <Page>
      Lütfen bekleyin...
    </Page>
  }

  if(error) {
    console.error(error);
    return <Page>
      Error Loading Reports Data, Please try later!
    </Page>;
  }

  const {
    ordersCount, newCustomers, repeatedCustomers, creditTotal,
    cancelledOrdersAndItems, currency:currencyCode, averageOrderValue,
    totalCustomers, netRevenue, topSellingItems,
    getTotalDiscounts, revenueTotal,
    openOrders, wasteTotal, complimentaryTotal, waiterSalesReport
  } = data;

  const currency = CURRENCIES.find((c)=>c.cc==currencyCode)?.symbol;

  // Yazdırma fonksiyonu
const handlePrintReport = async () => {
  try {
    toast.loading("Rapor yazdırılıyor...");

    // Yazdırılacak rapor verilerini hazırla
    const printData = {
      cancelledOrdersAndItems,
      netRevenue,
      revenueTotal,
      creditTotal,
      getTotalDiscounts,
      wasteTotal,
      complimentaryTotal
    };

    await savePrintDataReport(printData);
    toast.dismiss();
    toast.success("Rapor yazdırma isteği gönderildi");
  } catch (error) {
    toast.dismiss();
    console.error("Yazdırma hatası:", error);
    toast.error("Yazıcıya ulaşılamıyor");
  }
};

  return (
    <Page>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <h1 className="text-2xl font-bold">Raporlar</h1>

        <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
          <button
            onClick={() => document.getElementById("filter-dialog").showModal()}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-gray-200 flex-1 sm:flex-none justify-center sm:justify-start"
          >
            <IconFilter stroke={iconStroke} />
            <span className="hidden xs:inline">Filtre</span>
          </button>

          <button
            onClick={() => navigate('/dashboard/unpaid-orders')}
            className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-200 flex-1 sm:flex-none justify-center sm:justify-start"
          >
            <IconSearch stroke={iconStroke} />
            <span className="hidden xs:inline">Açık Borç Bulucu</span>
          </button>

          <button
            onClick={handlePrintReport}
            className="bg-restro-primary text-black px-4 py-2 rounded-lg flex items-center gap-2 flex-1 sm:flex-none justify-center sm:justify-start"
          >
            <IconPrinter size={20} stroke={iconStroke} />
            <span className="hidden xs:inline">Raporu Yazdır</span>
          </button>
        </div>
      </div>

      <h3 className="mt-6 mb-4 text-base text-center sm:text-left">{filters.find(f=>f.key==state.filter).value} Tarihli Raporlar Gösteriliyor</h3>

      <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 mt-6">
         {/* popular items */}
         <div className='row-span-3 border border-restro-border-green-light rounded-3xl h-80 sm:h-96 overflow-y-auto'>
          <div className='py-4 sm:py-5 px-4 sm:px-6 bg-white/80 backdrop-blur rounded-t-3xl sticky top-0 z-10'>
            <h3 className='font-bold'>Çok Satan Ürünler</h3>
          </div>

          {topSellingItems?.length == 0 && <div className='w-full flex items-center justify-center flex-col px-4 sm:px-8 2xl:px-10'>
            <img
              src="/assets/illustrations/no-popular-items.svg"
              alt="no-top-selling items"
              className='w-8/12 mx-auto mt-10'
            />
            <p className='text-center text-gray-500 text-sm sm:text-base'>Seçilen Filtre için En Çok Satan ürün bulunamadı!</p>
          </div>}

          {
            topSellingItems?.length > 0 && <div className='px-4 sm:px-6 flex flex-col'>
            {/* item */}
            {topSellingItems.map((item,i)=>{
              return <div className='mb-3 sm:mb-4 flex items-center gap-2 w-full' key={i}>
                <div className="bg-gray-100 text-gray-500 flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-lg">
                  <IconCarrot stroke={iconStroke} size={18} />
                </div>

                <div className='flex-1 min-w-0'>
                  <p className="truncate">{item?.title}</p>
                  <p className="text-xs text-gray-500">{currency||""}{formatNumber(item?.price)}</p>
                </div>

                <p className='font-bold'>{item?.orders_count}</p>
              </div>;
            })}
            {/* item */}
          </div>
          }
        </div>
        {/* popular items */}


        {/* items sold */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Siparişler</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{ordersCount || 0}</p>
        </div>
        {/* items sold */}

        {/* avg. order value */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Ortalama Sipariş Değeri</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{formatNumber(averageOrderValue)} {currency}</p>
        </div>
        {/* avg. order value */}

        {/* total customers */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Toplam Müşteri</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{totalCustomers}</p>
        </div>
        {/* total customers */}

        {/* New customers */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Yeni Müşteri</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{newCustomers}</p>
        </div>
        {/* New customers */}

        {/* repeat customers */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Tekrarlayen Müşteri</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{repeatedCustomers}</p>
        </div>
        {/* repeat customers */}

        {/* revenue */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Satış (Tüm Dahil)</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{formatNumber(revenueTotal)} {currency}</p>
        </div>
        {/* revenue */}

        {/* net sales */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Satış (Tüm Hariç)</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{formatNumber(netRevenue)} {currency}</p>
        </div>
        {/* net sales */}

        {/* net sales */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Cari Toplamı</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{formatNumber(creditTotal.total)} {currency}</p>
        </div>
        {/* net sales */}

        {/* Waste Total */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">Zayi Toplamı</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{formatNumber(wasteTotal.total)} {currency}</p>
        </div>
        {/* Waste Total */}

        {/* Complimentary Total */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">İkram Toplamı</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{formatNumber(complimentaryTotal.total)} {currency}</p>
        </div>
        {/* Complimentary Total */}

        {/* tax */}
        <div className="border border-restro-border-green-light rounded-3xl h-24 sm:h-28 py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold text-sm sm:text-base">İndirim Toplamı</h3>
          <p className="mt-1 sm:mt-2 text-2xl sm:text-4xl">{formatNumber(getTotalDiscounts.total)} {currency}</p>
        </div>
        {/* tax */}

        {/* Cancelled Orders and Items */}
        <div className="border border-restro-border-green-light rounded-3xl py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold mb-2 sm:mb-4 text-sm sm:text-base">İptal Edilen Siparişler ve Ürünler</h3>
          {cancelledOrdersAndItems?.length === 0 && <p className="text-sm sm:text-base">İptal edilmiş sipariş bulunamadı.</p>}

        </div>
        {/* Cancelled Orders and Items */}



        {!activeCashRegisterSessions || activeCashRegisterSessions.length === 0 ? (
          <div className="col-span-3 border border-restro-border-green-light rounded-3xl py-3 sm:py-5 px-4 sm:px-6">
            <p className="text-gray-500 text-sm sm:text-base">Açık kasa oturumu bulunmuyor.</p>
          </div>
        ) : (
          /* Her kasa için ayrı kart */
          activeCashRegisterSessions.map((session, index) => (
            <div
              key={index}
              className="col-span-3 md:col-span-1 border border-restro-border-green-light rounded-3xl py-3 sm:py-5 px-4 sm:px-6 hover:bg-gray-50 cursor-pointer transition-colors"
              onClick={() => window.location.href = `/dashboard/cash-register-session/${session.id}`}
            >
              <div className="flex items-center gap-2 mb-3 sm:mb-4">
                <IconCash size={20} stroke={iconStroke} className="text-gray-700" />
                <h3 className="font-bold text-base sm:text-lg truncate">{session.register_name}</h3>
              </div>

              <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
                <div>
                  <p className="text-gray-500 text-xs sm:text-sm">Açılış Zamanı</p>
                  <p className="font-medium text-sm sm:text-base truncate">{formatDate(session.opened_at)}</p>
                </div>
                <div>
                  <p className="text-gray-500 text-xs sm:text-sm">Açan Kullanıcı</p>
                  <p className="font-medium text-sm sm:text-base truncate">{session.opener_name}</p>
                </div>
                <div>
                  <p className="text-gray-500 text-xs sm:text-sm">Açılış Tutarı</p>
                  <p className="font-medium text-sm sm:text-base">{formatNumber(session.opening_amount)} {currency}</p>
                </div>
                <div>
                  <p className="text-gray-500 text-xs sm:text-sm">Toplam İşlem</p>
                  <p className="font-bold text-base sm:text-lg">{formatNumber(session.total_transactions)} {currency}</p>
                </div>
              </div>

              <div className="border-t pt-3 sm:pt-4">
                <p className="text-gray-500 text-xs sm:text-sm mb-2">Ödeme Türleri</p>
                {session.payment_totals_by_type && session.payment_totals_by_type.length > 0 ? (
                  <div className="grid grid-cols-1 xs:grid-cols-2 gap-2">
                    {session.payment_totals_by_type.map((payment, paymentIndex) => (
                      <div key={paymentIndex} className="flex justify-between items-center bg-gray-50 p-2 rounded-lg">
                        <span className="text-gray-700 text-xs sm:text-sm truncate mr-1">{payment.payment_type_name}</span>
                        <span className="font-bold text-xs sm:text-sm whitespace-nowrap">{formatNumber(payment.total_amount)} {currency}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-xs sm:text-sm">İşlem yok</p>
                )}
              </div>
            </div>
          ))
        )}

        {/* open orders */}
        <div className="border border-restro-border-green-light rounded-3xl py-3 sm:py-5 px-4 sm:px-6">
          <h3 className="font-bold mb-2 sm:mb-4 text-sm sm:text-base">Açık Siparişler</h3>

          {/* Open Orders sections */}
          {/* Dine-in Orders */}
          {openOrders?.dinein?.length > 0 && (
            <div>
              <h4 className="font-semibold mb-1 sm:mb-2 text-xs sm:text-sm">Masa Siparişler</h4>
              {openOrders.dinein.map((order, i) => (
                <div key={i} className="flex justify-between py-1 sm:py-2 border-b">
                  <span className="text-sm sm:text-base truncate max-w-[60%]">{order.table_title || 'Masa'}</span>
                  <span className="text-gray-500 text-sm sm:text-base">{formatNumber(order.order_total)} {currency}</span>
                </div>
              ))}
            </div>
          )}

          {/* Takeaway Orders */}
          {openOrders?.takeaway?.length > 0 && (
            <div>
              <h4 className="font-semibold mt-3 sm:mt-4 mb-1 sm:mb-2 text-xs sm:text-sm">Hızlı Satış Siparişler</h4>
              {openOrders.takeaway.map((order, i) => (
                <div key={i} className="flex justify-between py-1 sm:py-2 border-b">
                  <span className="text-sm sm:text-base truncate max-w-[60%]">{order.customer_name || 'Müşteri'}</span>
                  <span className="text-gray-500 text-sm sm:text-base">{formatNumber(order.order_total)} {currency}</span>
                </div>
              ))}
            </div>
          )}

          {/* Delivery Orders */}
          {openOrders?.delivery?.length > 0 && (
            <div>
              <h4 className="font-semibold mt-3 sm:mt-4 mb-1 sm:mb-2 text-xs sm:text-sm">Paket Siparişler</h4>
              {openOrders.delivery.map((order, i) => (
                <div key={i} className="flex justify-between py-1 sm:py-2 border-b">
                  <span className="text-sm sm:text-base truncate max-w-[60%]">{order.customer_name || 'Müşteri'}</span>
                  <span className="text-gray-500 text-sm sm:text-base">{formatNumber(order.order_total)} {currency}</span>
                </div>
              ))}
            </div>
          )}

          {/* Unknown Orders */}
          {openOrders?.unknown?.length > 0 && (
            <div>
              <h4 className="font-semibold mt-3 sm:mt-4 mb-1 sm:mb-2 text-xs sm:text-sm">Diğer Siparişler</h4>
              {openOrders.unknown.map((order, i) => (
                <div key={i} className="flex justify-between py-1 sm:py-2 border-b">
                  <span className="text-sm sm:text-base truncate max-w-[60%]">{order.customer_name || 'Müşteri'}</span>
                  <span className="text-gray-500 text-sm sm:text-base">{formatNumber(order.order_total)} {currency}</span>
                </div>
              ))}
            </div>
          )}

          {/* Eğer hiç açık sipariş yoksa */}
          {(!openOrders?.dinein?.length && !openOrders?.takeaway?.length && !openOrders?.delivery?.length && !openOrders?.unknown?.length) && (
            <p className="text-sm sm:text-base text-gray-500">Açık sipariş bulunmuyor.</p>
          )}
        </div>
        {/* open orders */}
      </div>

      {/* Garson Satış Raporu */}
      {waiterSalesReport && waiterSalesReport.length > 0 && (
        <div className="mt-6">
          <h2 className="text-xl font-bold mb-4">Garson Satış Raporu</h2>
          <div className="grid grid-cols-1 gap-4">
            {waiterSalesReport.map((waiter, index) => (
              <div key={index} className="border border-restro-border-green-light rounded-3xl p-4 sm:p-6">
                {/* Garson Bilgileri */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 pb-4 border-b">
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">{waiter.waiter_name}</h3>
                    <p className="text-sm text-gray-500">{waiter.waiter_username}</p>
                  </div>
                  <div className="grid grid-cols-3 gap-4 mt-3 sm:mt-0">
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Sipariş</p>
                      <p className="text-lg font-bold">{waiter.total_orders}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Ürün</p>
                      <p className="text-lg font-bold">{waiter.total_items_sold}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Toplam</p>
                      <p className="text-lg font-bold">{formatNumber(waiter.total_sales)} {currency}</p>
                    </div>
                  </div>
                </div>

                {/* Ürün Detayları */}
                {waiter.item_details && waiter.item_details.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3 text-gray-700">Satılan Ürünler</h4>
                    <div className="space-y-2">
                      {waiter.item_details.map((item, itemIndex) => (
                        <div key={itemIndex} className="bg-gray-50 rounded-lg p-3">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="flex-1 min-w-0 mb-2 sm:mb-0">
                              <div className="flex items-center gap-2">
                                <h5 className="font-medium text-gray-800 truncate">{item.item_title}</h5>
                                {item.variant_title && (
                                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                    {item.variant_title}
                                  </span>
                                )}
                              </div>
                              <p className="text-sm text-gray-500">
                                Birim: {formatNumber(item.price)} {currency}
                              </p>
                            </div>
                            <div className="grid grid-cols-3 gap-3 text-center sm:text-right">
                              <div>
                                <p className="text-xs text-gray-500">Adet</p>
                                <p className="font-bold">{item.total_quantity}</p>
                              </div>
                              <div>
                                <p className="text-xs text-gray-500">Sipariş</p>
                                <p className="font-bold">{item.order_count}</p>
                              </div>
                              <div>
                                <p className="text-xs text-gray-500">Tutar</p>
                                <p className="font-bold text-green-600">{formatNumber(item.total_amount)} {currency}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* filter dialog */}
      <dialog id="filter-dialog" className="modal">
        <div className="modal-box w-11/12 max-w-md">
          <h3 className="font-bold text-base sm:text-lg flex items-center gap-2">
            <IconFilter stroke={iconStroke} /> Filtre
          </h3>
          {/* filters */}
          <div className="my-4">
            <div>
              <label className="block text-gray-500 text-xs sm:text-sm">Filtre</label>
              <select
                className="select select-sm select-bordered w-full text-sm"
                ref={filterTypeRef}
              >
                {filters.map((filter, index) => (
                  <option key={index} value={filter.key}>
                    {filter.value}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 mt-4">
              <div className="flex-1">
                <label
                  htmlFor="fromDate"
                  className="block text-gray-500 text-xs sm:text-sm"
                >
                  Başlangıç Tarihi
                </label>
                <input
                  defaultValue={defaultDateFrom}
                  type="date"
                  ref={fromDateRef}
                  className="text-xs sm:text-sm w-full border rounded-lg px-3 sm:px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
              <div className="flex-1">
                <label
                  htmlFor="fromTime"
                  className="block text-gray-500 text-xs sm:text-sm"
                >
                  Başlangıç Saati
                </label>
                <input
                  defaultValue={defaultTimeFrom}
                  type="time"
                  ref={fromTimeRef}
                  className="text-xs sm:text-sm w-full border rounded-lg px-3 sm:px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 mt-4">
              <div className="flex-1">
                <label
                  htmlFor="toDate"
                  className="block text-gray-500 text-xs sm:text-sm"
                >
                  Bitiş Tarihi
                </label>
                <input
                  defaultValue={defaultDateTo}
                  type="date"
                  ref={toDateRef}
                  className="text-xs sm:text-sm w-full border rounded-lg px-3 sm:px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
              <div className="flex-1">
                <label
                  htmlFor="toTime"
                  className="block text-gray-500 text-xs sm:text-sm"
                >
                  Bitiş Saati
                </label>
                <input
                  defaultValue={defaultTimeTo}
                  type="time"
                  ref={toTimeRef}
                  className="text-xs sm:text-sm w-full border rounded-lg px-3 sm:px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
            </div>
          </div>
          {/* filters */}
          <div className="modal-action">
            <form method="dialog" className="flex w-full gap-2">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn btn-sm sm:btn-md flex-1">Kapat</button>
              <button
                onClick={() => {
                  setState({
                    ...state,
                    filter: filterTypeRef.current.value,
                    fromDate: fromDateRef.current.value || null,
                    toDate: toDateRef.current.value || null,
                    fromTime: fromTimeRef.current.value || null,
                    toTime: toTimeRef.current.value || null,
                  });
                }}
                className="btn btn-sm sm:btn-md flex-1 bg-restro-primary text-black"
              >
                Uygula
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* filter dialog */}
    </Page>
  );
}