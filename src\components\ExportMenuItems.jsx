import React from "react";
import * as XLSX from "xlsx";
import { toast } from "react-hot-toast";

const ExportMenuItems = ({ menuItems }) => {
  const handleExport = () => {
    try {

      // Ürünler için veri formatı
      const menuItemData = menuItems.map((item) => ({
        "Ürün ID": item.id,
        "Ürün Başlığı": item.title,
        "Açıklama": item.description || "",
        "Fiyat": item.price,
        "Net Fiyat": item.net_price || "",
        "Kategori ID": item.category_id,
        "Vergi ID": item.tax_id || "",
        "Stok": item.stock || 0,
        "Stok Aktif Mi": item.is_stock_active ? "Evet" : "Hayır",
        "Resim URL": item.image || "", // Eğer resim varsa ekleyin
      }));

      const workbook = XLSX.utils.book_new();
      const menuItemSheet = XLSX.utils.json_to_sheet(menuItemData);

      XLSX.utils.book_append_sheet(workbook, menuItemSheet, "Ürünler");

      // Dosyayı oluştur ve indir
      XLSX.writeFile(workbook, "Menu_SewPos.xlsx");
      toast.success("Excel dosyası başarıyla indirildi!");
    } catch (error) {
      console.error("Excel oluşturma hatası:", error);
      toast.error("Excel oluşturulurken bir hata oluştu!");
    }
  };

  return (
    <button
      onClick={handleExport}
      className="btn bg-blue-500 text-white hover:bg-blue-600 px-4 py-2 rounded-lg"
    >
      Excel İndir
    </button>
  );
};

export default ExportMenuItems;
