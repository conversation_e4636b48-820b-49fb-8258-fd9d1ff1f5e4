import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data.data);

/**
 * Hook to fetch all cash registers
 */
export function useCashRegisters() {
  const APIURL = `cash-register/cash-registers`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

/**
 * Hook to fetch a specific cash register by ID
 */
export function useCashRegister(id) {
  const APIURL = `cash-register/cash-registers/${id}`;
  const { data, error, isLoading, mutate } = useSWR(id ? APIURL : null, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

/**
 * Hook to fetch user's active cash register session
 */
export function useUserActiveSession() {
  const APIURL = `cash-register/user/active-session`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

/**
 * Hook to fetch cash register sessions with optional filters
 */
export function useCashRegisterSessions(filters = {}) {
  let queryParams = new URLSearchParams();

  if (filters.status) queryParams.append('status', filters.status);
  if (filters.cashRegisterId) queryParams.append('cashRegisterId', filters.cashRegisterId);
  if (filters.username) queryParams.append('username', filters.username);
  if (filters.startDate) queryParams.append('startDate', filters.startDate);
  if (filters.endDate) queryParams.append('endDate', filters.endDate);

  const APIURL = `cash-register/cash-register-sessions?${queryParams.toString()}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);

  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

/**
 * Hook to fetch a specific cash register session by ID
 */
export function useCashRegisterSession(id) {
  const APIURL = `cash-register/cash-register-sessions/${id}`;
  const { data, error, isLoading, mutate } = useSWR(id ? APIURL : null, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

/**
 * Add a new cash register
 */
export async function addCashRegister(registerData) {
  try {
    const response = await ApiClient.post('cash-register/cash-registers', registerData);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Update an existing cash register
 */
export async function updateCashRegister(id, registerData) {
  try {
    const response = await ApiClient.put(`cash-register/cash-registers/${id}`, registerData);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Delete a cash register
 */
export async function deleteCashRegister(id) {
  try {
    const response = await ApiClient.delete(`cash-register/cash-registers/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Open a new cash register session
 */
export async function openCashRegisterSession(sessionData) {
  try {
    const response = await ApiClient.post('cash-register/cash-register-sessions/open', sessionData);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Close an existing cash register session
 */
export async function closeCashRegisterSession(id, closeData) {
  try {
    const response = await ApiClient.post(`cash-register/cash-register-sessions/${id}/close`, closeData);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Add a payment transaction
 */
export async function addPaymentTransaction(transactionData) {
  try {
    const response = await ApiClient.post('cash-register/payment-transactions', transactionData);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Make a cash withdrawal from the register
 */
export async function makeCashWithdrawal(withdrawalData) {
  try {
    const response = await ApiClient.post('cash-register/cash-withdrawals', withdrawalData);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Make a cash deposit to the register
 */
export async function makeCashDeposit(depositData) {
  try {
    const response = await ApiClient.post('cash-register/cash-deposits', depositData);
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * Hook to fetch payment transactions with optional filters
 */
export function usePaymentTransactions(filters = {}) {
  let queryParams = new URLSearchParams();

  if (filters.paymentTypeId) queryParams.append('paymentTypeId', filters.paymentTypeId);
  if (filters.startDate) queryParams.append('startDate', filters.startDate);
  if (filters.endDate) queryParams.append('endDate', filters.endDate);
  if (filters.type) queryParams.append('type', filters.type);

  const APIURL = `cash-register/payment-transactions?${queryParams.toString()}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);

  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

/**
 * Hook to fetch all active cash register sessions
 */
export function useActiveCashRegisterSessions() {
  const APIURL = `cash-register/cash-register-sessions?status=open`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher, {
    // Otomatik yeniden veri çekmeyi devre dışı bırak
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    refreshInterval: 0,
    dedupingInterval: 60000 // 1 dakika içinde aynı isteği tekrar etme
  });
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}
