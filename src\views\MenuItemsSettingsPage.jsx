import React, { useRef, useState, useEffect } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { IconCarrot, IconPencil, IconPlus, IconTrash } from "@tabler/icons-react";
import { mutate } from "swr";
import Page from "../components/Page";
import { useCategories, useTaxes } from "../controllers/settings.controller";
import {
  addMenuItem,
  deleteMenuItem,
  updateCategoryOrder,
  updateMenuItemOrder,
  useMenuItems,
} from "../controllers/menu_item.controller";
import { iconStroke } from "../config/config";
import { Link, useNavigate } from "react-router-dom";
import SortableItem from "../components/SortableItem";
import { toast } from "react-hot-toast";
import BulkUploadMenuItems from "../components/BulkUploadMenuItems";
import ExportMenuItems from "../components/ExportMenuItems";
import ExportMenuPDF from "../components/ExportMenuPDF";
import BulkUpdateMenuItems from "../components/BulkUpdateMenuItems";


export default function MenuItemsSettingsPage() {
  const navigate = useNavigate();
  const titleRef = useRef();
  const descriptionRef = useRef();
  const priceRef = useRef();
  const netPriceRef = useRef();
  const taxIdRef = useRef();
  const categoryIdRef = useRef();

  const [expandedCategory, setExpandedCategory] = useState(null);

  // URL parametrelerini kontrol et
  useEffect(() => {
    // URL'den action parametresini al
    const queryParams = new URLSearchParams(window.location.search);
    const action = queryParams.get('action');

    // localStorage'dan modal açma bilgisini kontrol et
    const storedAction = localStorage.getItem('openModal');

    // Eğer action=add parametresi varsa veya localStorage'da kayıtlıysa, modalı aç
    if (action === 'add' || storedAction === 'add') {
      // Modalı aç
      document.getElementById('modal-add').showModal();

      // localStorage'dan temizle
      localStorage.removeItem('openModal');

      // URL'den parametreyi temizle (opsiyonel)
      if (action === 'add') {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }, []);

  const { APIURL: APIURLCategories, data: categories = [], error: errorCategories, isLoading: isLoadingCategories } = useCategories();


  const { APIURL: APIURLTaxes, data: taxes, error: errorTaxes, isLoading: isLoadingTaxes } = useTaxes();

  const { APIURL, data: menuItems, error, isLoading } = useMenuItems();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  if (isLoadingCategories || isLoadingTaxes || isLoading) {
    return <Page>Lütfen bekleyin...</Page>;
  }

  if (errorCategories || errorTaxes || error) {
    return <Page>Error loading details! Please Try Later!</Page>;
  }

  async function btnAdd() {
    const title = titleRef.current.value;
    const description = descriptionRef.current.value || null;
    const price = priceRef.current.value;
    const netPrice = netPriceRef.current.value || null;
    const categoryId = categoryIdRef.current.value || null;
    const taxId = taxIdRef.current.value || null;

    if (!title) {
      toast.error("Please enter title!");
      return;
    }

    if (price < 0) {
      toast.error("Please provide valid price!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await addMenuItem(title, description, price, netPrice, categoryId, taxId);

      if (res.status === 200) {
        titleRef.current.value = "";
        descriptionRef.current.value = "";
        priceRef.current.value = "";
        netPriceRef.current.value = "";
        categoryIdRef.current.value = "";
        taxIdRef.current.value = "";

        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  }

  const btnDelete = async (id) => {
    const isConfirm = window.confirm("Emin misin! Bu süreç geri döndürülemez!");

    if (!isConfirm) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteMenuItem(id);

      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = categories.findIndex((cat) => cat.id === active.id);
      const newIndex = categories.findIndex((cat) => cat.id === over.id);

      const updatedCategories = arrayMove(categories, oldIndex, newIndex);

      // Hemen UI'yı güncelle
      mutate(APIURLCategories, updatedCategories, false);

      try {
        await updateCategoryOrder(
          updatedCategories.map((cat, index) => ({
            id: cat.id,
            order: index + 1, // Yeni sıralama
          }))
        );

        toast.success("Kategori sıralaması güncellendi!");
      } catch (error) {
        console.error(error);
        toast.error("Kategori sıralaması güncellenirken hata oluştu!");
      } finally {
        // Gerçek veriyi yeniden al
        mutate(APIURLCategories);
      }
    }
  };

  const handleMenuItemDragEnd = async (event, categoryId) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      // Sadece aynı kategorideki ürünleri filtrele
      const categoryItems = menuItems.filter(item => item.category_id === categoryId);

      const oldIndex = categoryItems.findIndex(item => item.id === active.id);
      const newIndex = categoryItems.findIndex(item => item.id === over.id);

      const updatedItems = arrayMove(categoryItems, oldIndex, newIndex);

      // Hemen UI'yı güncelle (tüm menü öğelerini güncelle)
      const updatedMenuItems = [...menuItems];

      // Kategorideki öğeleri güncelle
      updatedItems.forEach((item, index) => {
        const itemIndex = updatedMenuItems.findIndex(mi => mi.id === item.id);
        if (itemIndex !== -1) {
          updatedMenuItems[itemIndex] = { ...updatedMenuItems[itemIndex], order: index + 1 };
        }
      });

      mutate(APIURL, updatedMenuItems, false);

      try {
        // API'ye yeni sıralamayı gönder
        await updateMenuItemOrder(
          updatedItems.map((item, index) => ({
            id: item.id,
            order: index + 1, // Yeni sıralama
          }))
        );

        toast.success("Ürün sıralaması güncellendi!");
      } catch (error) {
        console.error(error);
        toast.error("Ürün sıralaması güncellenirken hata oluştu!");
      } finally {
        // Gerçek veriyi yeniden al
        mutate(APIURL);
      }
    }
  };



  return (
    <Page className="px-8 py-6">
      <div className="flex md:items-center flex-col md:flex-row gap-2">
        <h3 className="text-3xl font-light mr-6">Menü</h3>
        <button
          onClick={() => document.getElementById("modal-add").showModal()}
          className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1 mr-4 w-fit"
        >
          <IconPlus size={22} stroke={iconStroke} /> Yeni
        </button>
        <Link
          to="categories"
          className="w-fit rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-3 py-1"
        >
          Ürün Kategorileri
        </Link>
        <Link
          to="/dashboard/quick-edit-menu-item"
          className="w-fit rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-3 py-1 flex items-center gap-1"
        >
          <IconPencil size={18} stroke={iconStroke} /> Hızlı Düzenleme
        </Link>

        <ExportMenuItems categories={categories} menuItems={menuItems} />
        <ExportMenuPDF categories={categories} menuItems={menuItems} />

      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext
          items={categories.map((category) => category.id)}
          strategy={rectSortingStrategy}
        >
          <div className="mt-8 w-full">
            {categories.map((category) => (
              <SortableItem key={category.id} id={category.id}>
                <div className="mb-4 border rounded-lg overflow-hidden">
                  <div
                    className="flex justify-between items-center bg-gray-100 px-4 py-2 cursor-pointer"
                    onClick={() =>
                      setExpandedCategory((prev) =>
                        prev === category.id ? null : category.id
                      )
                    }
                  >
                    <h4 className="font-bold text-lg">{category.title}</h4>
                  </div>

                  {expandedCategory === category.id && (
                    <div className="w-full p-4">
                      <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={(event) => handleMenuItemDragEnd(event, category.id)}
                      >
                        <SortableContext
                          items={menuItems
                            .filter((item) => item.category_id === category.id)
                            .map((item) => item.id)}
                          strategy={rectSortingStrategy}
                        >
                          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                            {menuItems
                              .filter((item) => item.category_id === category.id)
                              .map((menuItem) => (
                                <SortableItem key={menuItem.id} id={menuItem.id}>
                                  <div
                                    className="flex items-center justify-between border px-4 py-3 rounded-2xl gap-2 text-sm w-full"
                                  >
                                    <div>
                                      <p className="font-semibold">{menuItem.title}</p>
                                      <p className="text-gray-500 text-sm">
                                        {menuItem.price} ₺
                                      </p>
                                    </div>
                                    <div className="flex gap-2">
                                      <button
                                        onClick={() => navigate(`/dashboard/menu-items/${menuItem.id}`)}
                                        className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-100 transition active:scale-95"
                                      >
                                        <IconPencil stroke={iconStroke} />
                                      </button>
                                      <button
                                        onClick={() => btnDelete(menuItem.id)}
                                        className="w-8 h-8 rounded-full flex items-center justify-center text-red-500 hover:bg-gray-100 transition active:scale-95"
                                      >
                                        <IconTrash stroke={iconStroke} />
                                      </button>
                                    </div>
                                  </div>
                                </SortableItem>
                              ))}
                          </div>
                        </SortableContext>
                      </DndContext>
                    </div>
                  )}
                </div>
              </SortableItem>
            ))}
          </div>
        </SortableContext>
      </DndContext>

      <BulkUploadMenuItems />

      <BulkUpdateMenuItems />

      {/* add dialog */}
      <dialog id="modal-add" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Ürün Ekle</h3>

          <div className="mt-4">
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">
              Başlık
            </label>
            <input
              ref={titleRef}
              type="text"
              name="title"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Ürün adını girin"
            />
          </div>

          <div className="mt-4">
            <label
              htmlFor="description"
              className="mb-1 block text-gray-500 text-sm"
            >
              Açıklama
            </label>
            <input
              ref={descriptionRef}
              type="text"
              name="description"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Ürün açıklamasını giriniz.."
            />
          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label
                htmlFor="price"
                className="mb-1 block text-gray-500 text-sm"
              >
                Fiyat
              </label>
              <input
                ref={priceRef}
                type="number"
                name="price"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Ürün fiyatı"
              />
            </div>
            <div className="flex-1">
              <label
                htmlFor="nprice"
                className="mb-1 block text-gray-500 text-sm"
              >
                Net Fiyat
              </label>
              <input
                ref={netPriceRef}
                type="number"
                name="nprice"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Net ürün fiyatı"
              />
            </div>
          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label
                htmlFor="category"
                className="mb-1 block text-gray-500 text-sm"
              >
                Kategori
              </label>
              <select
                ref={categoryIdRef}
                name="category"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Select Category"
              >
                <option value="">Yok</option>
                {categories.map((category) => (
                  <option value={category.id} key={category.id}>{category.title}</option>
                ))}
              </select>
            </div>
            <div className="flex-1">
              <label
                htmlFor="tax"
                className="mb-1 block text-gray-500 text-sm"
              >
                Vergi
              </label>
              <select
                ref={taxIdRef}
                name="tax"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Select Tax"
              >
                <option value="">Yok</option>
                {taxes.map((tax) => (
                  <option value={tax.id} key={tax.id}>{tax.title} - {tax.rate}% ({tax.type})</option>
                ))}
              </select>
            </div>
          </div>

          <div className="modal-action">
            <form method="dialog">
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                onClick={() => {
                  btnAdd();
                }}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>
    </Page>
  );
}
