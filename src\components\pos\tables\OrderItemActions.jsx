import React, { useState } from "react";
import { IconP<PERSON>cil, IconGift, IconTrash } from "@tabler/icons-react";

const OrderItemActions = ({
  orderItem,
  currency,
  onUpdatePrice,
  onMarkAsComplimentary,
  onMarkAsZayi,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [price, setPrice] = useState(orderItem.price);

  const handleSave = async () => {
    await onUpdatePrice(orderItem.id, price);
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <div className="flex flex-col items-end gap-3">
        <div className="relative">
          <input
            type="number"
            value={price}
            onChange={(e) => setPrice(e.target.value)}
            className="w-32 p-2 pl-8 border border-gray-300 rounded-lg text-right text-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500">
            {currency}
          </span>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1.5 rounded-lg text-lg transition-colors"
          >
            Kaydet
          </button>
          <button
            onClick={() => setIsEditing(false)}
            className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-1.5 rounded-lg text-lg transition-colors"
          >
            İptal
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex gap-2">
      <button
        onClick={() => onMarkAsComplimentary(orderItem.id)}
        className="btn btn-sm bg-green-50 hover:bg-green-100 text-green-600 border-green-200"
        title="İkram Et"
      >
        <IconGift size={18} /> <span className="hidden sm:inline">İkram</span>
      </button>
      <button
        onClick={() => onMarkAsZayi(orderItem.id)}
        className="btn btn-sm bg-red-50 hover:bg-red-100 text-red-600 border-red-200"
        title="Zayi"
      >
        <IconTrash size={18} /> <span className="hidden sm:inline">Zayi</span>
      </button>
    </div>
  );
};

export default OrderItemActions;
