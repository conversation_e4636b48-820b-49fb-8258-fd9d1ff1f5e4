import React, { useContext, useEffect, useRef, useState } from "react";
import Page from "../components/Page";
import {
  cancelKitchenOrder,
  completeKitchenOrder,
  getCompleteOrderPaymentSummary,
  getOrders,
  getOrdersInit,
  payAndCompleteKitchenOrder,
  updateKitchenOrderItemStatus,
} from "../controllers/orders.controller";
import { getCancellationReasons } from "../controllers/reasons.controller";
import { savePrintDataRecipet } from "../controllers/pos.controller";

import { toast } from "react-hot-toast";
import {
  IconArmchair,
  IconBoxSeam,
  IconCash,
  IconCheck,
  IconChecks,
  IconClock,
  IconDotsVertical,
  IconReceipt,
  IconRefresh,
  IconX,
} from "@tabler/icons-react";
import { VITE_BACKEND_SOCKET_IO, iconStroke } from "../config/config";
import { CURRENCIES } from "../config/currencies.config";
import { PAYMENT_ICONS } from "../config/payment_icons";
import { SocketContext } from "../contexts/SocketContext";
import { initSocket } from "../utils/socket";
import { textToSpeech } from "../utils/textToSpeech";
import { getUserDetailsInLocalStorage } from "../helpers/UserDetails";
import { useNavbarVisibility } from '../contexts/NavbarVisibilityContext';


export default function OrdersPage() {
  const user = getUserDetailsInLocalStorage();
  const { socket, isSocketConnected } = useContext(SocketContext);

  const { setShowNavbar } = useNavbarVisibility();

  useEffect(() => {
    setShowNavbar(false);
    return () => setShowNavbar(true); // Cleanup function to show navbar when leaving the page
  }, [setShowNavbar]);


  const [state, setState] = useState({
    kitchenOrders: [],
    printSettings: null,
    storeSettings: null,
    paymentTypes: [],
    isLoading: true,

    cancelOrderIds: [],
    selectedCancelReasonId: null, // İptal nedeni ID'si için yeni alan
    selectedOrderItemId: null, // Seçilen sipariş ürünü ID'si
    selectedOrderItemStatus: null, // Seçilen sipariş ürünü durumu
    completeOrderIds: [],
    completeTokenIds: "",

    currency: null,

    summaryNetTotal: 0,
    summaryTaxTotal: 0,
    summaryTotal: 0,
    summaryOrders: [],
    order: null,

    selectedPaymentType: null,
  });

  // İptal nedenleri için state
  const [cancellationReasons, setCancellationReasons] = useState([]);

  useEffect(() => {
    _init();
    _initSocket();
    setShowNavbar(false);

  }, []);

  const {
    kitchenOrders,
    printSettings,
    storeSettings,
    paymentTypes,
    isLoading,
    currency,
  } = state;

  const _init = async () => {
    try {
      const [ordersResponse, ordersInitResponse, reasonsResponse] = await Promise.all([
        getOrders(),
        getOrdersInit(),
        getCancellationReasons(), // İptal nedenlerini yükle
      ]);

      if (ordersResponse.status == 200 && ordersInitResponse.status == 200) {
        const orders = ordersResponse?.data || [];
        const ordersInit = ordersInitResponse.data;

        const currency = CURRENCIES.find(
          (c) => c.cc == ordersInit?.storeSettings?.currency
        );

        setState({
          ...state,
          kitchenOrders: orders,
          printSettings: ordersInit.printSettings || {},
          storeSettings: ordersInit.storeSettings || {},
          paymentTypes: ordersInit.paymentTypes || {},
          currency: currency?.symbol,
          isLoading: false,
        });

        // İptal nedenlerini state'e kaydet
        if (reasonsResponse && reasonsResponse.length > 0) {
          setCancellationReasons(reasonsResponse);
        }
      }
    } catch (error) {
      console.error(error);
      toast.dismiss();
      toast.error("Error loading orders! Please try later!");

      setState({
        ...state,
        isLoading: false,
      });
    }
  };

  const [activeTab, setActiveTab] = useState('dine-in');

  // Dine-in ve takeaway siparişlerini hesapla
  const dineInOrders = state.kitchenOrders.filter(order => order.table_id);
  const takeawayOrders = state.kitchenOrders.filter(order => !order.table_id);

  // Eğer dine-in sipariş yoksa ve aktif tab dine-in ise, otomatik olarak takeaway'e geç
  useEffect(() => {
    if (dineInOrders.length === 0 && activeTab === 'dine-in') {
      setActiveTab('takeaway');
    }
  }, [dineInOrders.length, activeTab]);

  // Filtreleme fonksiyonu
  const getFilteredOrders = () => {
    if (activeTab === 'dine-in') {
      return dineInOrders;
    } else {
      return takeawayOrders;
    }
  };

  const refreshOrders = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await getOrders();
      toast.dismiss();
      if (res.status == 200) {
        setState({
          ...state,
          kitchenOrders: res.data,
          isLoading: false,
        });
      }
    } catch (error) {
      const message =
        error.response.data.message ||
        "Error loading orders! Please try later!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
      setState({
        ...state,
        isLoading: false,
      });
    }
  };

  const _initSocket = () => {
    const audio = new Audio("/new_order_sound.mp3");
    if (isSocketConnected) {
      socket.emit("authenticate", user.tenant_id);
      socket.on("new_order", (payload) => {
        // textToSpeech(`New order received, token number: ${payload}`)
        audio.play();
        refreshOrders();
      });

      socket.on("order_update", () => {
        refreshOrders();
      });
    } else {
      initSocket();
      socket.emit("authenticate", user.tenant_id);
      socket.on("new_order", (payload) => {
        // textToSpeech(`New order received, token number: ${payload}`);
        audio.play();
        refreshOrders();
      });

      socket.on("order_update", () => {
        refreshOrders();
      });
    }
  };

  const sendOrderUpdateEvent = () => {
    const user = getUserDetailsInLocalStorage();

    if (isSocketConnected) {
      socket.emit("order_update_backend", {}, user.tenant_id);
    } else {
      // Handle disconnected state (optional)
      initSocket();
      socket.emit("order_update_backend", {}, user.tenant_id);
    }
  };

  if (state.isLoading) {
    return <Page>Lütfen bekleyin...</Page>;
  }

  // Sipariş ürünü durumunu değiştirmek için modal göster
  const showChangeOrderItemStatusModal = (orderItemId, status) => {
    // Eğer durum "cancelled" ise iptal nedeni seçme modalını göster
    if (status === "cancelled") {
      setState({
        ...state,
        selectedOrderItemId: orderItemId,
        selectedOrderItemStatus: status
      });
      document.getElementById("modal-order-item-cancel-reason").showModal();
    } else {
      // Diğer durumlar için direkt değiştir
      btnChangeOrderItemStatus(orderItemId, status);
    }
  };

  // Sipariş ürünü durumunu değiştir
  const btnChangeOrderItemStatus = async (orderItemId, status, reasonId = null) => {
    try {
      toast.loading("Lütfen bekleyin...");
      // İptal nedeni ID'sini de gönder
      const res = await updateKitchenOrderItemStatus(orderItemId, status, reasonId);
      toast.dismiss();
      if (res.status == 200) {
        sendOrderUpdateEvent();
        await refreshOrders();
        toast.success(res.data.message);
        document.getElementById("modal-order-item-status-update").showModal();

        // İptal işlemi tamamlandıktan sonra seçili değerleri sıfırla
        if (status === "cancelled") {
          setState(prev => ({
            ...prev,
            selectedOrderItemId: null,
            selectedOrderItemStatus: null,
            selectedCancelReasonId: null
          }));
        }
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  const btnShowCancelOrderModal = (orderIds) => {
    setState({
      ...state,
      cancelOrderIds: orderIds,
    });
    document.getElementById("modal-order-cancel").showModal();
  };
  const btnCancelOrder = async () => {
    try {
      // İptal nedeni seçilmemişse uyarı ver
      if (!state.selectedCancelReasonId) {
        toast.error("Lütfen bir iptal nedeni seçin!");
        return;
      }

      toast.loading("Lütfen bekleyin...");
      // İptal nedeni ID'sini de gönder
      const res = await cancelKitchenOrder(state.cancelOrderIds, state.selectedCancelReasonId);
      toast.dismiss();
      if (res.status == 200) {
        sendOrderUpdateEvent();
        await refreshOrders();
        toast.success(res.data.message);
        document.getElementById("modal-order-cancel").close();

        // İptal işlemi tamamlandıktan sonra seçili iptal nedenini sıfırla
        setState(prev => ({
          ...prev,
          selectedCancelReasonId: null
        }));
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  const btnShowCompleteOrderModal = (orderIds) => {
    setState({
      ...state,
      completeOrderIds: orderIds,
    });
    document.getElementById("modal-order-complete").showModal();
  };
  const btnCompleteOrder = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await completeKitchenOrder(state.completeOrderIds);
      toast.dismiss();
      if (res.status == 200) {
        sendOrderUpdateEvent();
        await refreshOrders();
        toast.success(res.data.message);
        document.getElementById("modal-order-complete").close();
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  const btnShowPayAndComplete = async (orderIds, order) => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await getCompleteOrderPaymentSummary(orderIds);
      toast.dismiss();

      if (res.status == 200) {
        const { subtotal, taxTotal, total, orders } = res.data;


        const tokenNoArray = orders.map(o=>o.token_no);
        const tokens = tokenNoArray.join(",");

        setState({
          ...state,
          summaryNetTotal: subtotal,
          summaryTaxTotal: taxTotal,
          summaryTotal: total,
          summaryOrders: orders,
          completeOrderIds: orderIds,
          completeTokenIds: tokens,
          order: order,
        });

        document.getElementById("modal-order-summary-complete").showModal();
      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  const btnPayAndComplete = async () => {

    if (!state.selectedPaymentType) {
         toast.error("Lütfen bir ödeme yöntemi seçin!");
         return; // Ödeme yöntemi seçilmediyse fonksiyonu durdur
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await payAndCompleteKitchenOrder(
        state.completeOrderIds,
        state.summaryNetTotal,
        state.summaryTaxTotal,
        state.summaryTotal,
        state.selectedPaymentType,
      );
      toast.dismiss();
      if (res.status == 200) {
        sendOrderUpdateEvent();
        await refreshOrders();
        toast.success(res.data.message);
        document.getElementById("modal-order-summary-complete").close();

          const { table_id, table_title, floor } = state.order;

          const orders = [];
          const orderIds = state.completeOrderIds.join(", ");

          for (const o of state.summaryOrders) {
            const items = o.items;
            items.forEach((i) => {
              const variant = i.variant_id
                ? {
                    id: i.variant_id,
                    title: i.variant_title,
                    price: i.variant_price,
                  }
                : null;
              orders.push({
                ...i,
                title: i.item_title,
                addons_ids:
                  i?.addons?.length > 0 ? i?.addons?.map((a) => a.id) : [],
                variant: variant,
              });
            });
          }

          const {
            customer_id,
            customer_type,
            customer_name,
            date,
            delivery_type,
          } = state.summaryOrders[0];

          const paymentType = paymentTypes.find((v)=>v.id == state.selectedPaymentType);
          let paymentMethodText;
          if(paymentType) {
            paymentMethodText = paymentType.title;
          }

          setState((prev)=>({...prev, selectedPaymentType: null}));


      }
    } catch (error) {
      const message =
        error?.response?.data?.message ||
        "Error processing your request, Please try later!";
      toast.dismiss();
      console.error(error);
      toast.error(message);
    }
  };

  const btnPrintReceipt = async (orderIdsArr, tokens) => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await getCompleteOrderPaymentSummary(orderIdsArr);
      toast.dismiss();

      if (res.status == 200) {
        const { subtotal, taxTotal, total, orders: ordersArr } = res.data;

        const orders = [];
        const orderIds = orderIdsArr.join(", ");

        // Ürünleri hazırla
        for (const o of ordersArr) {
          const items = o.items;
          items.forEach((i) => {
            const variant = i.variant_id
              ? {
                  id: i.variant_id,
                  title: i.variant_title,
                  price: i.variant_price,
                }
              : null;

            const addons = i.addons?.map(addon => ({
              title: addon.title,
              price: addon.price
            })) || [];

            orders.push({
              ...i,
              title: i.item_title,
              price: i.price,
              notes: i.notes,
              variant,
              addons
            });
          });
        }

        const {
          customer_id,
          customer_type,
          customer_name,
          date,
          delivery_type,
          table_title,
        } = ordersArr[0];

        // Print verilerini hazırla
        const printData = {
          storeName: storeSettings.store_name,
          table: table_title || '',  // Masa adını gönder
          orderId: orderIds,
          serverName: user.name,
          orderType: delivery_type,
          items: orders,
          tokenNo: tokens,
          customerType: customer_type,
          customer: { id: customer_id, name: customer_name },
          paymentTotal: total,
          subtotal: subtotal,
          taxTotal: taxTotal,
        };

        try {
          await savePrintDataRecipet(printData);
          toast.success("Yazdırma verileri gönderildi");
        } catch (error) {
          console.error("Yazdırma hatası:", error);
          toast.error("Yazıcıya ulaşılamıyor");
        }
      }
    } catch (error) {
    const message =
      error?.response?.data?.message ||
      "Error processing your request, Please try later!";
    toast.dismiss();
    console.error(error);
    toast.error(message);
  }
};

return (
  <Page>

    {/* Tab Menüsü - Dine-in sipariş varsa göster */}
    {dineInOrders.length > 0 && (
      <div className="flex gap-2">
        <button
          onClick={() => setActiveTab('dine-in')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition ${
            activeTab === 'dine-in'
              ? 'bg-restro-green text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          <IconArmchair size={20} stroke={iconStroke} />
          Masalar ({dineInOrders.length})
        </button>
        <button
          onClick={() => setActiveTab('takeaway')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition ${
            activeTab === 'takeaway'
              ? 'bg-restro-green text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }`}
        >
          <IconReceipt size={20} stroke={iconStroke} />
          Paket / Servis ({takeawayOrders.length})
        </button>
        <button
        onClick={refreshOrders}
        className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
      >
        <IconRefresh size={22} stroke={iconStroke} /> Yenile
      </button>
      </div>
    )}

    {/* Boş durum gösterimi */}
    {getFilteredOrders()?.length === 0 && (
      <div className="w-full h-[calc(100vh-25vh)] flex gap-4 flex-col items-center justify-center">
        <img
          src="/assets/illustrations/kitchen.svg"
          alt="no orders"
          className="w-full md:w-60"
        />
        <p className="text-gray-400">
          {activeTab === 'dine-in'
            ? 'Masalarda bekleyen sipariş yok!'
            : 'Bekleyen paket/servis siparişi yok!'}
        </p>
      </div>
    )}

    {/* Siparişlerin Başlığı ve Listesi */}
    {getFilteredOrders()?.length > 0 && (
      <>
        {/* Eğer dine-in sipariş yoksa sadece başlık göster */}
        {dineInOrders.length === 0 && (
          <div className="mt-6">
            <h4 className="text-xl font-medium text-gray-600">
              Açık Siparişler ({takeawayOrders.length})
            </h4>
          </div>
        )}

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4">
          {getFilteredOrders().map((order, index) => {
            const { table_id, table_title, floor, orders, order_ids } = order;

            const tokenNoArray = orders.map(o=>o.token_no);
            const tokens = tokenNoArray.join(",")

            const isPaid = orders.every((o)=>o.payment_status=='paid');

            return (
              <div
                key={index}
                className="border border-restro-border-green-light rounded-2xl px-4 py-5 flex flex-col divide-y divide-dashed"
              >
                <div className="flex items-center flex-col md:flex-row md:justify-between text-center gap-2 pb-2">
                  <div className="flex items-center gap-2">
                    <div className="flex w-12 h-12 rounded-full items-center justify-center bg-gray-100 text-gray-400">
                      {table_id ? (
                        <IconArmchair size={24} stroke={iconStroke} />
                      ) : (
                        <IconReceipt size={24} stroke={iconStroke} />
                      )}
                    </div>
                    <div>
                      <p className="font-bold">
                        {table_id ? `${table_title}` : "Hızlı Satış / Paket"}
                      </p>
                      {floor && <p className="text-sm">{floor}</p>}
                    </div>
                  </div>
                  <div className="dropdown dropdown-end">
                    <div
                      tabIndex={0}
                      role="button"
                      className="btn btn-sm btn-circle bg-transparent border-none shadow-none m-1"
                    >
                      <IconDotsVertical size={18} stroke={iconStroke} />
                    </div>
                    <ul
                      tabIndex={0}
                      className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52"
                    >
                      <li>
                        <button
                          className="flex items-center gap-2 bg-transparent border-none shadow-none "
                          onClick={() => {
                            btnPrintReceipt(order_ids, tokens);
                          }}
                        >
                          <IconReceipt size={18} stroke={iconStroke} /> Hesap Fişi Yazdır
                        </button>
                      </li>
                      <li>
                        <button
                          className="flex items-center gap-2 bg-transparent border-none shadow-none text-red-500"
                          onClick={() => {
                            btnShowCancelOrderModal(order_ids);
                          }}
                        >
                          <IconX size={18} stroke={iconStroke} /> İptal
                        </button>
                      </li>
                      <li>
                        <button
                          className="flex items-center gap-2 bg-transparent border-none shadow-none text-green-500"
                          onClick={() => {
                            if(isPaid == false) {
                              toast.error("Ödenmemiş Sipariş Lütfen Ödeme Alın.")
                              return;
                            }
                            btnShowCompleteOrderModal(order_ids);
                          }}
                        >
                          <IconCheck size={18} stroke={iconStroke} /> Tamamlandı
                        </button>
                      </li>
                      <li>
                        <button
                          className="flex items-center gap-2 bg-transparent border-none shadow-none "
                          onClick={() => {
                            if(isPaid == true) {
                              toast.error("Ödemesi zaten alınmış. Lütfen tammala seçeneğini seçin.")
                              return;
                            }
                            btnShowPayAndComplete(order_ids, order);
                          }}
                        >
                          <IconCash size={18} stroke={iconStroke} /> Ödeme Al
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>

                {orders.map((o, i) => {
                  const {
                    id,
                    date,
                    delivery_type,
                    customer_type,
                    customer_id,
                    customer_name,
                    status,
                    payment_status,
                    token_no,
                    items,
                  } = o;

                  return (
                    <div key={i} className="py-2 bg-gray-50 px-2 mt-2 mb-2 rounded-xl">
                      <div className="flex items-center justify-between gap-2">
                        <div className="flex items-center justify-center flex-col text-center">
                          <p>Token:</p>
                          <div className="w-12 h-12 flex items-center justify-center font-bold rounded-full bg-slate-700 text-white">
                            {token_no}
                          </div>
                        </div>
                        <div className="text-end">
                          <p>
                            {new Intl.DateTimeFormat("tr-TR", {
                              timeStyle: "short",
                            }).format(new Date(date))}
                          </p>
                          <p className="flex gap-2 items-center text-sm text-gray-500">
                            <IconCash stroke={iconStroke} size={18} />
                            {payment_status}
                          </p>
                        </div>
                      </div>

                      {/* order items */}
                      <div className="mt-4 flex flex-col divide-y">
                        {items.map((item, index) => {
                          const {
                            id: orderItemId,
                            order_id,
                            item_id,
                            item_title,
                            variant_id,
                            variant_title,
                            quantity,
                            status,
                            date,
                            addons,
                            notes,
                          } = item;

                          const addonsText =
                            addons?.length > 0
                              ? addons?.map((a) => a.title)?.join(", ")
                              : null;

                          return (
                            <div
                              key={index}
                              className="flex items-center gap-2 py-2"
                            >
                              {/* status */}
                              {status == "preparing" && (
                                <IconClock
                                  stroke={iconStroke}
                                  className="text-amber-500"
                                />
                              )}
                              {status == "completed" && (
                                <IconCheck
                                  stroke={iconStroke}
                                  className="text-green-500"
                                />
                              )}
                              {status == "cancelled" && (
                                <IconX
                                  stroke={iconStroke}
                                  className="text-red-500"
                                />
                              )}
                              {status == "delivered" && (
                                <IconChecks
                                  stroke={iconStroke}
                                  className="text-green-500"
                                />
                              )}

                              {/* item title */}
                              <div className="flex-1">
                                <p>
                                  {item_title} {variant_title} x {quantity}
                                </p>
                                {addonsText && (
                                  <p className="text-sm text-gray-700">
                                    Ekstralar: {addonsText}
                                  </p>
                                )}
                                {notes && (
                                  <p className="text-sm text-gray-700">
                                    Not: {notes}
                                  </p>
                                )}
                              </div>

                              {/* action */}
                              <div className="dropdown dropdown-left">
                                <div
                                  tabIndex={0}
                                  role="button"
                                  className="btn btn-sm btn-circle bg-transparent border-none shadow-none m-1"
                                >
                                  <IconDotsVertical
                                    size={18}
                                    stroke={iconStroke}
                                  />
                                </div>
                                <ul
                                  tabIndex={0}
                                  className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52"
                                >
                                  <li>
                                    <button
                                      className="flex items-center gap-2 bg-transparent border-none shadow-none text-amber-500"
                                      onClick={() => {
                                        btnChangeOrderItemStatus(
                                          orderItemId,
                                          "preparing"
                                        );
                                      }}
                                    >
                                      <IconClock size={18} stroke={iconStroke} />
                                      Hazırlanıyor
                                    </button>
                                  </li>
                                  <li>
                                    <button
                                      className="flex items-center gap-2 bg-transparent border-none shadow-none text-restro-green"
                                      onClick={() => {
                                        btnChangeOrderItemStatus(
                                          orderItemId,
                                          "completed"
                                        );
                                      }}
                                    >
                                      <IconCheck size={18} stroke={iconStroke} />
                                      Hazır
                                    </button>
                                  </li>
                                  <li>
                                    <button
                                      className="flex items-center gap-2 bg-transparent border-none shadow-none "
                                      onClick={() => {
                                        btnChangeOrderItemStatus(
                                          orderItemId,
                                          "delivered"
                                        );
                                      }}
                                    >
                                      <IconChecks size={18} stroke={iconStroke} />
                                      Teslim Edildi
                                    </button>
                                  </li>
                                  <li>
                                    <button
                                      className="flex items-center gap-2 bg-transparent border-none shadow-none text-red-500"
                                      onClick={() => {
                                        btnChangeOrderItemStatus(
                                          orderItemId,
                                          "cancelled"
                                        );
                                      }}
                                    >
                                      <IconX size={18} stroke={iconStroke} />
                                      İptal
                                    </button>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      </>
    )}
    {/* dialog: successful order item status update */}
    <dialog id="modal-order-item-status-update" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Success!</h3>
          <p className="py-4">Order Item status successfully updated!</p>
          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn">Close</button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: successful order item status update */}

      {/* dialog: cancel order */}
      <dialog id="modal-order-cancel" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Dikkat!</h3>
          <p className="py-4">
            Siparişi İptal Etmek İstediğinize Emin misiniz?. 🛑✋
          </p>

          {/* İptal Nedeni Seçimi */}
          <div className="form-control w-full mb-4">
            <label className="label">
              <span className="label-text font-medium">İptal Nedeni Seçin</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={state.selectedCancelReasonId || ""}
              onChange={(e) => setState({...state, selectedCancelReasonId: e.target.value})}
              required
            >
              <option value="" disabled>Lütfen bir iptal nedeni seçin</option>
              {cancellationReasons.map((reason) => (
                <option key={reason.id} value={reason.id}>
                  {reason.title}
                </option>
              ))}
            </select>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn">Hayır</button>
              <button
                onClick={() => {
                  if (!state.selectedCancelReasonId) {
                    toast.error("Lütfen bir iptal nedeni seçin!");
                    return;
                  }
                  btnCancelOrder();
                }}
                className="ml-2 btn hover:bg-red-700 bg-red-500 text-white"
              >
                Evet Onaylıyorum!
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: cancel order */}

      {/* dialog: order item cancel reason */}
      <dialog id="modal-order-item-cancel-reason" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Dikkat!</h3>
          <p className="py-4">
            Ürünü İptal Etmek İçin Bir Neden Seçin 🛑✋
          </p>

          {/* İptal Nedeni Seçimi */}
          <div className="form-control w-full mb-4">
            <label className="label">
              <span className="label-text font-medium">İptal Nedeni Seçin</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={state.selectedCancelReasonId || ""}
              onChange={(e) => setState({...state, selectedCancelReasonId: e.target.value})}
              required
            >
              <option value="" disabled>Lütfen bir iptal nedeni seçin</option>
              {cancellationReasons.map((reason) => (
                <option key={reason.id} value={reason.id}>
                  {reason.title}
                </option>
              ))}
            </select>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn">İptal</button>
              <button
                onClick={() => {
                  if (!state.selectedCancelReasonId) {
                    toast.error("Lütfen bir iptal nedeni seçin!");
                    return;
                  }
                  // İptal nedeni ID'sini de gönder
                  btnChangeOrderItemStatus(
                    state.selectedOrderItemId,
                    state.selectedOrderItemStatus,
                    state.selectedCancelReasonId
                  );
                  document.getElementById("modal-order-item-cancel-reason").close();
                }}
                className="ml-2 btn hover:bg-red-700 bg-red-500 text-white"
              >
                Evet Onaylıyorum!
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: order item cancel reason */}

      {/* dialog: complete order */}
      <dialog id="modal-order-complete" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Alert!</h3>
          <p className="py-4">
            Are you sure, you are completing the order! 🛑✋
          </p>
          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn">Dismiss</button>
              <button
                onClick={() => {
                  btnCompleteOrder();
                }}
                className="ml-2 btn hover:bg-restro-green-dark bg-restro-green text-white"
              >
                Yes Confirm!
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: complete order */}

      {/* dialog: complete order & payment summary */}
      <dialog id="modal-order-summary-complete" className="modal">
        <div className="modal-box">
          <div className="flex items-center justify-between">
            <h3 className="font-bold text-lg">Ödeme Al ve Siparişi Kapat!</h3>
            <form method='dialog'>
              <button className="hover:bg-red-100 border-none transition active:scale-95 bg-red-50 text-red-500 btn btn-sm btn-circle"><IconX size={18} stroke={iconStroke} /></button>
            </form>
          </div>


          <div className="my-6">

            <div className="flex w-full items-center divide-x gap-x-4">
              <div className="flex-1 text-center">
                <p>Ara Toplam</p>
                <p className="text-2xl">
                  {Number(state.summaryNetTotal).toFixed(2)}
                  {currency}
                </p>
              </div>

              <div className="flex-1 text-center">
                <p>KDV</p>
                <p className="text-2xl">
                  {Number(state.summaryTaxTotal).toFixed(2)}
                  {currency}
                </p>
              </div>

              <div className="flex-1 text-center">
                <p>Toplam</p>
                <p className="text-2xl text-restro-green font-bold">
                  {Number(state.summaryTotal).toFixed(2)}
                  {currency}
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
            {paymentTypes.map((paymentType, i)=>{
              return <label key={i} className=''>
                <input
                checked={state?.selectedPaymentType == paymentType?.id}
                onChange={e=>{
                  setState({
                    ...state,
                    selectedPaymentType: e.target.value,
                  });
                }} type="radio" name="payment_type" id={paymentType?.icon} value={paymentType?.id} className='peer hidden' />
                <label htmlFor={paymentType?.icon} className='border rounded-2xl flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-restro-green peer-checked:text-restro-green peer-checked:font-bold cursor-pointer transition'>
                  {paymentType?.icon ? <div>{PAYMENT_ICONS[paymentType?.icon]}</div>:<></>}
                  <p className='text-xs'>{paymentType.title}</p>
                </label>
              </label>
            })}
          </div>

          <div className="modal-action">
            <form method="dialog" className="w-full">
              {/* if there is a button in form, it will close the modal */}
              <button
                onClick={() => {
                  btnPayAndComplete();
                }}
                className="w-full btn hover:bg-restro-green-dark bg-restro-green text-white"
              >
                Ödemeyi Al ve Siparişi Kapat
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: complete order & payment summary */}
  </Page>
)};