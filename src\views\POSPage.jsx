import React, { useContext, useEffect, useRef, useState } from 'react'
import Page from "../components/Page";
import { IconTrash, IconClipboardList, IconX, IconClearAll, IconPencil, IconRotate, IconQrcode, IconArmchair2, IconUser, IconCashRegister, IconCash, IconCashPlus } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { cancelAllQROrders, cancelQROrder, createOrder, createOrderAndInvoice, getQROrders, getQROrdersCount, initPOS, savePrintDataKitchen, savePrintDataRecipet } from "../controllers/pos.controller";
import { CURRENCIES } from '../config/currencies.config';
import { PAYMENT_ICONS } from "../config/payment_icons";
import { toast } from "react-hot-toast";
import { searchCustomer } from '../controllers/customers.controller';
import { Link, useNavigate } from 'react-router-dom';
import { setDetailsForReceiptPrint } from '../helpers/ReceiptHelper';
import { SocketContext } from "../contexts/SocketContext";
import { initSocket } from '../utils/socket';
import { getImageURL } from '../helpers/ImageHelper';
import { getUserDetailsInLocalStorage } from '../helpers/UserDetails';
import AsyncCreatableSelect from 'react-select/async-creatable';
import DialogAddCustomer from '../components/DialogAddCustomer';
import { useNavbarVisibility } from '../contexts/NavbarVisibilityContext';
import TableSelection from '../components/pos/tables/TableSelection';
import PaymentAndKitchenModal from '../components/pos/Modals/PaymentAndKitchenModal';
import CategoryList from '../components/pos/CategoryList';
import ProductGrid from '../components/pos/ProductGrid';
import Cart from '../components/pos/Cart';
import CashRegisterStatus from '../components/pos/CashRegisterStatus';
import CashRegisterSessionModal from '../components/pos/CashRegisterSessionModal';
import CashWithdrawalModal from '../components/pos/CashWithdrawalModal';
import CashDepositModal from '../components/pos/CashDepositModal';
import LastTransactionsModal from '../components/pos/LastTransactionsModal';
import OrderTypeButtons from '../components/OrderTypeButtons';
import { useUserActiveSession, addPaymentTransaction } from '../controllers/cash-register.controller';
import { SCOPES } from '../config/scopes';
import RequireScopes from '../components/RequireScopes';


// Modal açıldığında ve kapandığında body elementine modal-open sınıfını ekleyip çıkaracak fonksiyon
const setupModalEvents = () => {
  // Tüm dialog elementlerini seçelim
  const dialogs = document.querySelectorAll('dialog');

  // Her dialog için orijinal metodları saklayıp yeni metodlar ekleyelim
  dialogs.forEach(dialog => {
    const originalShowModal = dialog.showModal;
    const originalClose = dialog.close;

    // showModal metodunu override edelim
    dialog.showModal = function() {
      document.body.classList.add('modal-open');
      return originalShowModal.apply(this, arguments);
    };

    // close metodunu override edelim
    dialog.close = function() {
      document.body.classList.remove('modal-open');
      return originalClose.apply(this, arguments);
    };

    // ESC tuşu ile kapatıldığında da modal-open sınıfını kaldıralım
    dialog.addEventListener('cancel', () => {
      document.body.classList.remove('modal-open');
    });
  });
};

function POSPageContent() {
  const user = getUserDetailsInLocalStorage();
  const { socket, isSocketConnected } = useContext(SocketContext);
  const navigate = useNavigate();
  const { role: userRole, scope } = user;
  const userScopes = scope?.split(",");

  const { setShowNavbar } = useNavbarVisibility();

  // Fiziksel barkod okuyucu için referans
  const barcodeInputRef = useRef(null);

  useEffect(() => {
    setShowNavbar(false);
    return () => setShowNavbar(true);
  }, [setShowNavbar]);

  const tableRef = useRef();


  const dialogNotesIndexRef = useRef();
  const dialogNotesTextRef = useRef();


  // dialog: search customer
  const searchCustomerRef = useRef(null);
  // dialog: search customer

  const [state, setState] = useState({
    categories: [],
    menuItems: [],
    paymentTypes: [],
    printSettings: null,
    storeSettings: null,
    storeTables: [],
    floors: [],
    currency: "",
    isLoading: true,
    selectedTableId: null,
    selectedFloorId: null,
    cartItems: [],
    customerType: "WALKIN",
    customer: null,
    addCustomerDefaultValue: null,
    searchQuery: "",
    selectedCategory: "all",
    selectedItemId: null,
    drafts: [],

    itemsTotal: 0,
    taxTotal: 0,
    payableTotal: 0,

    orderId: null,
    tokenNo: null,
    orderType: 'dinein',

    qrOrdersCount: 0,
    qrOrders: [],
    selectedQrOrderItem: null,

    selectedPaymentType: null,
    maliModeActive: false,
  });

  // Get active cash register session
  const { data: activeSession, mutate: mutateSession } = useUserActiveSession();

  useEffect(()=>{
    _initPOS();
    _initSocket();
    setShowNavbar(false);

    // Modal olaylarını ayarla
    setupModalEvents();

    // Component unmount olduğunda modal-open sınıfını kaldır
    return () => {
      document.body.classList.remove('modal-open');
    };
  },[]);

  const { categories, menuItems, paymentTypes, printSettings, storeSettings, storeTables, floors, currency, cartItems, searchQuery, selectedCategory, selectedItemId, customer, customerType, isLoading } = state;

  // Handle cash register session modal
  const handleOpenCashRegisterModal = () => {
    document.getElementById("modal-cash-register-session").showModal();
  };

  // Handle cash withdrawal modal
  const handleOpenCashWithdrawalModal = () => {
    document.getElementById("modal-cash-withdrawal").showModal();
  };

  // Handle cash deposit modal
  const handleOpenCashDepositModal = () => {
    document.getElementById("modal-cash-deposit").showModal();
  };

  // Handle last transactions modal
  const handleOpenLastTransactionsModal = () => {
    document.getElementById("modal-last-transactions").showModal();
  };

  // Handle session change
  const handleSessionChange = () => {
    mutateSession();
  };


  const handlePriceChange = (index, newPrice) => {
    const updatedItems = [...cartItems];
    updatedItems[index].price = newPrice;
    setState({
      ...state,
      cartItems: updatedItems,

    })
  };


  const handlePaymentTypeChange = (value) => {
    setState({
      ...state,
      selectedPaymentType: value,
    });
  };



  const sendNewOrderEvent = (tokenNo, orderId) => {
    if (isSocketConnected) {
      socket.emit('new_order_backend', {tokenNo, orderId}, user.tenant_id);
      socket.emit('new_order');
      refreshOrders();
    } else {
      // Handle disconnected state (optional)
      initSocket();
      socket.emit('new_order_backend', {tokenNo, orderId}, user.tenant_id);
      socket.emit('new_order');
      refreshOrders();
    }
  }

  async function _initPOS() {
    try {
      const [posRes] = await Promise.all([initPOS()]);
      let totalQROrders = 0;

      if(posRes.status === 200) {
        const data = posRes.data;
        const currency = CURRENCIES.find((c)=>c.cc==data?.storeSettings?.currency);

        // Ürün listesi boş mu kontrol et
        if (!(data.menuItems && data.menuItems.length > 0)) {
          console.warn("UYARI: Ürün listesi boş veya tanımsız!");
        }

        try {
          totalQROrders = await _getQROrdersCount();
        } catch (error) {
          // Hata durumunda sessizce devam et
        }

        const enabledOrderTypes = {
          dinein: data.storeSettings.dine_in_enabled === 1,
          delivery: data.storeSettings.delivery_enabled === 1,
          takeaway: data.storeSettings.takeaway_enabled === 1,
        };

        setState({
          ...state,
          categories: data.categories,
          menuItems: data.menuItems || [], // Boş dizi olmaması için önlem
          paymentTypes: data.paymentTypes,
          printSettings: data.printSettings,
          storeSettings: data.storeSettings,
          floors: data.floors,
          storeTables: data.floors.flatMap(floor => floor.tables || []), // Tüm masaları düz bir diziye dönüştür
          currency: currency?.symbol || "",
          qrOrdersCount: totalQROrders || 0,
          isLoading: false,
          orderType: data.storeSettings.default_order_type || 'dinein',
          enabledOrderTypes: enabledOrderTypes,
          maliModeActive: data.storeSettings.mali_mode_active || false,
        });

        // State güncellendikten sonra menuItems'ın doğru şekilde ayarlandığını kontrol et
        console.log("_initPOS tamamlandı, menuItems ayarlandı:", data.menuItems?.length || 0);
      }
    } catch (error) {
      console.error("POS başlatma hatası:", error);
      toast.error("POS sistemi başlatılırken hata oluştu!");
    }
  }


  async function _initFloors() {
    try {
      const res = await initPOS();
      if (res.status === 200) {
        const data = res.data;
        // Sadece floors (ve istersek storeTables) güncelleriz
        setState((prev) => ({
          ...prev,
          floors: data.floors,
          storeTables: data.storeTables,
        }));
      }
    } catch (error) {
      console.error(error);
    }
  }

  const handleOrderTypeChange = (type) => {
    setState(prevState => ({
      ...prevState,
      orderType: type,
      selectedTableId: null // Sipariş türü değiştiğinde masa seçimini sıfırla
    }));
    if (tableRef.current) {
      tableRef.current.value = "";
    }
  };

  const _getQROrdersCount = async () => {
    try {
      const res = await getQROrdersCount();
      if(res.status == 200) {
        const data = res.data;

        return data?.totalQROrders || 0;
      }
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
  const _initSocket = () => {
    if(isSocketConnected) {
      socket.emit("authenticate", user.tenant_id);
      socket.on('new_qrorder', async (payload)=>{
        try {
          const totalQROrders = await _getQROrdersCount();

          setState((prevState)=>({
            ...prevState,
            qrOrdersCount: totalQROrders || 0
          }))
        } catch (error) {
          // Hata durumunda sessizce devam et
        }
      })
      socket.on('new_order', async (payload) => {
        refreshOrders();
      });

      socket.on('order_update', () => {
        refreshOrders();
      });
    } else {
      initSocket();
      socket.emit("authenticate", user.tenant_id);
      socket.on('new_qrorder', async (payload)=>{
        try {
          const totalQROrders = await _getQROrdersCount();

          setState((prevState)=>({
            ...prevState,
            qrOrdersCount: totalQROrders || 0
          }))
        } catch (error) {
          // Hata durumunda sessizce devam et
        }
      })
      socket.on('new_order', async (payload) => {
        refreshOrders();
      });

      // Sipariş güncelleme dinleme
      socket.on('order_update', () => {
        refreshOrders();
      });
    }
  }

  // Fiziksel barkod okuyucu dinleyicisi
// Fiziksel barkod okuyucu dinleyicisi - ana bileşende kalacak useEffect
useEffect(() => {
  // Barkod veri toplama için değişken
  window.barkodeBuffer = '';
  window.islemYapiliyor = false;

  let sonTusSuresi = 0;
  const barkodZamanAsimi = 200; // ms
  let zamanAsimId = null;

  const barkodTaramaIsle = (event) => {
    // İşlem devam ediyorsa yeni girişleri engelle
    if (window.islemYapiliyor) {
      console.log("Barkod işleniyor, yeni giriş engellendi");
      return;
    }

    // Form elementlerinde yazarken barkod taramasını engelle
    if (document.activeElement.tagName === 'INPUT' ||
        document.activeElement.tagName === 'TEXTAREA' ||
        document.activeElement.isContentEditable) {
      return;
    }

    const simdikiZaman = new Date().getTime();

    // Her zaman mevcut zamanlayıcıyı temizle
    if (zamanAsimId) {
      clearTimeout(zamanAsimId);
    }

    // Enter tuşuna basıldığında barkodu işle
    if (event.key === 'Enter') {
      console.log("Barkod tarama tamamlandı (Enter tuşu), buffer:", window.barkodeBuffer);
      if (window.barkodeBuffer && window.barkodeBuffer.length > 0) {
        window.islemYapiliyor = true; // İşlem başladı
        const mevcutBuffer = window.barkodeBuffer; // Mevcut buffer'ı kopyala
        window.barkodeBuffer = ''; // Buffer'ı temizle

        // Barkodu işle
        barkodIsleme(mevcutBuffer);
        event.preventDefault();

        // İşlem tamamlandıktan sonra kilidi kaldır
        setTimeout(() => {
          window.islemYapiliyor = false;
          console.log("Barkod işleme kilidi kaldırıldı, yeni taramaya hazır");
        }, 1000);
      }
      return;
    }

    // Yeni bir tarama başlat (zaman aşımı kontrolü)
    if (simdikiZaman - sonTusSuresi > barkodZamanAsimi) {
      console.log("Yeni barkod tarama başladı");
      window.barkodeBuffer = event.key;
    } else {
      // Mevcut taramaya karakter ekle
      window.barkodeBuffer += event.key;
      console.log("Barkod buffer güncellendi:", window.barkodeBuffer);
    }

    sonTusSuresi = simdikiZaman;
  };

  // Event listener ekle
  window.addEventListener('keypress', barkodTaramaIsle);

  // Bileşen temizlendiğinde event listener'ı ve zamanlayıcıyı kaldır
  return () => {
    window.removeEventListener('keypress', barkodTaramaIsle);
    if (zamanAsimId) {
      clearTimeout(zamanAsimId);
    }
    // Global değişkenleri temizle
    window.barkodeBuffer = '';
    window.islemYapiliyor = false;
  };
}, [state.menuItems]); // menuItems değiştiğinde bu useEffect'i yeniden çalıştır



// Barkod işleme fonksiyonu
// Barkod işleme fonksiyonu
// Barkod işleme fonksiyonu
const barkodIsleme = (barkodVerisi) => {
  try {
    console.log("==================== BARKOD BİLGİLERİ ====================");
    console.log("Ham barkod verisi:", barkodVerisi);

    // Ürün listesini state'ten al
    const menuItemsList = state.menuItems;

    // Ürünler yüklendi mi kontrol et
    if (!menuItemsList || menuItemsList.length === 0) {
      console.log("HATA: Ürün listesi yüklenmedi veya boş!");
      toast.error("Ürün listesi yüklenemedi. Lütfen bekleyin veya sayfayı yenileyin.");
      return;
    }

    console.log("Mevcut ürün sayısı:", menuItemsList.length);

    let urunBulundu = false; // Ürünün bulunup bulunmadığını kontrol eden yeni değişken

    try {
      // JSON formatında mı diye kontrol et
      let barkodJson = JSON.parse(barkodVerisi);
      console.log("JSON olarak ayrıştırıldı:", barkodJson);

      if (barkodJson && barkodJson.id !== undefined && barkodJson.value !== undefined) {
        const barkodDegeri = barkodJson.value;
        const miktar = 1;

        // Barkod değerini sayıya çevir
        const barkodDegeriSayi = parseInt(barkodDegeri, 10);

        // Farklı yöntemlerle ürünü bulma
        let urun = null;

        // Üç farklı yöntemle arama yap
        urun = menuItemsList.find(item => parseInt(item.id, 10) === barkodDegeriSayi) ||
               menuItemsList.find(item => String(item.id) === String(barkodDegeri)) ||
               menuItemsList.find(item => item.id == barkodDegeri);

        if (urun) {
          console.log(`ÜRÜN BULUNDU: ID ${urun.id}, Başlık: ${urun.title}`);
          addItemToCart({...urun, quantity: miktar, notes: null});
          toast.success(`${urun.title} (${miktar} adet) sepete eklendi!`);
          urunBulundu = true; // Ürün bulundu, flag'i ayarla
        }
        else if (!urunBulundu) { // Sadece daha önce ürün bulunmadıysa kontrol et
          // Hiçbir yöntemle bulunamadı, veri setinin bölünmüş olma ihtimalini kontrol et
          console.log(`ID bulunamadı: ${barkodDegeri}. Alternatif arama yapılıyor...`);

          const ilkYaris = menuItemsList.slice(0, Math.floor(menuItemsList.length / 2));
          const ikinciYaris = menuItemsList.slice(Math.floor(menuItemsList.length / 2));

          const ilkYarisUrun = ilkYaris.find(item => item.id == barkodDegeri);
          const ikinciYarisUrun = ikinciYaris.find(item => item.id == barkodDegeri);

          if (ilkYarisUrun) {
            console.log(`Ürün ilk yarıda bulundu: ${ilkYarisUrun.title}`);
            addItemToCart({...ilkYarisUrun, quantity: miktar, notes: null});
            toast.success(`${ilkYarisUrun.title} (${miktar} adet) sepete eklendi!`);
            urunBulundu = true;
          } else if (ikinciYarisUrun) {
            console.log(`Ürün ikinci yarıda bulundu: ${ikinciYarisUrun.title}`);
            addItemToCart({...ikinciYarisUrun, quantity: miktar, notes: null});
            toast.success(`${ikinciYarisUrun.title} (${miktar} adet) sepete eklendi!`);
            urunBulundu = true;
          } else {
            console.log(`Hiçbir yöntemle ürün bulunamadı. Aranan ID: ${barkodDegeri}`);
            toast.error(`Ürün bulunamadı (Barkod: ${barkodDegeri})`);
          }
        }
      } else {
        console.log("JSON formatında ama id veya value alanları eksik");
        toast.error("Geçersiz barkod formatı! ID ve değer alanları eksik.");
      }
    } catch (e) {
      // JSON değil, normal barkod olarak işle
      console.log("JSON ayrıştırma hatası, normal barkod olarak işleniyor");

      if (urunBulundu) return; // Eğer daha önce ürün bulunduysa işleme devam etme

      const barkodDegeriSayi = parseInt(barkodVerisi, 10);

      // Doğrudan barkod değerini ID olarak kabul et ve ara
      let urun = menuItemsList.find(item => item.id == barkodVerisi ||
                                        parseInt(item.id, 10) === barkodDegeriSayi ||
                                        String(item.id) === String(barkodVerisi));

      if (urun) {
        console.log(`Basit barkod işleme: Ürün bulundu - ${urun.title}`);
        addItemToCart({...urun, quantity: 1, notes: null});
        toast.success(`${urun.title} sepete eklendi!`);
      } else {
        // Alternatif alanlarda ara
        urun = menuItemsList.find(item =>
          item.barcode === barkodVerisi ||
          item.sku === barkodVerisi ||
          item.code === barkodVerisi
        );

        if (urun) {
          console.log(`Alternatif alan araması: Ürün bulundu - ${urun.title}`);
          addItemToCart({...urun, quantity: 1, notes: null});
          toast.success(`${urun.title} sepete eklendi!`);
        } else {
          // Yine bulunamadıysa, tanımsız ürün oluştur
          console.log("Ürün bulunamadı, örnek ürün oluşturuluyor");
          toast.info("Barkod okundu, ürün bulunamadı. Örnek ürün ekleniyor...");

          const ornekUrun = {
            id: `barkod-${barkodVerisi}`,
            title: `Tanımsız Ürün (Barkod: ${barkodVerisi})`,
            price: 0,
            tax_id: 1,
            tax_title: "KDV",
            tax_rate: 18,
            tax_type: "exclusive"
          };

          addItemToCart({...ornekUrun, quantity: 1, notes: null});
          toast.warning(`Bilinmeyen ürün (Barkod: ${barkodVerisi}) sepete eklendi! Fiyatı güncelleyin.`);
        }
      }
    }
    console.log("==================== BARKOD İŞLEME TAMAMLANDI ====================");
  } catch (error) {
    console.error("Barkod işleme hatası:", error);
    toast.error("Barkod işlenirken bir hata oluştu!");
  }
};


  const refreshOrders = async () => {
    try {
      await _initFloors();
    } catch (error) {
      console.error('Error refreshing orders:', error);
    }
  };

  if(isLoading) {
    return <Page className='px-4 py-3 flex flex-col min-h-0'>
      <div className='mt-4 h-[calc(100vh-136px)] flex gap-4 skeleton'>
        <div className='border border-restro-border-green-light rounded-2xl h-full w-[70%] overflow-y-auto'></div>
        <div className='border border-restro-border-green-light rounded-2xl h-full w-[30%] relative flex flex-col'></div>
      </div>
    </Page>
  }

  // cart
  function addItemToCart(item) {
    const modifiedItem = {
      ...item,
      quantity: 1,
      notes: null
    }
    if(!cartItems) {
      setState({
        ...state,
        cartItems: [modifiedItem]
      })
      return;
    }
    setState({
      ...state,
      cartItems: [...cartItems, modifiedItem]
    })
  }

  function removeItemFromCart(index) {
    setState({
      ...state,
      cartItems: cartItems.filter((c,i)=> i !== index)
    })
  }

  function addCartItemQuantity(index, currentQuantity) {
    const newQuantity = currentQuantity + 1;
    const newCartItems = cartItems;

    newCartItems[index].quantity = newQuantity;

    setState({
      ...state,
      cartItems: [...newCartItems]
    });
  }

  function minusCartItemQuantity(index, currentQuantity) {
    const newQuantity = currentQuantity - 1;
    let newCartItems = cartItems;

    newCartItems[index].quantity = newQuantity;

    if(newQuantity == 0) {
      newCartItems = cartItems.filter((v, i)=>i!=index);
    }

    setState({
      ...state,
      cartItems: [...newCartItems]
    });
  }
  // cart

  // order notes
  const btnOpenNotesModal = (index, notes) => {
    dialogNotesIndexRef.current.value = index;
    dialogNotesTextRef.current.value = notes;
    document.getElementById('modal-notes').showModal()
  }
  const btnAddNotes = () => {
    const index = dialogNotesIndexRef.current.value;
    const notes = dialogNotesTextRef.current.value || null;

    const newCartItems = cartItems;
    newCartItems[index].notes = notes;

    setState({
      ...state,
      cartItems: newCartItems
    })
  };
  // order notes

  // variant, addon modal
  const btnOpenVariantAndAddonModal = (menuItemId) => {
    setState({
      ...state,
      selectedItemId: menuItemId
    })
    document.getElementById('modal-variants-addons').showModal()
  }
  const btnAddMenuItemToCartWithVariantsAndAddon = () => {
    let price = 0;
    let selectedVariantId = null;
    const selectedAddonsId = [];

    const itemVariants = document.getElementsByName("variants");
    itemVariants.forEach((item)=>{
      if(item.checked) {
        selectedVariantId = item.value;
        return;
      }
    })



    const itemAddons = document.getElementsByName("addons");
    itemAddons.forEach((item)=>{
      if(item.checked) {
        selectedAddonsId.push(item.value);
      }
    })

    // get selected menu item
    const selectedItem = menuItems.find((item)=>item.id == selectedItemId);

    const addons = selectedItem?.addons || [];
    const variants = selectedItem?.variants || [];

    let selectedVariant = null;
    if(selectedVariantId) {
      selectedVariant = variants.find((v)=>v.id == selectedVariantId);
      price = parseFloat(selectedVariant.price);
    } else {
      price = parseFloat(selectedVariant?.price ?? selectedItem.price)
    }

    let selectedAddons = [];
    if(selectedAddonsId.length > 0) {
      selectedAddons = selectedAddonsId.map((addonId)=>addons.find((addon)=>addon.id == addonId))
      selectedAddons.forEach((addon)=>{
        const addonPrice = parseFloat(addon.price);
        price = price + addonPrice
      });
    }

    const itemCart = {...selectedItem, price: price, variant_id: selectedVariantId, variant: selectedVariant, addons_ids: selectedAddonsId, addons: selectedAddons}
    addItemToCart(itemCart)
  };
  // variant, addon modal



  // QR Menu Orders
  const btnShowQROrdersModal = async () => {
    try {
      const res = await getQROrders();
      if(res.status == 200) {
        const data = res.data;
        setState({
          ...state,
          qrOrders: [...data]
        })
        document.getElementById("modal-qrorders").showModal();
      }
    } catch (error) {
      // Hata durumunda sessizce devam et
    }
  };

  const btnClearQROrders = async () => {
    const isConfirm = window.confirm("Silmek istediğniize eminmisiniz?!");
    if(!isConfirm) {
      return;
    }
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await cancelAllQROrders();
      if(res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);
        setState((prevState)=>({
          ...prevState, qrOrders: [], qrOrdersCount: 0,
        }))
      }
    } catch (error) {
      const message = error?.response?.data?.message || "We're getting issue while clearing orders! Please try later!";
      console.log(error);
      toast.dismiss();
      toast.error(message);
    }
  }

  const btnCancelQROrder = async (orderId) => {
    const isConfirm = window.confirm("Are you sure? This process is irreversible!");
    if(!isConfirm) {
      return;
    }
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await cancelQROrder(orderId);
      if(res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);

        const newQROrders = state.qrOrders.filter((item)=>item.id != orderId);
        const newQROrderItem = state.qrOrdersCount - 1;

        setState((prevState)=>({
          ...prevState, qrOrders: [...newQROrders], qrOrdersCount: newQROrderItem,
        }))
      }
    } catch (error) {
      const message = error?.response?.data?.message || "We're getting issue while clearing orders! Please try later!";
      toast.dismiss();
      toast.error(message);
    }
  }
  const btnSelectQROrder = (qrOrder) => {
    // QR sipariş masa ID'sini state'e kaydet ve tableRef'i güncelle
    if(qrOrder.table_id && tableRef.current) {
      tableRef.current.value = qrOrder.table_id;
    }

    const modifiedCart = qrOrder.items.map((item)=>{
      const id = item.item_id;
      return {
        ...item,
        id: id,
        title: item.item_title,
        variant_id: selectedVariantId,
        variant: selectedVariant,
        addons_ids: item?.addons?.map((i)=>i.id)
      }
    })

    setState({
      ...state,
      cartItems: modifiedCart,
      selectedQrOrderItem: qrOrder.id,
      customerType: qrOrder.customer_type,
      customer: {phone: qrOrder.customer_id, name: qrOrder.customer_name},
      selectedTableId: qrOrder.table_id || null,
      orderType: qrOrder.table_id ? 'dinein' : state.orderType
    })

    document.getElementById('modal-qrorders').close();
  };

  // search customer modal
  const btnOpenSearchCustomerModal = () => {
    document.getElementById("modal-search-customer").showModal();
  };
  const btnClearSearchCustomer = () => {
    setState({
      ...state,
      customerType: "WALKIN",
      customer: null
    })
  }
  // search customer modal

  // send to kitchen modal
  const calculateOrderSummary = () => {
    let itemsTotal = 0; // without tax - net amount
    let taxTotal = 0;
    let payableTotal = 0;

    cartItems.forEach((item)=>{
      const taxId = item.tax_id;
      const taxTitle = item.tax_title;
      const taxRate = Number(item.tax_rate);
      const taxType = item.tax_type; // inclusive or exclusive or NULL

      const itemPrice = Number(item.price) * Number(item.quantity);

      if (taxType == "exclusive") {
        const tax = (itemPrice * taxRate) / 100;
        const priceWithTax = itemPrice + tax;

        taxTotal += tax;
        itemsTotal += itemPrice;
        payableTotal += priceWithTax;
      } else if (taxType == "inclusive") {
        const tax = itemPrice - (itemPrice * (100 / (100 + taxRate)));
        const priceWithoutTax = itemPrice - tax;

        taxTotal += tax;
        itemsTotal += priceWithoutTax;
        payableTotal += itemPrice;
      } else {
        itemsTotal += itemPrice;
        payableTotal += itemPrice;
      }
    });
    return {
      itemsTotal, taxTotal, payableTotal
    }
  };
  const btnShowPayAndSendToKitchenModal = () => {
    // calculate the item - total, tax, incl. tax, excl. tax, tax total, payable total

    if(cartItems?.length == 0) {
      toast.error("Sepetiniz Boş!");
      return;
    }

    const { itemsTotal, taxTotal, payableTotal } = calculateOrderSummary();

    setState({
      ...state,
      itemsTotal,
      taxTotal,
      payableTotal
    });
    document.getElementById('modal-pay-and-send-kitchen-summary').showModal();
  }


  const btnPayAndSendToKitchen = async () => {
    try {
      const deliveryType = state.orderType;
      let tableId = null;
      let floorId = null;

      if (deliveryType === 'dinein') {
        tableId = tableRef.current?.value;
        if (!tableId) {
          toast.error("Lütfen bir masa seçin.");
          return;
        }

        // Doğrudan state'den floor ID'yi al
        floorId = state.selectedFloorId;
        console.log("Ödeme ve sipariş gönderilirken floor ID:", floorId);
      }
      const customerType = state.customerType;
      const customer = state.customer;

      // Check if user has an active cash register session
      if (!activeSession) {
        toast.error("Ödeme işlemi yapabilmek için aktif bir kasa oturumu açmanız gerekmektedir!");
        handleOpenCashRegisterModal();
        return;
      }

      toast.loading("Lütfen bekleyin...");
      const res = await createOrderAndInvoice(
        cartItems,
        deliveryType,
        customerType,
        customer,
        tableId,
        state.itemsTotal,
        state.taxTotal,
        state.payableTotal,
        state.selectedQrOrderItem,
        state.selectedPaymentType,
        floorId
      );

      toast.dismiss();
      if(res.status == 200) {
        const data = res.data;

        // Record payment transaction in cash register
        try {
          await addPaymentTransaction({
            order_id: data.orderId,
            payment_type_id: state.selectedPaymentType,
            amount: state.payableTotal,
            transaction_type: 'payment',
            notes: `POS Satış - Token: ${data.tokenNo}`
          });
        } catch (error) {
          console.error("Kasa işlemi kaydedilemedi:", error);
          // Continue with the process even if cash register transaction fails
        }

        toast.success(res.data.message);
        document.getElementById("modal-pay-and-send-kitchen-summary").close();

        const page_format = printSettings?.page_format || null;
        const is_enable_print = printSettings?.is_enable_print || 0;

        const paymentType = paymentTypes.find((v)=>v.id == state.selectedPaymentType);
        let paymentMethodText = paymentType?.title;

        const selectedTable = storeTables.find(table => table.id.toString() === tableRef.current?.value);
        const tableName = selectedTable ? selectedTable.table_title : '';

        // Yazdırma verilerini hazırla
        const printData = {
          storeName: storeSettings.store_name,
          table: tableName,
          orderId: data.orderId,
          serverName: user.name,
          orderType: deliveryType,
          floorId: getFloorIdFromTableId(tableId, floors),
          items: cartItems.map(item => ({
            title: item.title,
            quantity: item.quantity,
            price: item.price,
            printerPath: item.printer_path,
            notes: item.notes,
            // Variant varsa ekle
            ...(item.variant && {
              variant: {
                title: item.variant.title,
                price: item.variant.price
              }
            }),
            // Addonlar varsa ekle
            ...(item.addons && item.addons.length > 0 && {
              addons: item.addons.map(addon => ({
                title: addon.title,
                price: addon.price
              }))
            })
          })),
          tokenNo: data.tokenNo,
          customerType,
          customer,
          paymentMethod: paymentMethodText
        };

        if (is_enable_print) {
          try {
            await savePrintDataKitchen(printData);

          } catch (error) {
            console.error("Yazdırma hatası:", error);
            toast.error("Yacıyıa Ulaşılamıyor");
          }
        }

        setDetailsForReceiptPrint({
          cartItems,
          deliveryType,
          customerType,
          customer,
          tableId,
          currency,
          storeSettings,
          printSettings,
          itemsTotal: state.itemsTotal,
          taxTotal: state.taxTotal,
          payableTotal: state.payableTotal,
          tokenNo: data.tokenNo,
          orderId: data.orderId,
          paymentMethod: paymentMethodText
        });

        sendNewOrderEvent(data.tokenNo, data.orderId);

        let newQROrderItemCount = state.qrOrdersCount;
        let newQROrders = [];
        if(state.selectedQrOrderItem) {
          newQROrderItemCount -= 1;
          newQROrders = state?.qrOrders?.filter((item)=>item.id != state.selectedQrOrderItem);
        }

        setState({
          ...state,
          cartItems: [],
          tokenNo: data.tokenNo,
          orderId: data.orderId,
          selectedQrOrderItem: null,
          qrOrders: newQROrders,
          qrOrdersCount: newQROrderItemCount,
          selectedPaymentType: null,
          selectedTableId: null
        });

      }
    } catch (error) {
      const message = error?.response?.data?.message || "Something went wrong!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const btnShowSendToKitchenModal = () => {
    // calculate the item - total, tax, incl. tax, excl. tax, tax total, payable total

    if(cartItems?.length == 0) {
      toast.error("Sepetiniz Boş!");
      return;
    }

    const { itemsTotal, taxTotal, payableTotal } = calculateOrderSummary();

    setState({
      ...state,
      itemsTotal,
      taxTotal,
      payableTotal
    });
    document.getElementById('modal-send-kitchen-summary').showModal();
  }


  const btnSendToKitchen = async () => {
    try {
      const deliveryType = state.orderType;
      let tableId = null;
      let floorId = null;

      if (deliveryType === 'dinein') {
        // Önce state'deki selectedTableId'yi kontrol et, yoksa tableRef'e bak
        tableId = state.selectedTableId || tableRef.current?.value;
       
        if (!tableId) {
          toast.error("Lütfen bir masa seçin.");
          return;
        }

        // Doğrudan state'den floor ID'yi al
        floorId = state.selectedFloorId;
        console.log("Sipariş gönderilirken floor ID:", floorId);
      }
      const customerType = state.customerType;
      const customer = state.customer;


      toast.loading("Lütfen bekleyin...");
      const res = await createOrder(cartItems, deliveryType, customerType, customer, tableId, state.selectedQrOrderItem, floorId);
      toast.dismiss();

      if(res.status == 200) {
        const data = res.data;
        toast.success(res.data.message);
        document.getElementById("modal-send-kitchen-summary").close();

        const is_enable_print = printSettings?.is_enable_print || 0;

        // Önce state'deki selectedTableId'yi kullan, yoksa tableRef'e bak
        const tableIdToUse = state.selectedTableId || tableRef.current?.value;
        console.log("Yazdırma için kullanılan masa ID:", tableIdToUse);

        const selectedTable = state.floors
          .flatMap(floor => floor.tables)
          .find(table => table.id.toString() === tableIdToUse?.toString());

        console.log("Bulunan masa:", selectedTable);

        const tableName = selectedTable ? selectedTable.table_title : '';

        // Yazdırma verilerini hazırla
        const printData = {
          storeName: storeSettings.store_name,
          table: tableName,
          orderId: data.orderId,
          serverName: user.name,
          customer: customer,
          tableID: tableId,
          orderType: deliveryType,
          items: cartItems.map(item => ({
            title: item.title,
            quantity: item.quantity,
            notes: item.notes,
            price: item.price,
            printerPath: item.printer_path,
            notes: item.notes,
            // Variant varsa ekle
            ...(item.variant && {
              variant: {
                title: item.variant.title,
                price: item.variant.price
              }
            }),
            // Addonlar varsa ekle
            ...(item.addons && item.addons.length > 0 && {
              addons: item.addons.map(addon => ({
                title: addon.title,
                price: addon.price
              }))
            })
          })),
          tokenNo: data.tokenNo,
          customerType,
        };

        if (is_enable_print) {
          try {
            await savePrintDataKitchen(printData);

          } catch (error) {
            console.error("Yazdırma hatası:", error);
            toast.error("Yazıcıya Ulaşılamadı");
          }
        }

        setDetailsForReceiptPrint({
          cartItems,
          deliveryType,
          customerType,
          customer,
          tableId,
          currency,
          storeSettings,
          printSettings,
          itemsTotal: state.itemsTotal,
          taxTotal: state.taxTotal,
          payableTotal: state.payableTotal,
          tokenNo: data.tokenNo,
          orderId: data.orderId
        });

        sendNewOrderEvent(data.tokenNo, data.orderId);

        let newQROrderItemCount = state.qrOrdersCount;
        let newQROrders = [];
        if(state.selectedQrOrderItem) {
          newQROrderItemCount -= 1;
          newQROrders = state?.qrOrders?.filter((item)=>item.id != state.selectedQrOrderItem);
        }


        setState((prevState) => ({
          ...prevState,
          cartItems: [], // Boşaltıyoruz
          tokenNo: data.tokenNo,
          orderId: data.orderId,
          selectedQrOrderItem: null,
          qrOrders: newQROrders,
          qrOrdersCount: newQROrderItemCount,
          selectedTableId: null
        }));

         mutateSession();

        console.log("Sipariş gönderildi. Güncel sepet:", cartItems);

      }
    } catch (error) {
      const message = error?.response?.data?.message || "Something went wrong!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };


  const setCustomer = (customer) => {

    if(customer) {
      setState({
        ...state,
        customer: {phone: customer.value, name:customer.label},
        customerType: "CUSTOMER"
      })
      document.getElementById("modal-search-customer").close()
    } else {
      btnClearSearchCustomer();
    }
  }

  const searchCustomersAsync = async (inputValue) => {
    try {
      if(inputValue) {
        const resp = await searchCustomer(inputValue);
        if(resp.status == 200) {
          return resp.data.map((data)=>( {
            label: `${data.name} - Tel: ${data.phone} ${data.address ? ` Adres: ${data.address}` : ''}`,
            value: data.phone
          }));
        }
      }
    } catch (error) {
      // Hata durumunda sessizce devam et
    }
  }
  // search customer modal



  return (
    <Page className='flex flex-col min-h-0'>
      {/* Üst Başlık ve Kontroller - Her zaman en üstte */}
      <div className="border-b border-gray-200 bg-white sticky top-0 z-10">
      <RequireScopes requiredScopes={[SCOPES.KASIYER]} userScopes={userScopes} userRole={userRole}>

        <div className="flex justify-end items-center flex-col md:flex-row gap-3 p-3">
          <div className="flex items-center gap-2">

            <CashRegisterStatus
              onOpenSessionModal={handleOpenCashRegisterModal}
              onOpenLastTransactionsModal={handleOpenLastTransactionsModal}
              maliModeActive={state.maliModeActive}
            />

            {/* Kasa Çıkışı Butonu - Sadece aktif kasa oturumu varsa göster */}
            {activeSession && (
              <>
                <button
                  onClick={handleOpenCashWithdrawalModal}
                  className="text-sm rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-4 py-2 flex items-center gap-1"
                >
                  <IconCash size={18} stroke={iconStroke} /> Kasa Çıkışı
                </button>
                <button
                  onClick={handleOpenCashDepositModal}
                  className="text-sm rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-green-600 px-4 py-2 flex items-center gap-1"
                >
                  <IconCashPlus size={18} stroke={iconStroke} /> Kasa Girişi
                </button>
              </>
            )}

            {/* QR Menu Orders - Sadece KASIYER yetkisi olanlar görebilir */}
              <button
                onClick={btnShowQROrdersModal}
                className={`text-sm rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-4 py-2 flex items-center gap-1 relative ${
                  state.qrOrdersCount > 0 ? 'animate-fastPulse text-white' : ''
                }`}
              >
                <IconQrcode size={18} stroke={iconStroke} /> QR Menu Siparişleri
                {state.qrOrdersCount > 0 && (
                  <div className="absolute -top-2 -right-2 w-4 h-4 rounded-full bg-white text-red-500 text-xs flex items-center justify-center">
                    {state.qrOrdersCount}
                  </div>
                )}
              </button>
          </div>
          <div className="flex items-center gap-2 w-full md:w-auto">
              <OrderTypeButtons
                defaultValue={state.orderType}
                onChange={handleOrderTypeChange}
                enabledTypes={state.enabledOrderTypes}
              />
          </div>
        </div>
        </RequireScopes>

      </div>

      <div className="h-[calc(100vh-140px)] flex flex-col-reverse md:flex-row">
        {/* pos items */}
        <div className='h-full w-full flex flex-col overflow-hidden'>
          {/* Kategoriler - Sipariş türü masaya ve masa seçilmemişse gösterme */}
          {!(state.orderType === 'dinein' && !state.selectedTableId) && (
            <div className="flex flex-col gap-2 bg-white/40 backdrop-blur sticky top-0 w-full px-4 py-3 rounded-t-2xl z-10">
              <CategoryList
                categories={categories}
                selectedCategory={selectedCategory}
                onCategorySelect={(categoryId) => setState({...state, selectedCategory: categoryId})}
                searchQuery={searchQuery}
                onSearchChange={(value) => setState({...state, searchQuery: value})}
                iconStroke={iconStroke}
              />
            </div>
          )}

          <input
            type="text"
            ref={barcodeInputRef}
            style={{ position: 'absolute', top: '-9999px', left: '-9999px' }}
          />

          <div className="flex-grow flex overflow-hidden">

            {state.orderType === 'dinein' && !state.selectedTableId ? (
                <div className="w-full overflow-hidden">
                  <TableSelection
                    tables={floors}
                    onTableSelect={(tableId) => {
                      if (tableRef.current) {
                        tableRef.current.value = tableId;
                      }
                      setState(prev => ({
                        ...prev,
                        selectedTableId: tableId
                      }));
                    }}
                    onFloorSelect={(floorId) => {

                      setState(prev => ({
                        ...prev,
                        selectedFloorId: floorId
                      }));
                    }}
                    refreshOrders={refreshOrders}
                  />
                </div>
            ) : (
                <div className="w-full overflow-y-auto">
                  <ProductGrid
                    menuItems={menuItems}
                    selectedCategory={selectedCategory}
                    searchQuery={searchQuery}
                    currency={currency}
                    onItemClick={(menuItem, hasVariantOrAddon) => {
                      if (hasVariantOrAddon) {
                        btnOpenVariantAndAddonModal(menuItem.id);
                      } else {
                        addItemToCart(menuItem);
                      }
                    }}
                    getImageURL={getImageURL}
                  />
                </div>
            )}
          </div>
        </div>

        {/* Cart Component - Sipariş türü masaya ve masa seçilmemişse gösterme */}
        {!(state.orderType === 'dinein' && !state.selectedTableId) && (
          <Cart
            customerType={customerType}
            customer={customer}
            storeTables={storeTables}
            orderType={state.orderType}
            selectedTableId={state.selectedTableId}
            cartItems={cartItems}
            currency={currency}
            tableRef={tableRef}
            onCustomerSearch={btnOpenSearchCustomerModal}
            onTableSelect={(tableId) => {
              // tableRef'i güncelle
              if (tableRef.current) {
                tableRef.current.value = tableId || "";
              }

              // State'i güncelle
              setState(prev => ({
                ...prev,
                selectedTableId: tableId
              }));
            }}
            onPriceChange={handlePriceChange}
            onQuantityDecrease={minusCartItemQuantity}
            onQuantityIncrease={addCartItemQuantity}
            onAddNote={btnOpenNotesModal}
            onRemoveItem={removeItemFromCart}
            onSendToKitchen={btnShowSendToKitchenModal}
            onPayAndSendToKitchen={btnShowPayAndSendToKitchenModal}
          />
        )}

        {/* Modals */}
        <dialog id="modal-notes" className="modal modal-bottom sm:modal-middle">
          <div className="modal-box">
            <h3 className="font-bold text-lg">Not Ekle</h3>

            <div className="my-4">
              <input type="hidden" ref={dialogNotesIndexRef} />
              <label htmlFor="dialogNotesText" className="mb-1 block text-gray-500 text-sm">Not <span className="text-xs text-gray-500">(Maks. 100 karakter.)</span></label>
              <input ref={dialogNotesTextRef} type="text" name="dialogNotesText" id='dialogNotesText' className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Notunuzu yazınız..." />
            </div>

            <div className="modal-action">
              <form method="dialog">
                <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
                <button onClick={btnAddNotes} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
              </form>
            </div>
          </div>
        </dialog>

        <dialog id="modal-variants-addons" className="modal modal-bottom sm:modal-middle">
          <div className="modal-box">
            <h3 className="font-bold text-lg">Seçenek Seçin</h3>

            <div className="my-4 flex gap-2">
              <div className="flex-1">
                <h3>Seçenekler</h3>
                <div className="flex flex-col gap-2 mt-2">
                  {menuItems.find((item)=>item.id==selectedItemId)?.variants?.map((variant, index)=>{
                    const {id, title, price} = variant;
                    return (
                      <label key={index} className='cursor-pointer label justify-start gap-2'>
                        <input type="radio" className='radio' name="variants" value={id} defaultChecked={index==0} />
                        <span className="label-text">{title} - {currency}{price}</span>
                      </label>
                    );
                  })}
                </div>
              </div>
              <div className="flex-1">
                <h3>Ekstralar</h3>
                <div className="flex flex-col gap-2 mt-2">
                  {menuItems.find((item)=>item.id==selectedItemId)?.addons?.map((addon, index)=>{
                    const {id, title, price} = addon;
                    return (
                      <label key={index} className='cursor-pointer label justify-start gap-2'>
                        <input type="checkbox" name="addons" className='checkbox checkbox-sm' value={id} />
                        <span className="label-text">{title} (+{currency}{price})</span>
                      </label>
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="modal-action">
              <form method="dialog">
                <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
                <button onClick={btnAddMenuItemToCartWithVariantsAndAddon} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
              </form>
            </div>
          </div>
        </dialog>

        <DialogAddCustomer
          defaultValue={state.addCustomerDefaultValue}
          onSuccess={(phone, name)=>{
            setCustomer({value: phone, label: `${name} - (${phone})`})
            document.getElementById("modal-search-customer").close();
          }}
        />

        <dialog id="modal-search-customer" className="modal modal-bottom sm:modal-middle">
          <div className="modal-box h-96">
            <div className="flex items-center justify-between">
              <h3 className="font-bold text-lg">Müşteri Ara</h3>
              <form method="dialog">
                <button onClick={btnClearSearchCustomer} className="btn btn-circle btn-sm bg-gray-50 hover:bg-gray-100 text-gray-500 border-none">
                  <IconRotate size={18} stroke={iconStroke}/>
                </button>
                <button className="ml-2 btn btn-circle btn-sm bg-red-50 hover:bg-red-100 text-red-500 border-none">
                  <IconX size={18} stroke={iconStroke}/>
                </button>
              </form>
            </div>

            <div className="my-4 flex items-end gap-2">
              <div className='flex-1'>
                <label htmlFor="searchCustomerRef" className="mb-1 block text-gray-500 text-sm">Müşteri Ara</label>
                <AsyncCreatableSelect
                  menuPlacement='auto'
                  loadOptions={searchCustomersAsync}
                  isClearable
                  placeholder="Müşteri adı veya telefon numarası..."
                  formatCreateLabel={(inputValue) => `"${inputValue}" ile yeni müşteri ekle`}
                  noOptionsMessage={()=>{return "İsim veya telefon numarası..."}}
                  onChange={setCustomer}
                  onCreateOption={(inputValue)=>{
                    setState({
                      ...state,
                      addCustomerDefaultValue: inputValue,
                    })
                    document.getElementById("modal-add-customer").showModal();
                  }}
                />
              </div>
            </div>
          </div>
        </dialog>

        <dialog id="modal-send-kitchen-summary" className="modal modal-bottom sm:modal-middle">
          <div className="modal-box">
            <h3 className="font-bold text-lg">Sipariş Onaylayın</h3>

            <div className="modal-action">
              <form method="dialog">
                <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Hayır</button>
                <button onClick={btnSendToKitchen} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Onaylıyorum</button>
              </form>
            </div>
          </div>
        </dialog>

        {/* dialog: qrorders */}
      <dialog id="modal-qrorders" className="modal">
        <div className="modal-box p-0 max-w-5xl w-11/12 max-h-[90vh]"> {/* Modalı daha büyük yaptık */}
          <div className="flex justify-between items-center sticky top-0 bg-white/80 backdrop-blur px-6 py-4 border-b">
            <h3 className="font-bold text-xl">QR Menu Siparişler</h3> {/* Başlık boyutunu büyüttük */}
            <form method="dialog" className="flex gap-2">
              <button onClick={btnClearQROrders} className="rounded-full hover:bg-red-200 transition active:scale-95 bg-red-50 text-red-500 w-10 h-10 flex items-center justify-center"> {/* Buton boyutunu büyüttük */}
                <IconClearAll stroke={iconStroke} size={20} /> {/* İkon boyutunu büyüttük */}
              </button>
              <button className="rounded-full hover:bg-gray-200 transition active:scale-95 bg-gray-100 text-gray-500 w-10 h-10 flex items-center justify-center"> {/* Buton boyutunu büyüttük */}
                <IconX stroke={iconStroke} size={20} /> {/* İkon boyutunu büyüttük */}
              </button>
            </form>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-3 gap-6 p-6 overflow-y-auto "> {/* Grid yapısını ve boşlukları büyüttük, overflow ekledik */}
            {state?.qrOrders?.map((qrOrder, index) => {
              const { customer_type, customer_id, customer_name, table_id, table_title, items, id } = qrOrder;

              return (
                <div
                  key={index}
                  onClick={() => btnSelectQROrder(qrOrder)}
                  className="flex items-center gap-4 rounded-2xl p-5 border border-gray-200 shadow-sm cursor-pointer hover:bg-gray-50 hover:shadow-md transition-all" /* Kartları büyüttük ve stil ekledik */
                >
                  <div className="w-16 h-16 rounded-full bg-gray-100 text-gray-500 flex items-center justify-center"> {/* İkon alanını büyüttük */}
                    <IconClipboardList stroke={iconStroke} size={24} /> {/* İkon boyutunu büyüttük */}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 text-sm text-gray-500 mb-1"> {/* Yazı boyutunu ve boşluğu büyüttük */}
                      <IconArmchair2 stroke={iconStroke} size={16} /> {/* İkon boyutunu büyüttük */}
                      <p className="font-medium">{table_title || "N/A"}</p> {/* Yazı stilini değiştirdik */}
                    </div>
                    <div className="flex items-center gap-2 text-sm mb-1"> {/* Yazı boyutunu ve boşluğu büyüttük */}
                      <IconUser stroke={iconStroke} size={16} /> {/* İkon boyutunu büyüttük */}
                      <p>{customer_type === "WALKIN" ? "WALKIN" : customer_name}</p>
                    </div>
                    <p className="text-sm font-medium mt-2">{items?.length} Sepetteki Ürünler</p> {/* Yazı boyutunu ve stilini değiştirdik */}
                  </div>
                  <div className="flex flex-col gap-2">
                    <button
                      onClick={(e) => { e.stopPropagation(); btnCancelQROrder(id); }}
                      className="rounded-full transition active:scale-95 text-red-500 w-10 h-10 bg-red-50 hover:bg-red-200 flex items-center justify-center" /* Buton boyutunu büyüttük */
                    >
                      <IconTrash size={20} stroke={iconStroke} /> {/* İkon boyutunu büyüttük */}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </dialog>



      {/* dialog: qrorders */}

        <PaymentAndKitchenModal
          itemsTotal={state.itemsTotal}
          taxTotal={state.taxTotal}
          payableTotal={state.payableTotal}
          currency={currency}
          paymentTypes={paymentTypes}
          selectedPaymentType={state.selectedPaymentType}
          onPaymentTypeChange={handlePaymentTypeChange}
          onConfirmPayment={btnPayAndSendToKitchen}
          paymentIcons={PAYMENT_ICONS}
        />

        {/* Cash Register Session Modal */}
        <CashRegisterSessionModal
          onSessionChange={handleSessionChange}
          maliModeActive={state.maliModeActive}
        />

        {/* Cash Withdrawal Modal */}
        <CashWithdrawalModal
          paymentTypes={paymentTypes}
          onSuccess={handleSessionChange}
        />

        {/* Cash Deposit Modal */}
        <CashDepositModal
          paymentTypes={paymentTypes}
          onSuccess={handleSessionChange}
        />

        {/* Last Transactions Modal */}
        <LastTransactionsModal activeSession={activeSession} />

      </div>
    </Page>
  )
}

// POSPage bileşenini doğrudan export ediyoruz
export default POSPageContent;
