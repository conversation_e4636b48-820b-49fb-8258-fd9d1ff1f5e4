import React, { useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { getUserFloorRestrictions, updateUserFloorRestrictions } from "../controllers/users.controller";
import { mutate } from "swr";
import { IconCheck, IconX } from "@tabler/icons-react";
import { iconStroke } from "../config/config";

export default function FloorRestrictionsModal({ username, floors, onSuccess, APIURL }) {
  const [loading, setLoading] = useState(true);
  const [floorRestrictions, setFloorRestrictions] = useState([]);
  const [selectedFloorIds, setSelectedFloorIds] = useState([]);

  useEffect(() => {
    if (username) {
      loadFloorRestrictions();
    }
  }, [username]);

  const loadFloorRestrictions = async () => {
    try {
      setLoading(true);
      const res = await getUserFloorRestrictions(username);
      if (res.status === 200) {
        setFloorRestrictions(res.data.data || []);
        
        // Set initially selected floor IDs
        const restrictedFloorIds = res.data.data
          .filter(floor => floor.is_restricted)
          .map(floor => floor.id);
        
        setSelectedFloorIds(restrictedFloorIds);
      }
    } catch (error) {
      console.error("Error loading floor restrictions:", error);
      toast.error("Kat kısıtlamaları yüklenirken hata oluştu!");
    } finally {
      setLoading(false);
    }
  };

  const handleToggleFloor = (floorId) => {
    setSelectedFloorIds(prev => {
      if (prev.includes(floorId)) {
        return prev.filter(id => id !== floorId);
      } else {
        return [...prev, floorId];
      }
    });
  };

  const handleSave = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateUserFloorRestrictions(username, selectedFloorIds);
      
      if (res.status === 200) {
        if (APIURL) {
          await mutate(APIURL);
        }
        toast.dismiss();
        toast.success("Kat kısıtlamaları başarıyla güncellendi!");
        
        if (typeof onSuccess === 'function') {
          onSuccess(selectedFloorIds);
        }
        
        document.getElementById("modal-floor-restrictions").close();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <dialog id="modal-floor-restrictions" className="modal modal-bottom sm:modal-middle">
      <div className="modal-box">
        <h3 className="font-bold text-lg">Kat Kısıtlamaları</h3>
        <p className="text-sm text-gray-500 mt-1">
          Kullanıcının erişim izni olmayan katları seçin. Seçilen katlara kullanıcı erişemeyecektir.
        </p>

        {loading ? (
          <div className="py-4 text-center">Yükleniyor...</div>
        ) : (
          <div className="mt-4 max-h-96 overflow-y-auto">
            {floorRestrictions.length === 0 ? (
              <div className="py-4 text-center">Hiç kat bulunamadı.</div>
            ) : (
              <div className="space-y-2">
                {floorRestrictions.map((floor) => (
                  <div 
                    key={floor.id} 
                    className={`p-3 border rounded-lg flex justify-between items-center cursor-pointer transition-colors ${
                      selectedFloorIds.includes(floor.id) 
                        ? 'bg-red-50 border-red-200' 
                        : 'bg-green-50 border-green-200'
                    }`}
                    onClick={() => handleToggleFloor(floor.id)}
                  >
                    <div>
                      <p className="font-medium">{floor.title}</p>
                      {floor.description && (
                        <p className="text-sm text-gray-500">{floor.description}</p>
                      )}
                    </div>
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                      selectedFloorIds.includes(floor.id) 
                        ? 'bg-red-100 text-red-500' 
                        : 'bg-green-100 text-green-500'
                    }`}>
                      {selectedFloorIds.includes(floor.id) ? (
                        <IconX size={16} stroke={iconStroke} />
                      ) : (
                        <IconCheck size={16} stroke={iconStroke} />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        <div className="modal-action">
          <form method="dialog">
            <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">
              Kapat
            </button>
          </form>
          <button
            onClick={handleSave}
            disabled={loading}
            className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
          >
            Kaydet
          </button>
        </div>
      </div>
    </dialog>
  );
}
