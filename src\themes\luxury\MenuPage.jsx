import React, { useEffect, useState } from "react";
import {
  IconChevronRight,
  IconChevronLeft,
} from "@tabler/icons-react";
import {
  getCart,
  setCart,
  getQRMenuInit,
} from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function MenuPage() {
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;

  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const { t, i18n } = useTranslation(['menu_item', 'category', 'menu_description']);

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    storeTable: null,
    categories: [],
    menuItems: [],
    searchQuery: "",
    cartItems: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);

      const storedCart = getCart();
      if (res.status == 200) {
        const data = res.data;

        const currency = CURRENCIES.find(
          (c) => c.cc == data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          ...state,
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          menuItems: data?.menuItems,
          storeTable: data?.storeTable || null,
          cartItems: [...storedCart],
          currency: currency?.symbol || "",
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - Menü - SewPOS`;
        }
      }
    } catch (error) {
      console.error("Menu loading error:", error);
    }
  };

  const {
    isLoading,
    storeSettings,
    categories,
    menuItems,
    searchQuery,
    cartItems,
    currency,
  } = state;

  const storeName = storeSettings?.store_name || "";
  const is_qr_menu_enabled = storeSettings?.is_qr_menu_enabled || false;
  const is_qr_order_enabled = storeSettings?.is_qr_order_enabled1 || false;
  const storeImage = storeSettings?.store_image;

  if (isLoading) {
    return <QRMenuLoading />;
  }

  if (!qrcode) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center font-serif">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border-2 border-amber-200">
          <p className="text-amber-800 font-bold text-xl">Broken Link!</p>
        </div>
      </div>
    );
  }

  if (!is_qr_menu_enabled) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center font-serif">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border-2 border-amber-200">
          <p className="text-amber-800 font-bold text-xl">Menu Not Available!</p>
        </div>
      </div>
    );
  }

  // cart functions
  function addItemToCart(item) {
    const modifiedItem = {
      ...item,
      quantity: 1,
      notes: null,
    };

    const newCart = cartItems;
    newCart.push(modifiedItem);

    setState({
      ...state,
      cartItems: [...newCart],
    });
    setCart(newCart);
  }

  // Kategoriye göre ürünleri grupla
  const getMenuItemsByCategory = () => {
    const filteredItems = menuItems.filter((item) => {
      if (searchQuery) {
        return item.title.toLowerCase().includes(searchQuery.toLowerCase());
      }
      return true;
    });

    const groupedItems = {};

    categories.forEach(category => {
      const categoryItems = filteredItems.filter(item => item.category_id === category.id);
      if (categoryItems.length > 0) {
        groupedItems[category.id] = {
          category: category,
          items: categoryItems
        };
      }
    });

    return groupedItems;
  };

  const menuByCategory = getMenuItemsByCategory();

  return (
    <div className="w-full min-h-screen" style={{ backgroundColor: '#f8f7f3', }}>
      {/* MINIMAL HEADER */}
      <header className="sticky top-0 z-10" style={{ backgroundColor: '#f8f7f3', }}>
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <IconChevronLeft
              size={24}
              className="cursor-pointer text-gray-600 hover:text-gray-800"
              onClick={() => navigate(-1)}
            />
            <div className="text-center">
              {storeImage ? (
                <img
                  src={getImageURL(storeImage)}
                  alt={storeName}
                  className="h-8 object-contain mx-auto"
                />
              ) : (
                <h1 className="text-lg font-semibold text-gray-900 italic">
                  {storeName}
                </h1>
              )}
            </div>
            <div className="w-6"></div>
          </div>
        </div>
      </header>
  
      
  
      {/* PDF STYLE MENU */}
      <div className="max-w-4xl mx-auto px-6 pb-8">
        {Object.keys(menuByCategory).length === 0 ? (
          <div className="text-center py-16">
            <p className="text-gray-500 text-sm italic" >
              {searchQuery
                ? "Aradığınız ürün bulunamadı"
                : "Menü yükleniyor..."
              }
            </p>
          </div>
        ) : (
          Object.values(menuByCategory).map(({ category, items }) => (
            <div key={category.id} className="mb-8">
              {/* Category Title - PDF Style */}
              <h2 className="text-base font-bold text-gray-900 mb-4 pb-2 border-b border-gray-300 italic" >
                {t(`category:${category.id}`, { defaultValue: category.title })}
              </h2>
  
              {/* Items List - PDF Style */}
              <div className="space-y-2">
                {items.map((item) => {
                  const { id, price, title } = item;
  
                  return (
                    <div key={id} className="flex justify-between items-center py-1">
                      <span className="text-gray-900 text-sm italic" >
                        {t(`menu_item:${id}`, { defaultValue: title })}
                      </span>
                      {price && parseFloat(price) > 0 && (
                        <span className="text-gray-900 font-medium text-sm ">
                          {price} {currency}
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))
        )}
      </div>
  
      {/* MINIMAL CART */}
      {is_qr_order_enabled && <div className="h-20" />}
  
      {is_qr_order_enabled && cartItems.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200 z-50">
          <div className="max-w-4xl mx-auto">
            <button
              onClick={() => {
                navigate(`/m/${qrcode}/cart`, {
                  state: { storeTable: state.storeTable, currency: currency },
                });
              }}
              className="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 transition-colors flex items-center justify-between"
            >
              <span>Sepet ({cartItems.length})</span>
              <IconChevronRight size={20} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
  
}