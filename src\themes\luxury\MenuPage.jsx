import React, { useEffect, useState } from "react";
import {
  IconCarrot,
  IconChevronRight,
  IconChevronLeft,
  IconCrown,
  IconStar,
  IconSearch,
} from "@tabler/icons-react";
import {
  getCart,
  setCart,
  getQRMenuInit,
} from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function MenuPage() {
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;

  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const { t, i18n } = useTranslation(['menu_item', 'category', 'menu_description']);

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    storeTable: null,
    categories: [],
    menuItems: [],
    searchQuery: "",
    cartItems: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);

      const storedCart = getCart();
      if (res.status == 200) {
        const data = res.data;

        const currency = CURRENCIES.find(
          (c) => c.cc == data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          ...state,
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          menuItems: data?.menuItems,
          storeTable: data?.storeTable || null,
          cartItems: [...storedCart],
          currency: currency?.symbol || "",
        });
      }
    } catch (error) {
      console.error("Menu loading error:", error);
    }
  };

  const {
    isLoading,
    storeSettings,
    categories,
    menuItems,
    searchQuery,
    cartItems,
    currency,
  } = state;

  const storeName = storeSettings?.store_name || "";
  const is_qr_menu_enabled = storeSettings?.is_qr_menu_enabled || false;
  const is_qr_order_enabled = storeSettings?.is_qr_order_enabled1 || false;
  const storeImage = storeSettings?.store_image;

  if (isLoading) {
    return <QRMenuLoading theme="luxury" />;
  }

  if (!qrcode) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center font-serif">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border-2 border-amber-200">
          <p className="text-amber-800 font-bold text-xl">Broken Link!</p>
        </div>
      </div>
    );
  }

  if (!is_qr_menu_enabled) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center font-serif">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border-2 border-amber-200">
          <p className="text-amber-800 font-bold text-xl">Menu Not Available!</p>
        </div>
      </div>
    );
  }

  // cart functions
  function addItemToCart(item) {
    const modifiedItem = {
      ...item,
      quantity: 1,
      notes: null,
    };

    const newCart = cartItems;
    newCart.push(modifiedItem);

    setState({
      ...state,
      cartItems: [...newCart],
    });
    setCart(newCart);
  }

  // Kategoriye göre ürünleri grupla
  const getMenuItemsByCategory = () => {
    const filteredItems = menuItems.filter((item) => {
      if (searchQuery) {
        return item.title.toLowerCase().includes(searchQuery.toLowerCase());
      }
      return true;
    });

    const groupedItems = {};

    categories.forEach(category => {
      const categoryItems = filteredItems.filter(item => item.category_id === category.id);
      if (categoryItems.length > 0) {
        groupedItems[category.id] = {
          category: category,
          items: categoryItems
        };
      }
    });

    return groupedItems;
  };

  const menuByCategory = getMenuItemsByCategory();

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 font-serif">
      {/* LUXURY HEADER */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-600 via-yellow-600 to-amber-700"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>

        <header className="relative z-10 px-6 py-6 sticky top-0">
          <div className="max-w-4xl mx-auto flex items-center justify-between">
            <IconChevronLeft
              size={28}
              stroke={3}
              className="cursor-pointer text-white hover:bg-white/20 rounded-full p-1 transition-all"
              onClick={() => navigate(-1)}
            />
            <div className="flex items-center space-x-3">
              <IconCrown size={24} className="text-yellow-200" />
              {storeImage ? (
                <img
                  src={getImageURL(storeImage)}
                  alt={storeName}
                  className="h-12 object-contain"
                />
              ) : (
                <h1 className="text-2xl font-bold text-white tracking-wide drop-shadow-lg">{storeName}</h1>
              )}
              <IconCrown size={24} className="text-yellow-200" />
            </div>
            <div className="w-7"></div>
          </div>
        </header>
      </div>

      {/* LUXURY SEARCH BAR */}
      <div className="px-6 -mt-4 relative z-20">
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-r from-white via-amber-50 to-yellow-50 rounded-2xl shadow-xl border-2 border-amber-200 p-4">
            <div className="relative">
              <IconSearch size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-600" />
              <input
                type="search"
                placeholder={t("searchPlaceholder", { defaultValue: "Lüks lezzetleri keşfedin..." })}
                className="w-full pl-10 pr-4 py-3 bg-white/80 rounded-xl border border-amber-200 focus:border-amber-500 focus:ring-2 focus:ring-amber-200 outline-none text-amber-900 placeholder-amber-600"
                value={searchQuery}
                onChange={(e) => {
                  setState({
                    ...state,
                    searchQuery: e.target.value,
                  });
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* LUXURY MENU BY CATEGORIES */}
      <div className="px-6 mt-8">
        <div className="max-w-4xl mx-auto">
          {Object.keys(menuByCategory).length === 0 ? (
            <div className="text-center py-16">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border-2 border-amber-200">
                <IconStar size={64} className="text-amber-400 mx-auto mb-4" />
                <p className="text-amber-700 text-xl font-bold">
                  {searchQuery
                    ? t("noSearchResults", { defaultValue: "Aradığınız ürün bulunamadı" })
                    : t("noMenuItems", { defaultValue: "Menü yükleniyor..." })
                  }
                </p>
              </div>
            </div>
          ) : (
            Object.values(menuByCategory).map(({ category, items }) => (
              <div key={category.id} className="mb-12">
                {/* Category Header */}
                <div className="text-center mb-8">
                  <div className="flex items-center justify-center space-x-4 mb-4">
                    <IconStar size={24} className="text-amber-600" />
                    <h2 className="text-3xl font-bold text-amber-800 tracking-wide">
                      {t(`category:${category.id}`, { defaultValue: category.name })}
                    </h2>
                    <IconStar size={24} className="text-amber-600" />
                  </div>
                  <div className="w-32 h-1 bg-gradient-to-r from-transparent via-amber-600 to-transparent mx-auto"></div>
                </div>

                {/* Category Items */}
                <div className="space-y-6">
                  {items.map((item) => {
                    const {
                      id,
                      image,
                      price,
                      description,
                      title,
                      sew_points,
                    } = item;

                    const imageURL = getImageURL(image);

                    return (
                      <div
                        key={id}
                        className="bg-gradient-to-r from-white via-amber-50 to-yellow-50 rounded-2xl p-6 shadow-lg border border-amber-200 hover:shadow-xl transition-all duration-300"
                      >
                        <div className="flex gap-6">
                          {/* Item Image */}
                          <div className="flex-shrink-0">
                            <div className="w-24 h-24 bg-gradient-to-br from-amber-100 to-yellow-100 rounded-xl flex items-center justify-center shadow-md border border-amber-300 overflow-hidden">
                              {image ? (
                                <img
                                  src={imageURL}
                                  alt={title}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <IconCarrot size={32} className="text-amber-600" />
                              )}
                            </div>
                          </div>

                          {/* Item Content */}
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h3 className="text-xl font-bold text-amber-800 mb-2">
                                  {t(`menu_item:${id}`, { defaultValue: title })}
                                </h3>

                                {description && (
                                  <p className="text-amber-700 mb-3 leading-relaxed">
                                    {t(`menu_description:${id}`, { defaultValue: description })}
                                  </p>
                                )}

                                {sew_points && (
                                  <div className="flex items-center space-x-2 mb-2">
                                    <IconCrown size={16} className="text-amber-600" />
                                    <p className="font-bold text-amber-700">HollyPuan: {sew_points}</p>
                                  </div>
                                )}
                              </div>

                              <div className="text-right ml-4">
                                <span className="text-2xl font-bold text-amber-600">
                                  {price} {currency}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* LUXURY CART */}
      {is_qr_order_enabled && <div className="h-28" />}

      {is_qr_order_enabled && cartItems.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 p-6 z-50">
          <div className="max-w-4xl mx-auto">
            <button
              onClick={() => {
                navigate(`/m/${qrcode}/cart`, {
                  state: { storeTable: state.storeTable, currency: currency },
                });
              }}
              className="w-full bg-gradient-to-r from-amber-600 via-yellow-600 to-amber-700 text-white py-6 px-8 rounded-2xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 flex items-center justify-between border-2 border-amber-400"
            >
              <div className="flex items-center space-x-3">
                <IconCrown size={28} />
                <span className="text-xl font-bold">
                  {cartItems.length} Premium Ürün
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-xl font-bold">Sepeti Görüntüle</span>
                <IconChevronRight size={24} stroke={3} />
              </div>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}