import React, { useState, useEffect } from "react";
import { usePrinters, addPrinter, updatePrinter, deletePrinter } from "../../controllers/settings.controller";
import { toast } from "react-hot-toast";

// Printer veri tiplerini tanımlıyoruz
interface Printer {
  id: number;
  tenant_id: number;
  name: string;
  path: string;
  type: "kitchen" | "cashier";
  created_at: string;
  updated_at: string;
}

export default function PrinterManagement() {
  const { data: printers, isLoading, error } = usePrinters();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [currentPrinter, setCurrentPrinter] = useState<Printer | null>(null);

  // Yeni yazıcı ekleme için başlangıç durumu
  const initialPrinterState: Omit<Printer, "id" | "tenant_id" | "created_at" | "updated_at"> = {
    name: "",
    path: "/default/path",
    type: "kitchen",
  };
  const [printerData, setPrinterData] = useState<typeof initialPrinterState>(initialPrinterState);


  // Seçilen yazıcıyı modal açıldığında form içine yerleştir
  useEffect(() => {
    if (currentPrinter) {
      setPrinterData(currentPrinter);
    } else {
      setPrinterData(initialPrinterState);
    }
  }, [currentPrinter]);

  const openModal = (printer: Printer | null = null) => {
    setCurrentPrinter(printer);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setCurrentPrinter(null);
    setIsModalOpen(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setPrinterData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    try {
      toast.loading("İşlem yapılıyor...");
      
      // Form alanlarının dolu olup olmadığını kontrol et
      if (!printerData.name || !printerData.path || !printerData.type) {
        toast.dismiss();
        toast.error("Lütfen tüm alanları doldurun.");
        return;
      }

      // Type doğrulama (kitchen / cashier dışında olamaz)
      if (!["kitchen", "cashier"].includes(printerData.type)) {
        toast.dismiss();
        toast.error("Geçersiz yazıcı türü.");
        return;
      }

      if (currentPrinter) {
        await updatePrinter(currentPrinter.id, printerData);
      } else {
        await addPrinter(printerData);
      }
      toast.dismiss();
      toast.success("İşlem başarılı!");
      closeModal();
    } catch (error: any) {
      toast.dismiss();
      toast.error(`Bir hata oluştu: ${error.response?.data?.message || error.message}`);
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Bu yazıcıyı silmek istediğinizden emin misiniz?")) {
      try {
        toast.loading("Siliniyor...");
        await deletePrinter(id);
        toast.dismiss();
        toast.success("Silme işlemi başarılı!");
      } catch (error: any) {
        toast.dismiss();
        toast.error(`Silme işlemi başarısız: ${error.response?.data?.message || error.message}`);
      }
    }
  };

  if (isLoading) return <p>Yükleniyor...</p>;
  if (error) return <p>Veri yüklenirken hata oluştu!</p>;

  return (
    <div className="mt-6">
      <h3 className="text-lg font-semibold mb-4">Yazıcı Yönetimi</h3>
      <button onClick={() => openModal()} className="bg-blue-600 text-white px-4 py-2 rounded-md">+ Yazıcı Ekle</button>
      <ul className="mt-4 border p-4 rounded-md">
        {printers && printers.length > 0 ? (
          printers.map((printer: Printer) => (
            <li key={printer.id} className="flex justify-between items-center py-2 border-b">
              <span>{printer.name} ({printer.path}) - {printer.type}</span>
              <div>
                <button onClick={() => openModal(printer)} className="text-yellow-500 mr-2">Düzenle</button>
                <button onClick={() => handleDelete(printer.id)} className="text-red-500">Sil</button>
              </div>
            </li>
          ))
        ) : (
          <p>Henüz eklenmiş yazıcı yok.</p>
        )}
      </ul>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center">
          <div className="bg-white p-6 rounded-md w-96">
            <h4 className="text-lg font-semibold mb-4">{currentPrinter ? "Yazıcıyı Düzenle" : "Yeni Yazıcı Ekle"}</h4>
            <input
              type="text"
              name="name"
              placeholder="Yazıcı Adı"
              className="border p-2 w-full mb-2"
              value={printerData.name}
              onChange={handleChange}
            />
            <input
              type="text"
              name="path"
              placeholder="Path"
              className="border p-2 w-full mb-2"
              value={printerData.path}
              onChange={handleChange}
            />
            <select
              name="type"
              className="border p-2 w-full mb-2"
              value={printerData.type}
              onChange={handleChange}
            >
              <option value="kitchen">Mutfak Yazıcısı</option>
              <option value="cashier">Kasiyer Yazıcısı</option>
            </select>
            <div className="flex justify-between">
              <button onClick={handleSave} className="bg-green-600 text-white px-4 py-2 rounded-md">Kaydet</button>
              <button onClick={closeModal} className="bg-gray-400 text-white px-4 py-2 rounded-md">İptal</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
