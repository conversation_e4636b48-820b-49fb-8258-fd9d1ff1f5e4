import React from 'react'
import Logo from "../assets/logo.svg";
import {
  IconChevronLeft,
  IconLogout,
  IconBasket,
  IconLayoutDashboard,
  IconRefresh
} from '@tabler/icons-react';
import { iconStroke } from '../config/config';
import { useNavigate } from 'react-router-dom';
import { signOut } from "../controllers/auth.controller";
import { toast } from "react-hot-toast";

export default function NoAccessPage() {
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      toast.loading("Lütfen bekleyin...");
      const response = await signOut();
      if (response.status == 200) {
        toast.dismiss();
        toast.success(response.data.message);
        navigate("/login", { replace: true });
      }
    } catch (error) {
      const message =
        error?.response?.data?.message || "Bir şeyler ters gitti! Try later!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <div className="bg-restro-green-light relative overflow-x-hidden min-h-screen">
      {/* Background SVG - Korundu */}
      <img 
        src="/assets/circle_illustration.svg" 
        alt="illustration" 
        className="absolute w-96 lg:w-[1024px] h-96 lg:h-[1024px] lg:-bottom-96 lg:-right-52 -right-36" 
      />
            

      <div className="flex flex-col items-center justify-center w-full min-h-screen container mx-auto px-4 py-8 relative">
        <div className="w-full max-w-4xl bg-restro-green-light bg-opacity-20 backdrop-filter backdrop-blur-sm rounded-2xl shadow-2xl p-8 flex flex-col items-center border border-white/10">

          {/* Başlık ve açıklama */}
          <h2 className="text-3xl font-bold text-center text-gray-800 mb-2">Erişim Engellendi</h2>
          <p className="text-xl text-center text-gray-600 mb-8">
            Bu alana erişim yetkiniz bulunmuyor. Lütfen yöneticinizle iletişime geçin veya farklı bir alana gidin.
          </p>

          {/* Seçenekler grid - Açık renkli butonlar ile */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full mb-8">
            <button
              onClick={() => navigate('/dashboard/pos')}
              className="flex flex-col items-center justify-center bg-blue-50 hover:bg-blue-100 text-blue-700 p-6 rounded-xl transition-all transform hover:scale-105 shadow-md hover:shadow-lg group"
            >
              <div className="p-4 rounded-full bg-blue-100 group-hover:bg-blue-200 mb-3">
                <IconBasket size={36} stroke={1.5} className="text-blue-600" />
              </div>
              <span className="text-lg font-medium">Satış Ekranı</span>
            </button>

            <button
              onClick={() => navigate('/dashboard/home')}
              className="flex flex-col items-center justify-center bg-green-50 hover:bg-green-100 text-green-700 p-6 rounded-xl transition-all transform hover:scale-105 shadow-md hover:shadow-lg group"
            >
              <div className="p-4 rounded-full bg-green-100 group-hover:bg-green-200 mb-3">
                <IconLayoutDashboard size={36} stroke={1.5} className="text-green-600" />
              </div>
              <span className="text-lg font-medium">Yönetim Paneli</span>
            </button>

            <button
              onClick={() => window.location.reload()}
              className="flex flex-col items-center justify-center bg-purple-50 hover:bg-purple-100 text-purple-700 p-6 rounded-xl transition-all transform hover:scale-105 shadow-md hover:shadow-lg group"
            >
              <div className="p-4 rounded-full bg-purple-100 group-hover:bg-purple-200 mb-3">
                <IconRefresh size={36} stroke={1.5} className="text-purple-600" />
              </div>
              <span className="text-lg font-medium">Sayfayı Yenile</span>
            </button>
          </div>

          {/* Alt butonlar */}
          <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
            <button
              onClick={() => navigate(-1)}
              className="flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-6 rounded-lg transition-all transform hover:scale-105 shadow-sm hover:shadow"
            >
              <IconChevronLeft stroke={iconStroke} />
              <span className="font-medium">Geri Dön</span>
            </button>

            <button
              onClick={handleLogout}
              className="flex-1 flex items-center justify-center gap-2 bg-red-50 hover:bg-red-100 text-red-600 py-3 px-6 rounded-lg transition-all transform hover:scale-105 shadow-sm hover:shadow"
            >
              <IconLogout stroke={iconStroke} />
              <span className="font-medium">Çıkış Yap</span>
            </button>
          </div>

          {/* Yardım metni */}
          <p className="text-gray-700 text-sm mt-8">
            Sorun devam ederse, lütfen sistem yöneticinizle iletişime geçin.
          </p>
        </div>
      </div>
    </div>
  )
}