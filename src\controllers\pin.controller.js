import ApiClient from "../helpers/ApiClient";
import {
  getPinSettings as getSettings,
  savePinSettings,
  updatePinSettingsOnVerify
} from "../helpers/LockScreenHelper";

export async function setPin(pin) {
  try {
    const response = await ApiClient.post("/pin/set-pin", { pin });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function initPOS() {
    try {
      const response = await ApiClient.get("/pos/init");
      return response;
    } catch (error) {
      throw error;
    }
}

export async function verifyPin(pin, tenant_id) {
  try {
    // Eğer tenant_id verilmişse, isteğe ekle
    const requestData = tenant_id ? { pin, tenant_id } : { pin };
    const response = await ApiClient.post("/pin/verify-pin", requestData);

    // Pin doğruysa ve kullanıcı bilgileri geldiyse
    if (response.status === 200 && response.data.user) {
      // PIN doğrulandığında, PIN ayarlarını güncelle
      updatePinSettingsOnVerify(response.data.user);

      // Eğer response doğrudan API'den geliyorsa, olduğu gibi döndür
      if (response.data.accessToken) {
        return response;
      }

      // Eski format için geriye dönük uyumluluk
      return {
        status: 200,
        data: {
          message: 'PIN doğrulandı',
          user: response.data.user
        }
      };
    }

    throw new Error('Geçersiz PIN!');
  } catch (error) {
    throw error;
  }
}

// Yeni helper fonksiyonlarını kullanarak PIN ayarlarını al
export function getPinSettings() {
  return getSettings();
}

// Yeni helper fonksiyonlarını kullanarak PIN ayarlarını kaydet
export function setPinSettings(settings) {
  savePinSettings(settings);
}