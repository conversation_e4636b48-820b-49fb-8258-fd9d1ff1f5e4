import React, { useRef, useState } from "react";
import * as XLSX from "xlsx"; // Excel dosyasını okumak için
import { toast } from "react-hot-toast";
import { BulkUpdateMenuItemsC } from "../controllers/menu_item.controller";

const BulkUpdateMenuItems = () => {
  const fileInputRef = useRef();
  const [parsedData, setParsedData] = useState([]);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(sheet);

        // Verileri formatla ve "Evet"/"Hayır" değerlerini 1/0'a dönüştür
        const formattedData = jsonData.map(item => ({
          id: item["Ürün ID"],
          title: item["Ürün Başlığı"],
          description: item["Açıklama"],
          price: item["Fiyat"],
          netPrice: item["Net Fiyat"],
          categoryId: item["Kategori ID"],
          taxID: item["Vergi ID"],
          stock: item["Stok"],
          stockActive: item["Stok Aktif Mi"] === "Evet" ? 1 : 0, // Evet -> 1, Hayır -> 0
          imageUrl: item["Resim URL"]
        }));

        setParsedData(formattedData);
        toast.success("Excel dosyası başarıyla yüklendi!");
      };
      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error(error);
      toast.error("Dosya okunurken bir hata oluştu!");
    }
  };

  const handleBulkUpdate = async () => {
    if (parsedData.length === 0) {
      toast.error("Güncellemek için veri bulunamadı!");
      return;
    }

    if (!parsedData.every(item => item.id && item.title && item.price)) {
      toast.error("Bazı ürünlerde ID, Başlık veya Fiyat eksik. Lütfen kontrol edin.");
      return;
    }

    try {
      toast.loading("Ürünler güncelleniyor...");
      const response = await BulkUpdateMenuItemsC(parsedData);
      toast.dismiss();

      if (response.status === 200) {
        toast.success(`${response.data.affectedRows} ürün başarıyla güncellendi!`);
        setParsedData([]);
      }
    } catch (error) {
      console.error(error);
      const message = error.response?.data?.message || "Ürünler güncellenirken bir hata oluştu!";
      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-lg font-bold mb-4">Excel ile Toplu Ürün Güncelleme</h2>

      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx, .xls"
        onChange={handleFileUpload}
        className="mb-4 block"
      />

      <button
        onClick={handleBulkUpdate}
        className="btn bg-blue-500 text-white hover:bg-blue-600"
      >
        Ürünleri Güncelle
      </button>

      {parsedData.length > 0 && (
        <div className="mt-4">
          <h3 className="font-bold mb-2">Önizleme</h3>
          <table className="table-auto w-full border">
            <thead>
              <tr>
                <th className="border px-2 py-1">ID</th>
                <th className="border px-2 py-1">Başlık</th>
                <th className="border px-2 py-1">Açıklama</th>
                <th className="border px-2 py-1">Fiyat</th>
                <th className="border px-2 py-1">Net Fiyat</th>
                <th className="border px-2 py-1">Kategori</th>
                <th className="border px-2 py-1">Stok</th>
                <th className="border px-2 py-1">Stok Aktif Mi</th>
                <th className="border px-2 py-1">Resim URL</th>
              </tr>
            </thead>
            <tbody>
              {parsedData.map((item, index) => (
                <tr key={index}>
                  <td className="border px-2 py-1">{item.id}</td>
                  <td className="border px-2 py-1">{item.title}</td>
                  <td className="border px-2 py-1">{item.description}</td>
                  <td className="border px-2 py-1">{item.price}</td>
                  <td className="border px-2 py-1">{item.netPrice}</td>
                  <td className="border px-2 py-1">{item.categoryTitle}</td>
                  <td className="border px-2 py-1">{item.stock}</td>
                  <td className="border px-2 py-1">{item.stockActive === 1 ? "Evet" : "Hayır"}</td>
                  <td className="border px-2 py-1">{item.imageUrl}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default BulkUpdateMenuItems;
