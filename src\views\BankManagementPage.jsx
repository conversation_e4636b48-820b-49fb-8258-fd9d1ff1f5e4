import React, { useState } from "react";
import Page from "../components/Page";
import { IconPlus, IconEdit, IconTrash, IconBuildingBank, IconToggleLeft, IconToggleRight } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { toast } from "react-hot-toast";
import { useBanks, addBank, updateBank, deleteBank, toggleBankStatus } from "../controllers/banks.controller";

export default function BankManagementPage() {
  const { banks, isLoading, error, mutate } = useBanks();
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingBank, setEditingBank] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    description: ""
  });

  // Loading durumu
  if (isLoading) {
    return (
      <Page>
        <div className="container mx-auto px-4 py-6">
          <div className="text-center py-8">
            <span className="loading loading-spinner loading-lg"></span>
            <p className="mt-4">Lütfen bekleyin...</p>
          </div>
        </div>
      </Page>
    );
  }

  // Error durumu
  if (error) {
    return (
      <Page>
        <div className="container mx-auto px-4 py-6">
          <div className="text-center py-8">
            <p className="text-red-600">Banka verileri yüklenirken hata oluştu. Lütfen daha sonra deneyin!</p>
          </div>
        </div>
      </Page>
    );
  }

  // Form submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error("Banka adı zorunludur!");
      return;
    }

    try {
      if (editingBank) {
        // Güncelleme
        await updateBank(editingBank.id, formData);
        toast.success("Banka başarıyla güncellendi!");
      } else {
        // Yeni ekleme
        await addBank(formData);
        toast.success("Banka başarıyla eklendi!");
      }
      
      // Form'u temizle ve modal'ı kapat
      setFormData({ name: "", description: "" });
      setEditingBank(null);
      setShowAddModal(false);
      
      // Listeyi yenile
      mutate();
    } catch (error) {
      const message = error?.response?.data?.message || "Bir hata oluştu!";
      toast.error(message);
    }
  };

  // Düzenleme modalını aç
  const handleEdit = (bank) => {
    setEditingBank(bank);
    setFormData({
      name: bank.name,
      description: bank.description || ""
    });
    setShowAddModal(true);
  };

  // Banka durumunu değiştir
  const handleToggleStatus = async (bank) => {
    try {
      const newStatus = bank.is_active ? 0 : 1;
      await toggleBankStatus(bank.id, newStatus);
      toast.success(`Banka ${newStatus ? 'aktif' : 'pasif'} hale getirildi!`);
      mutate();
    } catch (error) {
      const message = error?.response?.data?.message || "Durum değiştirilemedi!";
      toast.error(message);
    }
  };

  // Banka sil
  const handleDelete = async (bank) => {
    if (!confirm(`"${bank.name}" bankasını silmek istediğinize emin misiniz?`)) {
      return;
    }

    try {
      await deleteBank(bank.id);
      toast.success("Banka başarıyla silindi!");
      mutate();
    } catch (error) {
      const message = error?.response?.data?.message || "Banka silinemedi!";
      toast.error(message);
    }
  };

  // Modal'ı kapat
  const closeModal = () => {
    setShowAddModal(false);
    setEditingBank(null);
    setFormData({ name: "", description: "" });
  };

  return (
    <Page>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <IconBuildingBank size={32} stroke={iconStroke} className="text-blue-600" />
            <h1 className="text-2xl font-bold">Banka Yönetimi</h1>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="btn bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
          >
            <IconPlus size={18} stroke={iconStroke} />
            Yeni Banka Ekle
          </button>
        </div>

        {/* Banka Listesi */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          {banks.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left">Banka Adı</th>
                    <th className="text-left">Açıklama</th>
                    <th className="text-center">Durum</th>
                    <th className="text-center">Oluşturma Tarihi</th>
                    <th className="text-center">İşlemler</th>
                  </tr>
                </thead>
                <tbody>
                  {banks.map((bank) => (
                    <tr key={bank.id} className="hover:bg-gray-50">
                      <td className="font-medium">{bank.name}</td>
                      <td className="text-gray-600">{bank.description || "-"}</td>
                      <td className="text-center">
                        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          bank.is_active 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {bank.is_active ? 'Aktif' : 'Pasif'}
                        </span>
                      </td>
                      <td className="text-center text-gray-600">
                        {new Date(bank.created_at).toLocaleDateString('tr-TR')}
                      </td>
                      <td className="text-center">
                        <div className="flex justify-center gap-2">
                          <button
                            onClick={() => handleEdit(bank)}
                            className="btn btn-sm bg-blue-50 hover:bg-blue-100 text-blue-600"
                            title="Düzenle"
                          >
                            <IconEdit size={16} />
                          </button>
                          <button
                            onClick={() => handleToggleStatus(bank)}
                            className={`btn btn-sm ${
                              bank.is_active 
                                ? 'bg-red-50 hover:bg-red-100 text-red-600' 
                                : 'bg-green-50 hover:bg-green-100 text-green-600'
                            }`}
                            title={bank.is_active ? 'Pasif Yap' : 'Aktif Yap'}
                          >
                            {bank.is_active ? <IconToggleRight size={16} /> : <IconToggleLeft size={16} />}
                          </button>
                          <button
                            onClick={() => handleDelete(bank)}
                            className="btn btn-sm bg-red-50 hover:bg-red-100 text-red-600"
                            title="Sil"
                          >
                            <IconTrash size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <IconBuildingBank size={48} stroke={iconStroke} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">Henüz banka eklenmemiş</h3>
              <p className="text-gray-500 mb-4">İlk bankanızı eklemek için yukarıdaki butonu kullanın.</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="btn bg-blue-500 hover:bg-blue-600 text-white"
              >
                <IconPlus size={18} stroke={iconStroke} />
                Yeni Banka Ekle
              </button>
            </div>
          )}
        </div>

        {/* Add/Edit Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="font-bold text-lg mb-4">
                {editingBank ? 'Banka Düzenle' : 'Yeni Banka Ekle'}
              </h3>
              
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Banka Adı *
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="input input-bordered w-full"
                      placeholder="Örn: Ziraat Bankası"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Açıklama
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="textarea textarea-bordered w-full"
                      placeholder="Banka hakkında açıklama..."
                      rows="3"
                    />
                  </div>
                </div>
                
                <div className="flex justify-end gap-2 mt-6">
                  <button
                    type="button"
                    onClick={closeModal}
                    className="btn"
                  >
                    İptal
                  </button>
                  <button
                    type="submit"
                    className="btn bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    {editingBank ? 'Güncelle' : 'Kaydet'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </Page>
  );
}
