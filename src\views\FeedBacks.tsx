import React, { useState, useMemo } from "react";
import { 
  IconSearch, 
  IconFilter, 
  IconChevronDown, 
  IconChevronUp 
} from "@tabler/icons-react";
import Page from "../components/Page";
import { useFeedback } from "../controllers/feedbacks.controller";

export default function ProfilePage() {
  const { data, error, isLoading } = useFeedback();
  
  // State tanımlamaları
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [sortColumn, setSortColumn] = useState("created_at");
  const [sortDirection, setSortDirection] = useState("desc");

  // Filtreleme ve sıralama
  const processedFeedbacks = useMemo(() => {
    if (!data?.data) return [];

    return data.data
      .filter(feedback => {
        // Arama filtresi
        const matchesSearch = 
          feedback.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
          feedback.id.toString().includes(searchQuery);
        
        // Tip filtresi
        const matchesType = 
          filterType === "all" || 
          feedback.feedback_type === filterType;

        return matchesSearch && matchesType;
      })
      .sort((a, b) => {
        // Sıralama
        let comparison = 0;
        switch(sortColumn) {
          case "id":
            comparison = a.id - b.id;
            break;
          case "type":
            comparison = a.feedback_type.localeCompare(b.feedback_type);
            break;
          case "message":
            comparison = a.message.localeCompare(b.message);
            break;
          default: // created_at
            comparison = new Date(a.created_at) - new Date(b.created_at);
        }
        
        return sortDirection === "desc" ? -comparison : comparison;
      });
  }, [data, searchQuery, filterType, sortColumn, sortDirection]);

  // Sütun başlığı tıklaması
  const handleColumnSort = (column) => {
    if (sortColumn === column) {
      // Aynı sütun tıklanırsa sıralama yönünü değiştir
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Farklı sütun tıklanırsa sütunu değiştir ve varsayılan olarak azalan sıralama
      setSortColumn(column);
      setSortDirection("desc");
    }
  };

  if (isLoading) {
    return (
      <Page>
        <div className="text-center mt-4">Yükleniyor...</div>
      </Page>
    );
  }

  if (error) {
    return (
      <Page>
        <div className="text-center mt-4 text-red-500">
          Geri bildirimler yüklenirken bir hata oluştu.
        </div>
      </Page>
    );
  }

  return (
    <Page>
      <div className="container mx-auto px-4">
        <h3 className="text-center mt-8 text-2xl font-bold text-gray-800 mb-6">
          Geri Bildirimler
        </h3>

        {/* Filtreleme ve Arama Alanı */}
        <div className="mb-6 flex items-center gap-4">
          {/* Arama Input */}
          <div className="relative flex-grow max-w-md">
            <input
              type="text"
              placeholder="Ara (ID, mesaj)"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-2 pl-10 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>

          {/* Tip Filtresi */}
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="p-2 border rounded-lg"
          >
            <option value="all">Tüm Geri Bildirimler</option>
            <option value="thankyou">Teşekkür</option>
            <option value="complaint">Şikayet</option>
            <option value="suggestion">Öneri</option>
          </select>
        </div>

        {/* Tablo */}
        <div className="bg-white shadow overflow-hidden rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
<thead className="bg-gray-50">
  <tr>
    <th 
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
      onClick={() => handleColumnSort("id")}
    >
      <div className="flex items-center gap-2">
        ID
        {sortColumn === "id" && (
          <span className="text-blue-500">
            {sortDirection === "asc" ? "▲" : "▼"}
          </span>
        )}
      </div>
    </th>
    <th 
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
      onClick={() => handleColumnSort("type")}
    >
      <div className="flex items-center gap-2">
        Tür
        {sortColumn === "type" && (
          <span className="text-blue-500">
            {sortDirection === "asc" ? "▲" : "▼"}
          </span>
        )}
      </div>
    </th>
    <th 
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
      onClick={() => handleColumnSort("message")}
    >
      <div className="flex items-center gap-2">
        Mesaj
        {sortColumn === "message" && (
          <span className="text-blue-500">
            {sortDirection === "asc" ? "▲" : "▼"}
          </span>
        )}
      </div>
    </th>
    <th 
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer select-none"
      onClick={() => handleColumnSort("created_at")}
    >
      <div className="flex items-center gap-2">
        Tarih
        {sortColumn === "created_at" && (
          <span className="text-blue-500">
            {sortDirection === "asc" ? "▲" : "▼"}
          </span>
        )}
      </div>
    </th>
  </tr>
</thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {processedFeedbacks.length === 0 ? (
                <tr>
                  <td colSpan="4" className="px-6 py-4 text-center text-gray-500">
                    Filtrelenmiş sonuç bulunamadı.
                  </td>
                </tr>
              ) : (
                processedFeedbacks.map((feedback) => (
                  <tr key={feedback.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {feedback.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                          feedback.feedback_type === "thankyou"
                            ? "bg-green-100 text-green-700"
                            : feedback.feedback_type === "complaint"
                            ? "bg-red-100 text-red-700"
                            : "bg-blue-100 text-blue-700"
                        }`}
                      >
                        {feedback.feedback_type === "thankyou"
                          ? "Teşekkür"
                          : feedback.feedback_type === "complaint"
                          ? "Şikayet"
                          : "Öneri"}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {feedback.message}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(feedback.created_at).toLocaleString("tr-TR")}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </Page>
  );
}