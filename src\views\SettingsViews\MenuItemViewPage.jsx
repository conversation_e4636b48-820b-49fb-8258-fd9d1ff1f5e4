import React, { useEffect, useRef, useState } from "react";
import Page from "../../components/Page";
import { Link, useParams } from "react-router-dom";
import { addMenuItemAddon, addMenuItemRecipeItem, addMenuItemVariant, deleteMenuItemAddon, deleteMenuItemVariant, deleteRecipeItem, getMenuItem, removeMenuItemPhoto, updateMenuItem, updateMenuItemAddon, updateMenuItemRecipeItem, updateMenuItemVariant, uploadMenuItemPhoto, useMenuItem } from "../../controllers/menu_item.controller";
import { useCategories, useTaxes } from "../../controllers/settings.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";
import { IconCarrot, IconChevronDown, IconPencil, IconTrash, IconUpload } from "@tabler/icons-react";
import { iconStroke } from "../../config/config"
import imageCompression from "browser-image-compression";
import { getImageURL } from "../../helpers/ImageHelper";
import AsyncSelect from "react-select/async"

export default function MenuItemViewPage() {
  const params = useParams();
  const itemId = params.id;

  const titleRef = useRef();
  const descriptionRef = useRef();
  const priceRef = useRef();
  const netPriceRef = useRef();
  const taxIdRef = useRef();
  const categoryIdRef = useRef();

  const variantTitleRef = useRef();
  const variantPriceRef = useRef();

  const variantIdRef = useRef();
  const variantTitleUpdateRef = useRef();
  const variantPriceUpdateRef = useRef();

  const addonTitleRef = useRef();
  const addonPriceRef = useRef();

  const addonIdRef = useRef();
  const addonTitleUpdateRef = useRef();
  const addonPriceUpdateRef = useRef();

  const [activeAddRecipeItemTab, setActiveAddRecipeItemTab] = useState("item");
  const quantityRef = useRef();
  const [selectedRecipeData, setSelectedRecipeData] = useState({
    ingredient: null, // selected ingredient (inventory item)
    selectedBase: null, // can be item, variant, or addon
  });

  const [editRecipeData, setEditRecipeData] = useState({
    id: null,
    ingredient: null,
    quantity: null,
    selectedBase: null,
    baseType: "item", // can be item, variant, or addon
  });

  const quantityEditRef = useRef(null);

  const {
    APIURL: APIURLCategories,
    data: categories,
    error: errorCategories,
    isLoading: isLoadingCategories,
  } = useCategories();

  const {
    APIURL: APIURLTaxes,
    data: taxes,
    error: errorTaxes,
    isLoading: isLoadingTaxes,
  } = useTaxes();

  // const { APIURL, data: menuItem, error, isLoading } = useMenuItem(itemId);
  const [state, setState] = useState({
    menuItem: {},
    inventoryItems:[],
    variants:[],
    addons:[],
    recipeItems:[]
  })

  useEffect(()=>{
    _init(itemId)
  },[itemId]);

  const _init = async (id) => {
    try {
      const res = await getMenuItem(id);
      if(res.status == 200) {
       setTimeout(() => {
        if(taxIdRef.current){
          taxIdRef.current.value = res.data?.formattedMenuItem?.tax_id;
        }

        if(categoryIdRef.current){
          categoryIdRef.current.value = res.data?.formattedMenuItem?.category_id;
        }
       }, 100)

        setState({
          ...state,
          menuItem: res.data?.formattedMenuItem || {},
          variants: res.data?.formattedMenuItem?.variants || [],
          addons: res.data?.formattedMenuItem?.addons || [],
          recipeItems: res.data?.formattedMenuItem?.recipeItems || [],
          inventoryItems: res.data?.inventoryItems || []
        })
      }
    } catch (error) {
      console.log(error);
    }
  }

  if (isLoadingCategories) {
    return <Page>Lütfen bekleyin...</Page>;
  }

  if (errorCategories) {
    return <Page>Detaylar yüklenirken hata oluştu! Lütfen daha sonra tekrar deneyin.</Page>;
  }

  if (isLoadingTaxes) {
    return <Page>Lütfen bekleyin...</Page>;
  }

  if (errorTaxes) {
    return <Page>Detaylar yüklenirken hata oluştu! Lütfen daha sonra tekrar deneyin.</Page>;
  }

  // if (isLoading) {
  //   return <Page>Lütfen bekleyin...</Page>;
  // }

  // if (error) {
  //   return <Page>Detaylar yüklenirken hata oluştu! Lütfen daha sonra tekrar deneyin.</Page>;
  // }

  const {
    id,
    title,
    description,
    category_id,
    category_title,
    tax_id,
    tax_title,
    tax_rate,
    tax_type,
    price,
    net_price,
    addons,
    variants,
    image
  } = state.menuItem;
  const imageURL = image ? getImageURL(image) : null;


  async function btnSave() {
    const title = titleRef.current.value;
    const description = descriptionRef.current.value;
    const price = priceRef.current.value;
    const netPrice = netPriceRef.current.value || null;
    const categoryId = categoryIdRef.current.value || null;
    const taxId = taxIdRef.current.value || null;

    if(!title) {
      toast.error("Lütfen başlık giriniz!");
      return;
    }

    if(price < 0) {
      toast.error("Lütfen geçerli bir fiyat giriniz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateMenuItem(id, title, description, price, netPrice, categoryId, taxId);

      if(res.status == 200) {
        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error.response.data.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  }

  const btnVariantDelete = async (variantId) => {
    const isConfirm = window.confirm("Emin misiniz? Bu işlem geri alınamaz!");

    if(!isConfirm) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteMenuItemVariant(id, variantId);

      if(res.status == 200) {
        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  const btnAddonDelete = async (addonId) => {
    const isConfirm = window.confirm("Emin misiniz? Bu işlem geri alınamaz!");

    if(!isConfirm) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteMenuItemAddon(id, addonId);

      if(res.status == 200) {
        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  async function btnAddVariant() {
    const variantTitle = variantTitleRef.current.value;
    const variantPrice = variantPriceRef.current.value || 0;

    if(!variantTitle) {
      toast.error("Lütfen varyasyon başlığı giriniz!");
      return;
    }
    if(variantPrice < 0) {
      toast.error("Lütfen geçerli bir varyasyon fiyatı giriniz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await addMenuItemVariant(id, variantTitle, variantPrice);

      if(res.status == 200) {
        variantTitleRef.current.value = null;
        variantPriceRef.current.value = null;

        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  }

  const btnShowVariantUpdate = (variantId, title, price) => {
    variantIdRef.current.value = variantId;
    variantTitleUpdateRef.current.value = title;
    variantPriceUpdateRef.current.value = price;
    document.getElementById('modal-update-variant').showModal()
  };

  async function btnUpdateVariant() {
    const variantId = variantIdRef.current.value;
    const variantTitle = variantTitleUpdateRef.current.value;
    const variantPrice = variantPriceUpdateRef.current.value || 0;

    if(!variantTitle) {
      toast.error("Lütfen varyasyon başlığı giriniz!");
      return;
    }
    if(variantPrice < 0) {
      toast.error("Lütfen geçerli bir varyasyon fiyatı giriniz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateMenuItemVariant(id, variantId, variantTitle, variantPrice);

      if(res.status == 200) {
        variantIdRef.current.value = null;
        variantTitleUpdateRef.current.value = null;
        variantPriceUpdateRef.current.value = null;

        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  }

  async function btnAddAddon() {
    const addonTitle = addonTitleRef.current.value;
    const addonPrice = addonPriceRef.current.value || 0;

    if(!addonTitle) {
      toast.error("Lütfen eklenti başlığı giriniz!");
      return;
    }
    if(addonPrice < 0) {
      toast.error("Lütfen geçerli bir eklenti fiyatı giriniz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await addMenuItemAddon(id, addonTitle, addonPrice);

      if(res.status == 200) {
        addonTitleRef.current.value = null;
        addonPriceRef.current.value = null;

        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  }

  const btnShowAddonUpdate = (addonId, title, price) => {
    addonIdRef.current.value = addonId;
    addonTitleUpdateRef.current.value = title;
    addonPriceUpdateRef.current.value = price;
    document.getElementById('modal-update-addon').showModal()
  };

  async function btnUpdateAddon() {
    const addonId = addonIdRef.current.value;
    const addonTitle = addonTitleUpdateRef.current.value;
    const addonPrice = addonPriceUpdateRef.current.value || 0;

    if(!addonTitle) {
      toast.error("Lütfen eklenti başlığı giriniz!");
      return;
    }
    if(addonPrice < 0) {
      toast.error("Lütfen geçerli bir eklenti fiyatı giriniz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateMenuItemAddon(id, addonId, addonTitle, addonPrice);

      if(res.status == 200) {
        addonIdRef.current.value = null;
        addonTitleUpdateRef.current.value = null;
        addonPriceUpdateRef.current.value = null;

        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  }

  const handleFileChange = async (e) => {

    const file = e.target.files[0];

    if(!file) {
      return;
    }

    // compress image
    try {
      toast.loading("Lütfen bekleyin...");
      const compressedImage = await imageCompression(file, {
        maxSizeMB: 0.5,
        maxWidthOrHeight: 512,
        useWebWorker: true,
      })

      const formData = new FormData();
      formData.append("image", compressedImage);

      const res = await uploadMenuItemPhoto(itemId, formData);
      if(res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);

        // update the image state
        const imagePath = res.data.imageURL;
        await _init(itemId);
        location.reload();
      }

    } catch (error) {
      console.error(error);
      toast.dismiss();
      const message = error?.response?.data?.message || "Resim yüklenirken bir hata oluştu!";
      toast.error(message)
    }
  }

  const btnRemoveMenuItemImage = async () => {
    const isConfirm = window.confirm("Resmi kaldırmak istediğinizden emin misiniz?");

    if(!isConfirm) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");

      const res = await removeMenuItemPhoto(itemId);
      if(res.status == 200) {
        toast.dismiss();
        toast.success(res.data.message);
        await _init(itemId);
        location.reload();
      }

    } catch (error) {
      console.error(error);
      toast.dismiss();
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      toast.error(message)
    }
  }


  const getBaseIdOptions = () => {
    const data =
      activeAddRecipeItemTab == "variant"
        ? state.variants
        : activeAddRecipeItemTab == "addon"
        ? state.addons
        : [];

    return data.map((item) => ({
      label: item.title,
      value: item.id,
    }));
  };

  const getIngredientsOptions = () => {
    return state.inventoryItems?.map((item) => ({
      label: item.title,
      value: item.id,
      unit:item.unit
    }));
  };

  async function btnAddRecipeItem() {
    const qty = parseFloat(quantityRef.current?.value);

    if (!selectedRecipeData.ingredient) {
      toast.error("Lütfen bir malzeme seçin.");
      return;
    }

    if (isNaN(qty) || qty <= 0) {
      toast.error("Lütfen geçerli bir miktar girin.");
      return;
    }

    if ((activeAddRecipeItemTab === "variant" || activeAddRecipeItemTab === "addon") && !selectedRecipeData.selectedBase) {
      toast.error("Lütfen bir varyasyon veya eklenti seçin.");
      return;
    }

    let variantId = 0;
    let addonId = 0;

    if(activeAddRecipeItemTab == 'variant'){
      variantId = selectedRecipeData?.selectedBase?.id;
    }else if(activeAddRecipeItemTab == 'addon'){
      addonId = selectedRecipeData?.selectedBase?.id;
    }

    try {
      toast.loading("Lütfen bekleyin...");

      const res = await addMenuItemRecipeItem({
        menuItemId: itemId,
        variantId,
        addonId,
        ingredientId: selectedRecipeData?.ingredient?.value,
        quantity:qty,
      });

      if (res.status === 200) {
        toast.dismiss();
        toast.success(res.data.message);

        quantityRef.current.value = null;
        setSelectedRecipeData({
          ingredient:null,
          selectedBase:null
        });
        setActiveAddRecipeItemTab('item')

        document.getElementById('modal-add-recipe-item').close();

        await _init(itemId);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  }

  async function btnUpdateRecipeItem() {
    const qty = parseFloat(quantityEditRef.current?.value);

    if (!editRecipeData.ingredient) {
      toast.error("Lütfen bir malzeme seçin.");
      return;
    }

    if (isNaN(qty) || qty <= 0) {
      toast.error("Lütfen geçerli bir miktar girin.");
      return;
    }

    if ((editRecipeData.baseType === "variant" || editRecipeData.baseType === "addon") && !editRecipeData.selectedBase) {
      toast.error("Lütfen bir varyasyon veya eklenti seçin.");
      return;
    }

    let variantId = 0;
    let addonId = 0;

    if (editRecipeData.baseType === "variant") {
      variantId = editRecipeData?.selectedBase?.id;
    } else if (editRecipeData.baseType === "addon") {
      addonId = editRecipeData?.selectedBase?.id;
    }

    try {
      toast.loading("Güncelleniyor...");

      const res = await updateMenuItemRecipeItem({
        id: editRecipeData.id,
        menuItemId: itemId,
        variantId,
        addonId,
        ingredientId: editRecipeData?.ingredient?.value,
        quantity: qty,
      });

      if (res.status === 200) {
        toast.dismiss();
        toast.success(res.data.message);

        document.getElementById("modal-edit-recipe-item").close();
        await _init(itemId);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  }

  const btnDeleteRecipeItem = async (recipeItemId, recipeItemVariantId, recipeItemAddonId) => {
    const isConfirm = window.confirm("Emin misiniz? Bu işlem geri alınamaz!");

    if(!isConfirm) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteRecipeItem(itemId, recipeItemId, recipeItemVariantId, recipeItemAddonId);

      if(res.status == 200) {
        await _init(itemId);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <Page className="px-4 md:px-8 py-3 md:py-6">
      <div className="text-sm breadcrumbs">
        <ul>
          <li>
            <Link to="/dashboard/settings">Ayarlar</Link>
          </li>
          <li>
            <Link to="/dashboard/settings/menu-items">Menü Ürünleri</Link>
          </li>
          <li>{title}</li>
        </ul>
      </div>

      <div className="my-6 flex gap-6 flex-col lg:flex-row">
        <div className="">
          <div className="relative w-32 h-32 md:w-64 md:h-64 bg-gray-200 rounded-2xl flex items-center justify-center text-gray-600 text-2xl mb-4">

            {
              image ? <div className="w-full h-full relative top-0 left-0">
                <img src={imageURL} alt={title} className="w-full h-full absolute top-0 left-0 rounded-2xl object-cover" />
              </div>:
              <p className="absolute"><IconCarrot stroke={iconStroke} /></p>
            }


            {/* upload image options */}
            <div className="absolute bottom-2 md:bottom-auto md:top-4 md:right-4 flex items-center gap-2">
              <label htmlFor="file" className="flex items-center justify-center w-9 h-9 rounded-full bg-white shadow hover:bg-slate-100 cursor-pointer transition active:scale-95">
                <IconUpload stroke={iconStroke} size={18} />
                <input onChange={handleFileChange} type="file" name="file" id="file" className="hidden" accept="image/*" />
              </label>

              <button onClick={btnRemoveMenuItemImage} className="flex items-center justify-center w-9 h-9 rounded-full bg-white shadow hover:bg-slate-100 cursor-pointer transition active:scale-95 text-red-500">
                <IconTrash stroke={iconStroke} size={18} />
              </button>
            </div>
            {/* upload image options */}
          </div>
          <p>Fiyat: {price}</p>
          {net_price ? (
            <p className="text-sm text-gray-500">Net Fiyat: {net_price}</p>
          ): <></>}
          {category_id ? (
            <p className="text-sm text-gray-500">Kategori: {category_title}</p>
          ):<></>}
          {tax_id && (
            <p className="text-sm text-gray-500">
              KDV: {tax_title} - {tax_rate}% ({tax_type})
            </p>
          )}

          <button onClick={btnSave} className="mt-6 text-white w-full bg-restro-green transition hover:bg-restro-green/80 active:scale-95 rounded-lg px-4 py-2 outline-restro-border-green-light">Kaydet</button>
        </div>
        <div className="flex-1">
          <div className="">
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">
              Başlık
            </label>
            <input
              ref={titleRef}
              defaultValue={title}
              type="text"
              name="title"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Başlık giriniz"
            />
          </div>

          <div className="mt-4">
          <label htmlFor="description" className="mb-1 block text-gray-700 text-sm font-medium">
            Açıklama
            <span className="text-xs text-gray-500 ml-1">(Maks. 500 karakter)</span>
          </label>
            <textarea
              ref={descriptionRef}
              defaultValue={description}
              name="description"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Açıklama giriniz"
              rows="6"
              maxLength={500}
            />
          </div>

          <div className="flex gap-4 w-full my-4 flex-col lg:flex-row">
            <div className="flex-1">
              <label
                htmlFor="price"
                className="mb-1 block text-gray-500 text-sm"
              >
                Fiyat
              </label>
              <input
                ref={priceRef}
                defaultValue={price}
                type="number"
                name="price"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Fiyat giriniz"
              />
            </div>
            <div className="flex-1">
              <label
                htmlFor="nprice"
                className="mb-1 block text-gray-500 text-sm"
              >
                Net Fiyat
              </label>
              <input
                ref={netPriceRef}
                type="number"
                name="nprice"
                defaultValue={net_price}
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Net fiyat giriniz"
              />
            </div>
          </div>

          <div className="flex gap-4 w-full my-4 flex-col lg:flex-row">
            <div className="flex-1">
              <label
                htmlFor="category"
                className="mb-1 block text-gray-500 text-sm"
              >
                Kategori
              </label>
              <select
                ref={categoryIdRef}
                defaultValue={category_id}
                name="category"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Kategori seçiniz"
              >
                <option value="">Hiçbiri</option>
                {categories.map((category, index) => {
                  return (
                    <option value={category.id} key={category.id}>
                      {category.title}
                    </option>
                  );
                })}
              </select>
            </div>
            <div className="flex-1">
              <label htmlFor="tax" className="mb-1 block text-gray-500 text-sm">
                KDV
              </label>
              <select
                ref={taxIdRef}
                name="tax"
                defaultValue={tax_id}
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="KDV seçiniz"
              >
                <option value="">Hiçbiri</option>
                {taxes.map((tax, index) => {
                  return (
                    <option value={tax.id} key={tax.id}>
                      {tax.title} - {tax.rate}% ({tax.type})
                    </option>
                  );
                })}
              </select>
            </div>
          </div>

          {/* variants */}
          <div className="collapse bg-gray-50 collapse-arrow mt-6">
            <input type="checkbox" />
            <div className="collapse-title font-medium">
              Varyasyonları Göster
            </div>
            <div className="collapse-content flex flex-col">
              {
                state.variants?.map((variant, index)=>{
                  return <div key={variant.id} className="flex items-center justify-between hover:bg-gray-100 transition p-2 rounded-lg cursor-pointer">
                    <div className="flex-1">
                      <p>{variant.title}</p>
                      <p className="text-xs text-gray-500">Fiyat: {variant.price}</p>
                    </div>
                    <div className="flex items-center">
                      <button
                        onClick={() => {
                          btnShowVariantUpdate(variant.id, variant.title, variant.price);
                        }}
                        className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-200 transition active:scale-95"
                      >
                        <IconPencil stroke={iconStroke} />
                      </button>
                      <button
                        onClick={() => {
                          btnVariantDelete(variant.id);
                        }}
                        className="w-8 h-8 rounded-full flex items-center justify-center text-red-500 hover:bg-gray-200 transition active:scale-95"
                      >
                        <IconTrash stroke={iconStroke} />
                      </button>
                    </div>
                  </div>
                })
              }

              <button onClick={()=>document.getElementById('modal-add-variant').showModal()} className="btn btn-sm mt-4">Varyasyon Ekle</button>
            </div>
          </div>
          {/* variants */}

          {/* addons */}
          <div className="collapse bg-gray-50 collapse-arrow mt-4">
            <input type="checkbox" />
            <div className="collapse-title font-medium">
              Eklentileri Göster
            </div>
            <div className="collapse-content flex flex-col">
              {
                state.addons?.map((addon, index)=>{
                  return <div key={addon.id} className="flex items-center justify-between hover:bg-gray-100 transition p-2 rounded-lg cursor-pointer">
                    <div className="flex-1">
                      <p>{addon.title}</p>
                      <p className="text-xs text-gray-500">Fiyat Artışı: +{addon.price}</p>
                    </div>
                    <div className="flex items-center">
                      <button
                        onClick={() => {
                          btnShowAddonUpdate(addon.id, addon.title, addon.price);
                        }}
                        className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-200 transition active:scale-95"
                      >
                        <IconPencil stroke={iconStroke} />
                      </button>
                      <button
                        onClick={() => {
                          btnAddonDelete(addon.id);
                        }}
                        className="w-8 h-8 rounded-full flex items-center justify-center text-red-500 hover:bg-gray-200 transition active:scale-95"
                      >
                        <IconTrash stroke={iconStroke} />
                      </button>
                    </div>
                  </div>
                })
              }
              <button onClick={()=>document.getElementById('modal-add-addon').showModal()} className="btn btn-sm mt-4">Eklenti Ekle</button>
            </div>
          </div>
          {/* addons */}

          {/* recipe */}
          <div className="collapse bg-gray-50 collapse-arrow mt-4">
            <input type="checkbox" />
            <div className="collapse-title font-medium">
              Tarif Malzemeleri
            </div>
            <div className="collapse-content flex flex-col">
              {
                state.recipeItems?.map((item, index)=>{
                  return <div key={item.id} className="flex items-center justify-between hover:bg-gray-100 transition p-2 rounded-lg cursor-pointer">
                    <div className="flex-1">
                      <p className="flex items-center">
                        {item.ingredient_title}
                        {item.variant_title && <span className="bg-gray-200 ml-2 px-2 text-xs rounded-full">{item.variant_title}</span>}
                        {item.addon_title && <span className="bg-gray-200 ml-2 px-2 text-xs rounded-full">{item.addon_title}</span>}
                      </p>
                      <p className="text-xs text-gray-500">({item.quantity} {item.unit})</p>
                    </div>
                    <div className="flex items-center">
                      <button
                        onClick={() => {
                          setEditRecipeData({
                            id: item.id,
                            quantity: item.quantity,
                            ingredient: {
                              value: item.inventory_item_id,
                              label: item.ingredient_title,
                              unit: item.unit
                            },
                            selectedBase: item.variant_id
                              ? { id: item.variant_id, label: item.variant_title }
                              : item.addon_id
                                ? { id: item.addon_id, label: item.addon_title }
                                : null,
                            baseType: item.variant_id
                              ? "variant"
                              : item.addon_id
                                ? "addon"
                                : "item",
                          });
                          setTimeout(() => {
                            if (quantityEditRef.current) quantityEditRef.current.value = item.quantity;
                          }, 100);
                          const activeTab = item.variant_id ? "variant" : item.addon_id ? "addon" : "item"
                          setActiveAddRecipeItemTab(activeTab)
                          document.getElementById("modal-edit-recipe-item").showModal();
                        }}
                        className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-200 transition active:scale-95"
                      >
                        <IconPencil stroke={iconStroke} />
                      </button>
                      <button
                        onClick={() => {
                          btnDeleteRecipeItem(item.id, item.variant_id, item.addon_id)
                        }}
                        className="w-8 h-8 rounded-full flex items-center justify-center text-red-500 hover:bg-gray-200 transition active:scale-95"
                      >
                        <IconTrash stroke={iconStroke} />
                      </button>
                    </div>
                  </div>
                })
              }
              <button onClick={()=>document.getElementById('modal-add-recipe-item').showModal()} className="btn btn-sm mt-4">Malzeme Ekle</button>
            </div>
          </div>
          {/* recipe / inventory */}

        </div>
      </div>

      {/* variant add dialog */}
      <dialog id="modal-add-variant" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Varyasyon Ekle</h3>

          <div className="mt-4">
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">Varyasyon Başlığı</label>
            <input ref={variantTitleRef} type="text" name="title" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Varyasyon başlığı giriniz" />
          </div>

          <div className="my-4">
            <label htmlFor="price" className="mb-1 block text-gray-500 text-sm">Varyasyon Fiyatı</label>
            <input ref={variantPriceRef} type="number" name="price" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Varyasyon fiyatı giriniz" />

            <p className="text-xs text-gray-500 mt-1">Bu fiyat, ürün fiyatına ek olarak uygulanacaktır.</p>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
              <button onClick={()=>{btnAddVariant();}} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
            </form>
          </div>
        </div>
      </dialog>
      {/* variant add dialog */}

      {/* variant update dialog */}
      <dialog id="modal-update-variant" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Varyasyon Güncelle</h3>

          <div className="mt-4">
            <input type="hidden" ref={variantIdRef} />
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">Varyasyon Başlığı</label>
            <input ref={variantTitleUpdateRef} type="text" name="title" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Varyasyon başlığı giriniz" />
          </div>

          <div className="my-4">
            <label htmlFor="price" className="mb-1 block text-gray-500 text-sm">Varyasyon Fiyatı</label>
            <input ref={variantPriceUpdateRef} type="number" name="price" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Varyasyon fiyatı giriniz" />

            <p className="text-xs text-gray-500 mt-1">Bu fiyat, ürün fiyatına ek olarak uygulanacaktır.</p>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
              <button onClick={()=>{btnUpdateVariant();}} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
            </form>
          </div>
        </div>
      </dialog>
      {/* variant update dialog */}

      {/* addon add dialog */}
      <dialog id="modal-add-addon" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Eklenti Ekle</h3>

          <div className="mt-4">
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">Eklenti Başlığı</label>
            <input ref={addonTitleRef} type="text" name="title" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Eklenti başlığı giriniz" />
          </div>

          <div className="my-4">
            <label htmlFor="price" className="mb-1 block text-gray-500 text-sm">Eklenti Fiyatı</label>
            <input ref={addonPriceRef} type="number" name="price" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Eklenti fiyatı giriniz" />

            <p className="text-xs text-gray-500 mt-1">Bu fiyat, ürün fiyatına ek olarak uygulanacaktır.</p>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
              <button onClick={()=>{btnAddAddon();}} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
            </form>
          </div>
        </div>
      </dialog>
      {/* addon add dialog */}

      {/* addon update dialog */}
      <dialog id="modal-update-addon" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Eklenti Güncelle</h3>

          <div className="mt-4">
            <input type="hidden" ref={addonIdRef} />
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">Eklenti Başlığı</label>
            <input ref={addonTitleUpdateRef} type="text" name="title" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Eklenti başlığı giriniz" />
          </div>

          <div className="my-4">
            <label htmlFor="price" className="mb-1 block text-gray-500 text-sm">Eklenti Fiyatı</label>
            <input ref={addonPriceUpdateRef} type="number" name="price" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Eklenti fiyatı giriniz" />

            <p className="text-xs text-gray-500 mt-1">Bu fiyat, ürün fiyatına ek olarak uygulanacaktır.</p>
          </div>

          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">Kapat</button>
              <button onClick={()=>{btnUpdateAddon();}} className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3">Kaydet</button>
            </form>
          </div>
        </div>
      </dialog>
      {/* addon update dialog */}

            {/* recipe add dialog */}
            <dialog
        id="modal-add-recipe-item"
        className="modal modal-bottom sm:modal-middle"
      >
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Tarif Malzemesi Ekle</h3>

          {/* Tabs */}
          <div role="tablist" className="tabs my-4">
            {["item", "variant", "addon"].map((tab) => {
              let tabName = "";
              if (tab === "item") tabName = "Ürün";
              else if (tab === "variant") tabName = "Varyasyon";
              else if (tab === "addon") tabName = "Eklenti";

              return (
                <a
                  key={tab}
                  role="tab"
                  className={`tab ${
                    activeAddRecipeItemTab === tab
                      ? "border-b-2 border-b-restro-green bg-restro-green/10 rounded-t-lg text-restro-green"
                      : "text-gray-500 hover:bg-gray-100"
                  }`}
                  onClick={() => {
                    setSelectedRecipeData({ ingredient: null, selectedBase: null });
                    quantityRef.current.value = "";
                    setActiveAddRecipeItemTab(tab);
                  }}
                >
                  {tabName}
                </a>
              );
            })}
          </div>

          {/* Note */}
          {activeAddRecipeItemTab !== "item" && (
            <p className="text-xs text-gray-500 mb-4">
              <strong>Not:</strong> Sadece temel ürün tarifinde{" "}
              <em>bulunmayan</em> ekstra malzemeleri ekleyin.
            </p>
          )}

          {/* Base Selector (for variant/addon only) */}
          {(activeAddRecipeItemTab === "variant" || activeAddRecipeItemTab === "addon") && (
            <div className="mb-4">
              <label
                htmlFor="baseSelect"
                className="mb-1 block text-gray-500 text-sm"
              >
                {activeAddRecipeItemTab === "variant"
                  ? "Varyasyon Seçin"
                  : "Eklenti Seçin"}
              </label>
              <AsyncSelect
                key={`base-${activeAddRecipeItemTab}`}
                defaultOptions={getBaseIdOptions()}
                loadOptions={(inputValue, callback) =>
                  callback(
                    getBaseIdOptions().filter((opt) =>
                      opt.label.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  )
                }
                isClearable
                placeholder="Aramak için yazın..."
                onChange={(v) => {
                  setSelectedRecipeData((prev) => ({
                    ...prev,
                    selectedBase: v ? { id: v.value, label: v.label } : null, // Store full object
                  }));
                }}
                value={selectedRecipeData.selectedBase}
                noOptionsMessage={() => "Sonuç bulunamadı"}
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: "#f3f4f6",
                    borderRadius: "0.5rem",
                    // borderColor: "#d1d5db",
                    borderColor: "transparent",
                    height: 40,
                    // padding: "0.25rem",
                    boxShadow: "none",
                    "&:hover": {
                      borderColor: "#9ca3af",
                    },
                  }),
                  menu: (base) => ({
                    ...base,
                    borderRadius: "0.5rem",
                    // overflow: "hidden",
                  }),
                  menuList: (base) => ({
                    ...base,
                    maxHeight: 200,
                    overflowY: "auto",
                    borderRadius: "0.5rem",

                  }),
                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isFocused ? "#e5e7eb" : "white",
                    color: "#111827",
                    "&:active": {
                      backgroundColor: "#d1d5db",
                    },
                  }),
                }}
              />
            </div>
          )}

          {/* Inventory Selector */}
          <div className="mb-4 overflow-visible">
            <label
              htmlFor="inventorySelect"
              className="mb-1 block text-gray-500 text-sm"
            >
              Envanter Malzemesi Seçin
            </label>
            <AsyncSelect
              key={`inventory-${activeAddRecipeItemTab}`}
              defaultOptions={getIngredientsOptions()}
              loadOptions={(inputValue, callback) =>
                callback(
                  getIngredientsOptions().filter((opt) =>
                    opt.label.toLowerCase().includes(inputValue.toLowerCase())
                  )
                )
              }
              isClearable
              placeholder="Aramak için yazın..."
              onChange={(v) =>
                setSelectedRecipeData((prev) => ({ ...prev, ingredient: v }))
              }
              value={selectedRecipeData?.ingredient}
              noOptionsMessage={() => "Sonuç bulunamadı"}
              className="overflow-visible"
              styles={{
                control: (base) => ({
                  ...base,
                  backgroundColor: "#f3f4f6",
                  borderRadius: "0.5rem",
                  // borderColor: "#d1d5db",
                  borderColor: "transparent",
                  height: 40,
                  // padding: "0.25rem",
                  boxShadow: "none",
                  "&:hover": {
                    borderColor: "#9ca3af",
                  },
                }),
                menu: (base) => ({
                  ...base,
                  borderRadius: "0.5rem",
                  // overflow: "hidden",
                }),
                menuList: (base) => ({
                  ...base,
                  maxHeight: 150,
                  overflowY: "auto",
                  borderRadius: "0.5rem",

                }),
                option: (base, state) => ({
                  ...base,
                  backgroundColor: state.isFocused ? "#e5e7eb" : "white",
                  color: "#111827",
                  "&:active": {
                    backgroundColor: "#d1d5db",
                  },
                }),
              }}
            />
          </div>

          {/* Quantity */}
          <div className="mb-4">
           <label
              htmlFor="recipe_item_qty"
              className="mb-1 block text-gray-500 text-sm"
            >
              Miktar
              {selectedRecipeData?.ingredient?.unit != null && (
                <span> ({selectedRecipeData.ingredient.unit} cinsinden)</span>
              )}
            </label>
            <input
              type="number"
              ref={quantityRef}
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Miktar giriniz"
              step="any"
              min="0"
            />
          </div>

          {/* Actions */}
          <div className="modal-action">
            <form method="dialog">
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                type="button"
                onClick={btnAddRecipeItem}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* recipe add dialog */}

      {/* recipe edit dialog */}
      <dialog id="modal-edit-recipe-item" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Tarif Malzemesi Düzenle</h3>

          {/* Base Type Tabs */}
          <div role="tablist" className="tabs my-4">
            {["item", "variant", "addon"].map((tab) => {
              let tabName = "";
              if (tab === "item") tabName = "Ürün";
              else if (tab === "variant") tabName = "Varyasyon";
              else if (tab === "addon") tabName = "Eklenti";

              return (
                <a
                  key={tab}
                  role="tab"
                  className={`tab ${
                    editRecipeData.baseType === tab
                      ? "border-b-2 border-b-restro-green bg-restro-green/10 rounded-t-lg text-restro-green"
                      : "text-gray-500 hover:bg-gray-100"
                  }`}
                  onClick={() =>
                    setEditRecipeData((prev) => ({
                      ...prev,
                      baseType: tab,
                      selectedBase: null,
                      quantity: "",
                      ingredient: null,
                    }))
                  }
                >
                  {tabName}
                </a>
              );
            })}
          </div>

          {/* Note */}
          {editRecipeData.baseType !== "item" && (
            <p className="text-xs text-gray-500 mb-4">
              <strong>Not:</strong> Sadece temel ürün tarifinde{" "}
              <em>bulunmayan</em> ekstra malzemeleri ekleyin.
            </p>
          )}

          {/* Variant / Addon Base Selector */}
          {(editRecipeData.baseType === "variant" || editRecipeData.baseType === "addon") && (
            <div className="mb-4">
              <label className="mb-1 block text-gray-500 text-sm">
                {editRecipeData.baseType === "variant" ? "Varyasyon Seçin" : "Eklenti Seçin"}
              </label>
              <AsyncSelect
                defaultOptions={getBaseIdOptions()}
                loadOptions={(inputValue, callback) =>
                  callback(
                    getBaseIdOptions().filter((opt) =>
                      opt.label.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  )
                }

                isClearable
                placeholder="Aramak için yazın..."
                onChange={(v) => {
                  setEditRecipeData((prev) => ({
                    ...prev,
                    selectedBase: v ? { id: v.value, label: v.label } : null,
                  }));
                }}
                value={editRecipeData.selectedBase}
                noOptionsMessage={() => "Sonuç bulunamadı"}
                styles={{
                  control: (base) => ({
                    ...base,
                    backgroundColor: "#f3f4f6",
                    borderRadius: "0.5rem",
                    // borderColor: "#d1d5db",
                    borderColor: "transparent",
                    height: 40,
                    // padding: "0.25rem",
                    boxShadow: "none",
                    "&:hover": {
                      borderColor: "#9ca3af",
                    },
                  }),
                  menu: (base) => ({
                    ...base,
                    borderRadius: "0.5rem",
                    // overflow: "hidden",
                  }),
                  menuList: (base) => ({
                    ...base,
                    maxHeight: 200,
                    overflowY: "auto",
                    borderRadius: "0.5rem",

                  }),
                  option: (base, state) => ({
                    ...base,
                    backgroundColor: state.isFocused ? "#e5e7eb" : "white",
                    color: "#111827",
                    "&:active": {
                      backgroundColor: "#d1d5db",
                    },
                  }),
                }}
              />
            </div>
          )}

          {/* Ingredient Selector */}
          <div className="mb-4">
            <label className="mb-1 block text-gray-500 text-sm">Select Inventory Item</label>
            <AsyncSelect
              defaultOptions={getIngredientsOptions()}
              loadOptions={(inputValue, callback) =>
                callback(
                  getIngredientsOptions().filter((opt) =>
                    opt.label.toLowerCase().includes(inputValue.toLowerCase())
                  )
                )
              }
              isClearable
              placeholder="Type to search..."
              onChange={(v) =>
                setEditRecipeData((prev) => ({ ...prev, ingredient: v }))
              }
              value={editRecipeData.ingredient}
              noOptionsMessage={() => "No results found"}
              styles={{
                control: (base) => ({
                  ...base,
                  backgroundColor: "#f3f4f6",
                  borderRadius: "0.5rem",
                  // borderColor: "#d1d5db",
                  borderColor: "transparent",
                  height: 40,
                  // padding: "0.25rem",
                  boxShadow: "none",
                  "&:hover": {
                    borderColor: "#9ca3af",
                  },
                }),
                menu: (base) => ({
                  ...base,
                  borderRadius: "0.5rem",
                  overflow: "hidden",
                }),
                menuList: (base) => ({
                  ...base,
                  maxHeight: 200,
                  overflowY: "auto",
                  borderRadius: "0.5rem",

                }),
                option: (base, state) => ({
                  ...base,
                  backgroundColor: state.isFocused ? "#e5e7eb" : "white",
                  color: "#111827",
                  "&:active": {
                    backgroundColor: "#d1d5db",
                  },
                }),
              }}
            />
          </div>

          {/* Quantity Field */}
          <div className="mb-4">
            <label className="mb-1 block text-gray-500 text-sm">
              Qty.
              {editRecipeData?.ingredient?.unit != null && (
                <span> (in {editRecipeData.ingredient.unit})</span>
              )}
            </label>
            <input
              type="number"
              ref={quantityEditRef}
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Enter Qty."
              step="any"
              min="0"
              defaultValue={editRecipeData.quantity}
            />
          </div>

          {/* Actions */}
          <div className="modal-action">
            <form method="dialog">
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                type="button"
                onClick={btnUpdateRecipeItem}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
              >
                Güncelle
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* recipe edit dialog */}
    </Page>
  );
}
