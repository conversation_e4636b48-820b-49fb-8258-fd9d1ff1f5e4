import React, { useEffect, useRef, useState } from "react";
import Page from "../components/Page";
import { IconAlertTriangle, IconBox, IconCarrot, IconCategory2,IconCircleMinus, IconDotsVertical, IconLogs, IconPencil, IconPlus, IconSearch, IconTrash, IconX, IconMapPin, IconHome2 } from "@tabler/icons-react";
import { Link } from "react-router-dom";
import { iconStroke } from "../config/config";
import { addInventoryItem, addInventoryItemStockMovement, deleteInventoryItem, updateInventoryItem, useInventoryItems } from "../controllers/inventory.controller";
import { useStorageLocationSettings } from "../controllers/storage-locations.controller";
import toast from "react-hot-toast";
import moment from "moment";
import { mutate } from "swr";
import { clsx } from "clsx";

export const UNIT_LABELS = {
  kg: "Kilogram",
  g: "Gram",
  l: "Litre",
  ml: "Mililitre",
  cl: "Santilitre",
  pc: "Adet",
};

export default function InventoryPage() {
  const [selectedStatus, setSelectedStatus] = useState("all");

  const [state, setState] = useState({
    search: '',
    inventory: [],
    statusCounts: {
      all: 0,
      in: 0,
      low: 0,
      out: 0,
    },
  })

  const titleRef = useRef(null);
  const quantityRef = useRef(null);
  const unitRef = useRef(null);
  const minQuantityThresholdRef = useRef(null);

  const updateTitleRef = useRef(null);
  const updateQuantityRef = useRef(null);
  const updateUnitRef = useRef(null);
  const updateMinQuantityThresholdRef = useRef(null);

  const [selectedUpdateItem, setSelectedUpdateItem] = useState(null);

  const [selectedDeletItem, setSelectedDeleteItem] = useState(null);

  const addStockMovementTypeRef = useRef("IN");
  const addStockMovementItemIdRef = useRef(null);
  const addStockMovementQuantityRef = useRef(null);
  const addStockMovementNoteRef = useRef(null);

  const { APIURL, data, error, isLoading } = useInventoryItems({
    status: selectedStatus
  });

  const { data: storageSettings } = useStorageLocationSettings();

  useEffect(() => {
    if (data) {
      setState({
        ...state,
        inventory: data.items || [],
        statusCounts: data.statusCounts || {},
      });
    }
  }, [data])

  if (isLoading) {
    return <Page>Yükleniyor...</Page>;
  }

  if (error) {
    return <Page>Veri yüklenirken bir hata oluştu!</Page>;
  }

  const handleAddInventoryItem = async () => {
    const title = titleRef.current?.value.trim();
    const quantity = parseFloat(quantityRef.current?.value || 0);
    const unit = unitRef.current?.value;
    const minQuantityThreshold = parseFloat(minQuantityThresholdRef.current?.value || 0);

    if (!title) {
      toast.error("Ürün adı gereklidir");
      return;
    }
    if (!quantity || quantity <= 0) {
      toast.error("Miktar pozitif bir sayı olmalıdır");
      return;
    }
    if (!unit) {
      toast.error("Birim seçilmelidir");
      return;
    }
    if (!minQuantityThreshold || minQuantityThreshold <= 0) {
      toast.error("Minimum miktar pozitif bir sayı olmalıdır");
      return;
    }
    if (minQuantityThreshold > quantity) {
      toast.error("Minimum miktar, mevcut stoktan küçük olmalıdır");
      return;
    }

    try {
      const res = await addInventoryItem({
        title,
        quantity,
        unit,
        min_quantity_threshold: minQuantityThreshold,
      });

      if (res.status === 200) {
        toast.dismiss();
        toast.success(res.data.message);

        titleRef.current.value = "";
        quantityRef.current.value = "";
        unitRef.current.value = "pc";
        minQuantityThresholdRef.current.value = "";

        document.getElementById('modal-add-inventory-item').close();

        await mutate(APIURL);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir hata oluştu";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const handleUpdateInventoryItem = async () => {
    const title = updateTitleRef.current?.value.trim();
    const quantity = parseFloat(updateQuantityRef.current?.value || 0);
    const unit = updateUnitRef.current?.value;
    const minQuantityThreshold = parseFloat(updateMinQuantityThresholdRef.current?.value || 0);

    if(!selectedUpdateItem){
      toast.error("Geçersiz istek");
      return;
    }
    if (!title) {
      toast.error("Ürün adı gereklidir");
      return;
    }
    if (!quantity || quantity <= 0) {
      toast.error("Miktar pozitif bir sayı olmalıdır");
      return;
    }
    if (!unit) {
      toast.error("Birim seçilmelidir");
      return;
    }
    if (!minQuantityThreshold || minQuantityThreshold <= 0) {
      toast.error("Minimum miktar pozitif bir sayı olmalıdır");
      return;
    }
    if (minQuantityThreshold > quantity) {
      toast.error("Minimum miktar, mevcut stoktan küçük olmalıdır");
      return;
    }

    try {
      const res = await updateInventoryItem({
        id: selectedUpdateItem,
        title,
        unit,
        min_quantity_threshold: minQuantityThreshold,
      });

      if (res.status === 200) {
        toast.dismiss();
        toast.success(res.data.message);

        updateTitleRef.current.value = "";
        updateQuantityRef.current.value = "";
        updateUnitRef.current.value = "pc";
        updateMinQuantityThresholdRef.current.value = "";
        setSelectedUpdateItem(null);

        document.getElementById('modal-update-inventory-item').close();

        await mutate(APIURL);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir hata oluştu";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const handleDeleteInventoryItem = async () => {
    if(!selectedDeletItem){
      toast.error("Geçersiz istek");
      return;
    }

    try {
      const res = await deleteInventoryItem({
        id: selectedDeletItem
      });

      if (res.status === 200) {
        toast.dismiss();
        toast.success(res.data.message);

        setSelectedDeleteItem(null)

        document.getElementById('modal-delete-inventory-item').close();

        await mutate(APIURL);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir hata oluştu";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const handleAddItemStockMovement = async () => {
    const movementType = addStockMovementTypeRef.current?.value || null;
    const itemId = addStockMovementItemIdRef.current?.value || null;
    const quantity = parseFloat(addStockMovementQuantityRef.current?.value || "0");
    const note = addStockMovementNoteRef.current?.value?.trim();

    if(!itemId){
      toast.error("Geçersiz istek");
      return;
    }

    if(!movementType){
      toast.error("Hareket tipi seçilmelidir");
      return;
    }

    if (isNaN(quantity) || quantity <= 0) {
      toast.error("Geçersiz miktar");
      return;
    }

    try {
      const res = await addInventoryItemStockMovement({
        id: itemId,
        movementType,
        quantity,
        note
      });

      if (res.status === 200) {
        toast.dismiss();
        toast.success(res.data.message);

        addStockMovementTypeRef.current.value = 'IN'
        addStockMovementItemIdRef?.current.value == null;
        addStockMovementQuantityRef.current.value = "";
        addStockMovementNoteRef.current.value = "";

        document.getElementById("modal-add-inventory-stock-movement")?.close();

        await mutate(APIURL);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir hata oluştu";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <Page>
      <div className="flex flex-wrap gap-4 flex-col md:flex-row items-center justify-between">
        <div className="flex flex-col md:flex-row md:items-center gap-4">
          <h3 className="text-2xl">Envanter</h3>
          <Link
            to="dashboard"
            className="rounded-lg border-none bg-gray-50 hover:bg-gray-100 shadow-none transition active:scale-95 text-gray-500 px-2 py-1 flex items-center gap-1"
          >
            <IconCategory2 stroke={iconStroke} size={18} /> Panel
          </Link>
        </div>

        <div className="flex flex-col md:flex-row md:items-center gap-2">
          <div className="flex items-center justify-between">
            <div className="relative md:w-80">
              <IconSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="search"
                className="input input-sm input-bordered focus:outline-none focus:ring-1 focus-within:ring-gray-200 transition pl-8 pr-2 w-full rounded-lg text-gray-500 py-4 h-8"
                placeholder="Ara..."
                value={state.search}
                onChange={(e) => {
                  setState({
                    ...state,
                    search: e.target.value,
                  });
                }}
              />
            </div>
            <div></div>
          </div>
          <button onClick={()=>{
              document.getElementById('modal-add-inventory-item').showModal();
          }} className="rounded-lg border-none bg-gray-50 hover:bg-gray-100 shadow-none transition active:scale-95 text-gray-500 px-2 py-1 flex items-center gap-1">
            <IconPlus size={18} stroke={iconStroke}/>
            Stok Ekle
          </button>
          <Link
            to="stock-movements"
            className="rounded-lg border-none bg-gray-50 hover:bg-gray-100 shadow-none transition active:scale-95 text-gray-500 px-2 py-1 flex items-center gap-1"
          >
            <IconLogs stroke={iconStroke} size={18} /> Stok Hareketleri
          </Link>

          {/* Depo Lokasyonları Butonu - Sadece modül aktifse göster */}
          {storageSettings?.is_enabled && (
            <Link
              to="/dashboard/storage-locations"
              className="rounded-lg border-none bg-purple-50 hover:bg-purple-100 shadow-none transition active:scale-95 text-purple-600 px-2 py-1 flex items-center gap-1"
            >
              <IconHome2 stroke={iconStroke} size={18} /> Depo/Bar Lokasyonları
            </Link>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
        {[
          {
            label: "Tümü",
            icon: <IconBox stroke={1.5} className="text-gray-600" />,
            status: "all",
            count: state.inventory?.length || 0,
            border: "border-l-gray-400",
            countColor: "text-gray-600",
          },
          {
            label: "Stokta",
            icon: <IconCarrot stroke={1.5} className="text-green-600" />,
            status: "in",
            count: state.statusCounts?.in,
            border: "border-l-restro-green",
            countColor: "text-restro-green",
          },
          {
            label: "Stok Az",
            icon: <IconAlertTriangle stroke={1.5} className="text-yellow-600" />,
            status: "low",
            count: state.statusCounts?.low,
            border: "border-l-yellow-600",
            countColor: "text-yellow-600",
          },
          {
            label: "Stokta Yok",
            icon: <IconCircleMinus stroke={1.5} className="text-red-600"/>,
            status: "out",
            count: state.statusCounts?.out,
            border: "border-l-red-600",
            countColor: "text-red-600",
          },
        ].map(({ label, icon, status, count, border, countColor }) => {
          const isSelected = selectedStatus === status;
          return (
            <div
              key={status}
              onClick={() => setSelectedStatus(status)}
              className={clsx(
                "cursor-pointer flex items-center gap-4 px-4 py-4 rounded-xl border border-l-8 transition-all duration-200",
                border,
                isSelected ? "bg-gray-50" : ""
              )}
            >
              <div className="p-3 rounded-lg bg-gray-100 shadow-inner">{icon}</div>
              <div>
                <div className="text-sm font-medium text-gray-600">{label}</div>
                <div className={`text-lg font-bold ${countColor}`}>{count}</div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="h-[calc(100vh-260px)] overflow-auto w-[100%] mt-4">
       {(state.inventory || [])
        .filter((item) => {
          if (!state.search) return true;
          return item.title?.toLowerCase().includes(state.search.trim().toLowerCase());
        }).length === 0 ? (
          <div className="flex flex-col justify-center items-center text-center h-full">
            <img
              src="/assets/illustrations/orders-not-found.webp"
              alt="Envanter öğesi bulunamadı"
              className="w-1/2 md:w-60"
            />
            <p className="text-md text-gray-600">Envanter öğesi bulunamadı</p>
          </div>
        ) :
        (<table className="table table-sm">
          <thead className="bg-restro-green-light text-gray-500 sticky top-0 z-10">
            <tr>
              <th className="text-start p-2.5">#</th>
              <th className="text-start">Ürün</th>
              <th className="text-start">Mevcut Stok</th>
              <th className="text-start">Minimum Stok</th>
              <th className="text-start">Stok Durumu</th>
              <th className="text-start max-w-20">Güncelleme Tarihi</th>
              <th className="text-start">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {
              (state.inventory || [])
              .filter((item) => {
                if (!state.search) return true;
                return item.title?.toLowerCase().includes(state.search.trim().toLowerCase());
              })
              .map((inventoryItem, i) => {
                const { id, title, quantity, unit, min_quantity_threshold, status, updated_at } = inventoryItem;
                return (
                  <tr key={id}>
                    <td>{state.inventory?.length - i}</td>
                    <td>
                      <p className="text-ellipsis line-clamp-2">{title}</p>
                    </td>
                    <td>
                      <p>
                        {quantity ? quantity : "-"} {unit}
                      </p>
                    </td>
                    <td>
                      <p>
                        {min_quantity_threshold ? min_quantity_threshold : "-"} {unit}
                      </p>
                    </td>
                    <td>
                      <span
                        className={`text-xs rounded-lg py-1 px-2 font-semibold
                          ${status === 'in' ? 'bg-green-50 text-green-600' : ''}
                          ${status === 'low' ? 'bg-yellow-50 text-yellow-600' : ''}
                          ${status === 'out' ? 'bg-red-50 text-red-600' : ''}
                        `}
                      >
                        {status === 'in' ? 'Stokta' :
                        status === 'low' ? 'Stok Az' :
                        status === 'out' ? 'Stokta Yok' : '-'}
                      </span>
                    </td>
                    <td>
                      {updated_at
                        ? moment.utc(updated_at).local().format("DD/MM/YYYY, h:mm A")
                        : "-"}
                    </td>
                    <td>
                      <div className="dropdown dropdown-end">
                        <div tabIndex={0} role="button" className="m-1">
                          <IconDotsVertical size={18} stroke={iconStroke} />
                        </div>
                        <ul tabIndex={0} className="dropdown-content menu menu-sm bg-base-100 rounded-box border w-56 z-[1] p-2 text-gray-600">
                          <li>
                            <button
                              onClick={()=>{
                                requestAnimationFrame(() => {
                                  if (addStockMovementItemIdRef?.current) {
                                    addStockMovementItemIdRef.current.value = inventoryItem.id;
                                  }
                                });
                                document.getElementById('modal-add-inventory-stock-movement').showModal();
                              }}
                            ><IconPlus size={18} stroke={iconStroke} /> Stok Hareketi Ekle</button>
                          </li>
                          <li>
                            <button
                             onClick={() => {
                              requestAnimationFrame(() => {
                                setSelectedUpdateItem(inventoryItem.id);
                                updateTitleRef.current.value = inventoryItem.title;
                                updateQuantityRef.current.value = inventoryItem.quantity;
                                updateUnitRef.current.value = inventoryItem.unit;
                                updateMinQuantityThresholdRef.current.value = inventoryItem.min_quantity_threshold;
                              });
                              document.getElementById('modal-update-inventory-item').showModal();
                            }}

                            ><IconPencil size={18}  stroke={iconStroke} /> Ürünü Güncelle</button>
                          </li>
                          <li>
                            <button
                              onClick={()=>{
                                setSelectedDeleteItem(inventoryItem?.id || null)
                                document.getElementById('modal-delete-inventory-item').showModal();
                              }}
                              className="text-red-500"
                            ><IconTrash size={18} stroke={iconStroke} /> Ürünü Sil</button>
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                );
              })
            }
          </tbody>
        </table>)}
      </div>

      <dialog id="modal-add-inventory-item" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <div className='flex justify-between items-center'>
            <h3 className="font-bold text-lg">Envanter Öğesi Ekle</h3>
            <button className='bg-red-50 text-red-600 p-2 rounded-full' onClick={() => document.getElementById('modal-add-inventory-item').close()}><IconX size={18} stroke={iconStroke}/></button>
          </div>

          {/* Title */}
          <div className="mt-4">
            <label htmlFor="item_title" className="mb-1 block text-gray-500 text-sm">Ürün Adı</label>
            <input ref={titleRef} type="text" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Ürün adını girin" />
          </div>

          <div className="mt-4 flex items-end gap-4">
            <div className="flex-1">
              <label htmlFor="item_unit" className="mb-1 block text-gray-500 text-sm">Birim</label>
              <select ref={unitRef} className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light">
                <option value="pc">Adet</option>
                <option value="kg">Kilogram</option>
                <option value="g">Gram</option>
                <option value="l">Litre</option>
                <option value="ml">Mililitre</option>
                <option value="cl">Santilitre</option>
              </select>
            </div>
          </div>

          {/* Current Qty and Min Qty */}
          <div className="mt-4 flex items-center gap-4">
            <div className="flex-1">
              <label htmlFor="item_qty" className="mb-1 block text-gray-500 text-sm">Mevcut Miktar</label>
              <input ref={quantityRef} type="number" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Mevcut miktarı girin" />
            </div>
            <div className="flex-1">
              <label htmlFor="low_stock_qty" className="mb-1 block text-gray-500 text-sm">Minimum Miktar</label>
              <input ref={minQuantityThresholdRef} type="number" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Minimum miktarı girin" />
            </div>
          </div>

          {/* Actions */}
          <div className="modal-action w-full">
            <form method="dialog" className="w-full">
              <button
                type="button"
                onClick={handleAddInventoryItem}
                className="w-full rounded-lg hover:bg-restro-green-dark transition active:scale-95 hover:shadow-lg px-4 py-2 bg-restro-green text-white">Kaydet</button>
            </form>
          </div>
        </div>
      </dialog>

      {/* dialog: update item */}
      <dialog id="modal-update-inventory-item" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <div className='flex justify-between items-center'>
            <h3 className="font-bold text-lg">Envanter Öğesini Güncelle</h3>
            <button className='bg-red-50 text-red-600 p-2 rounded-full' onClick={() => document.getElementById('modal-update-inventory-item').close()}><IconX size={18} stroke={iconStroke}/></button>
          </div>

          {/* Title */}
          <div className="mt-4">
            <label htmlFor="item_title" className="mb-1 block text-gray-500 text-sm">Ürün Adı</label>
            <input ref={updateTitleRef} type="text" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Ürün adını girin" />
          </div>

          <div className="mt-4 flex items-end gap-4">
            <div className="flex-1">
              <label htmlFor="item_unit" className="mb-1 block text-gray-500 text-sm">Birim</label>
              <select ref={updateUnitRef} disabled className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light disabled:cursor-not-allowed">
                <option value="pc">Adet</option>
                <option value="kg">Kilogram</option>
                <option value="g">Gram</option>
                <option value="l">Litre</option>
                <option value="ml">Mililitre</option>
                <option value="cl">Santilitre</option>
              </select>
            </div>
          </div>

          {/* Current Qty and Min Qty */}
          <div className="mt-4 flex items-center gap-4">
            <div className="flex-1">
              <label htmlFor="item_qty" className="mb-1 block text-gray-500 text-sm">Mevcut Miktar</label>
              <input ref={updateQuantityRef} type="number" disabled className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light disabled:cursor-not-allowed" placeholder="Mevcut miktarı girin" />
            </div>
            <div className="flex-1">
              <label htmlFor="low_stock_qty" className="mb-1 block text-gray-500 text-sm">Minimum Miktar</label>
              <input ref={updateMinQuantityThresholdRef} type="number" className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light" placeholder="Minimum miktarı girin" />
            </div>
          </div>

          {/* Actions */}
          <div className="modal-action w-full">
            <form method="dialog" className="w-full">
              <button
                type="button"
                onClick={() => {
                  handleUpdateInventoryItem()
                }}
                className="rounded-lg hover:bg-restro-green-dark transition active:scale-95 hover:shadow-lg px-4 py-2 bg-restro-green text-white w-full">Kaydet</button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: update item */}

      {/* dialog: add stock */}
      <dialog id="modal-add-inventory-stock-movement" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <div className='flex justify-between items-center'>
            <h3 className="font-bold text-lg">Stok Hareketi Ekle</h3>
            <button className='bg-red-50 text-red-600 p-2 rounded-full' onClick={() => document.getElementById('modal-add-inventory-stock-movement').close()}><IconX size={18} stroke={iconStroke}/></button>
          </div>

          <div className="mt-4">
            <label htmlFor="movement_type" className="mb-1 block text-gray-500 text-sm">
              Hareket Tipi
            </label>
            <select
              ref={addStockMovementTypeRef}
              name="movement_type"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              defaultValue="IN"
            >
              <option value="IN">Giriş</option>
              <option value="OUT">Çıkış</option>
              <option value="WASTAGE">Fire</option>
            </select>
          </div>

          <div className="mt-4">
            <input type="hidden" ref={addStockMovementItemIdRef} />
            <label htmlFor="add_quantity" className="mb-1 block text-gray-500 text-sm">Miktar</label>
            <input
              ref={addStockMovementQuantityRef}
              type="number"
              name="add_quantity"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Miktar girin"
            />
          </div>

          <div className="mt-4">
            <label htmlFor="add_notes" className="mb-1 block text-gray-500 text-sm">Notlar</label>
            <textarea
              ref={addStockMovementNoteRef}
              name="add_notes"
              rows={4}
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light resize-none"
              placeholder="Not ekleyin (opsiyonel)"
            />
          </div>

          <div className="modal-action w-full">
            <form method="dialog" className="w-full">
              <button
                type="button"
                onClick={handleAddItemStockMovement}
                className="rounded-lg hover:bg-restro-green-dark transition active:scale-95 hover:shadow-lg px-4 py-2 bg-restro-green text-white w-full"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: add stock */}

      {/* dialog: delete inventory item */}
      <dialog id="modal-delete-inventory-item" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Envanter Öğesini Sil</h3>
          <p className="py-4">
            Bu envanter öğesini silmek istediğinizden emin misiniz?
          </p>
          <div className="modal-action">
            <form method="dialog">
              <button className="btn">İptal</button>
              <button
                onClick={() => {
                  handleDeleteInventoryItem();
                }}
                className="ml-2 btn hover:bg-red-700 bg-red-500 text-white"
              >
                Sil
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* dialog: delete inventory item */}


    </Page>
  );
}