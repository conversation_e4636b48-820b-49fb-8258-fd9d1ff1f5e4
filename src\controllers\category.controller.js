import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

// Kategoriler (Categories)
export function useCategories() {
  const APIURL = `/categories`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export function useCategory(id) {
  const APIURL = `/categories/${id}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export async function addCategory(title, parentId, sortOrder) {
  try {
    const response = await ApiClient.post("/categories/add", {
      title,
      parentId,
      sortOrder: sortOrder || 0
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateCategory(id, title, parentId, sortOrder) {
  try {
    const response = await ApiClient.post(`/categories/update/${id}`, {
      title,
      parentId,
      sortOrder
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteCategory(id) {
  try {
    const response = await ApiClient.delete(`/categories/delete/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateCategoryOrder(order) {
  try {
    const response = await ApiClient.post(`/categories/update-order`, {
      order
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export default {
  useCategories,
  useCategory,
  addCategory,
  updateCategory,
  deleteCategory,
  updateCategoryOrder
};