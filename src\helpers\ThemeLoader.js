/**
 * QR Menü Tema Yükleyici
 * Backend'den gelen template_id'ye göre doğru tema component'lerini yükler
 */

import { lazy } from 'react';

// Tema mapping'i - Backend'den gelen template_id'ye göre klasör adı
const THEME_MAPPING = {
  1: 'classic',    // Klasik tema (varsayılan)
  2: 'modern',     // Modern tema
  3: 'minimal',    // Minimal tema
  4: 'luxury'      // Lüks tema
};

// Varsayılan tema
const DEFAULT_THEME = 'classic';

/**
 * Template ID'ye göre tema adını döndürür
 */
export const getThemeName = (templateId) => {
  return THEME_MAPPING[templateId] || DEFAULT_THEME;
};

/**
 * Tema component'lerini lazy load eder
 * Vite için static import paths kullanır
 */
export const loadThemeComponents = (templateId) => {
  const themeName = getThemeName(templateId);

  // Vite için static import paths - her tema için ayrı ayrı tanımla
  const themeComponents = {
    classic: {
      MenuPage: lazy(() => import('../themes/classic/MenuPage.jsx')),
      QRMenuPage: lazy(() => import('../themes/classic/QRMenuPage.jsx')),
      CategoryPage: lazy(() => import('../themes/classic/CategoryPage.jsx'))
    },
    modern: {
      MenuPage: lazy(() => import('../themes/modern/MenuPage.jsx')),
      QRMenuPage: lazy(() => import('../themes/modern/QRMenuPage.jsx')),
      CategoryPage: lazy(() => import('../themes/modern/CategoryPage.jsx'))
    },
    minimal: {
      MenuPage: lazy(() => import('../themes/minimal/MenuPage.jsx')),
      QRMenuPage: lazy(() => import('../themes/minimal/QRMenuPage.jsx')),
      CategoryPage: lazy(() => import('../themes/minimal/CategoryPage.jsx'))
    },
    luxury: {
      MenuPage: lazy(() => import('../themes/luxury/MenuPage.jsx')),
      QRMenuPage: lazy(() => import('../themes/luxury/QRMenuPage.jsx')),
      CategoryPage: lazy(() => import('../themes/luxury/CategoryPage.jsx'))
    }
  };

  // İlgili tema component'lerini döndür, yoksa classic kullan
  return themeComponents[themeName] || themeComponents[DEFAULT_THEME];
};

/**
 * CSS artık kullanılmıyor - her tema kendi Tailwind sınıflarını kullanıyor
 */
export const loadThemeStyles = () => {
  // Artık CSS yükleme yok, her tema kendi Tailwind sınıflarını kullanıyor
};

export const clearThemeStyles = () => {
  // Artık CSS temizleme yok
};

/**
 * Mevcut temaları listeler
 */
export const getAvailableThemes = () => {
  return Object.entries(THEME_MAPPING).map(([id, name]) => ({
    id: parseInt(id),
    name,
    displayName: getThemeDisplayName(name)
  }));
};

/**
 * Tema görünen adını döndürür
 */
const getThemeDisplayName = (themeName) => {
  const displayNames = {
    classic: 'Klasik',
    modern: 'Modern',
    minimal: 'Minimal',
    luxury: 'Lüks'
  };
  
  return displayNames[themeName] || themeName;
};
