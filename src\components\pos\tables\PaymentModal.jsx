import React, { useState, useEffect } from 'react';
import { IconX, IconTrash, IconArrowBack } from "@tabler/icons-react";
import { toast } from "react-hot-toast";
import { iconStroke } from "../../../config/config";
import { PAYMENT_ICONS } from "../../../config/payment_icons";
import { PaymentSummary } from './PaymentSummary';
import { savePartialPayment, applyDiscount } from "../../../controllers/orders.controller";
import DiscountModal from './DiscountModal';

// Ana ödeme modal bileşeni
export const PaymentModal = ({
  table,                    // Masa bilgisi
  paymentState,            // Ödeme durumu
  currency,
  paymentTypes,            // Ödeme yöntemleri
  onPaymentComplete,       // Ödeme tamamlandığında çalışacak fonksiyon
  onPaymentTypeSelect,     // Ödeme yöntemi seçildiğinde çalışacak fonksiyon
  onOrderUpdate,
  paymentList, // props olarak alınıyor
  setPaymentList, // props olarak alınıyor

}) => {
  // State tanımlamaları
  const [inputValue, setInputValue] = useState('0.00');              // Girilen ödeme tutarı
  const [selectedProducts, setSelectedProducts] = useState([]);      // Seçili ürünler
  const [isSaving, setIsSaving] = useState(false);                   // Ödeme kaydediliyor mu?


  // Yeni state'ler
  const [showDiscountPanel, setShowDiscountPanel] = useState(false);
  const [discountType, setDiscountType] = useState('amount'); // 'amount' veya 'percentage'
  const [discountTarget, setDiscountTarget] = useState('order'); // 'order' veya 'item'
  const [discountValue, setDiscountValue] = useState('0');

  // Kalan ürünlerin durumunu tutan state
  const [remainingProducts, setRemainingProducts] = useState(() =>
    paymentState.summaryOrders?.[0]?.items || []
  );

  useEffect(() => {
    const handleShow = () => {
      setPaymentList([]);
    };

    const handleClose = () => {
      if (paymentList.length > 0) {

        const savedPayments = paymentState.summaryOrders?.[0]?.partialPayments || [];
        const savedPaymentIds = savedPayments.map(p => p.id);

        // Eğer yeni eklenen ödemeler kaydedilmemişse, temizle
        if (!savedPaymentIds.some(id => paymentList.some(p => p.id === id))) {
          setPaymentList([]);
        }
      }
    };

    // Modal elementini bul
    const modal = document.getElementById("modal-order-summary-complete-advanced");

    // Event listener'ları ekle
    if (modal) {
      modal.addEventListener('close', handleClose);
      modal.addEventListener('showModal', handleShow);
    }

    // Cleanup fonksiyonu
    return () => {
      if (modal) {
        modal.removeEventListener('close', handleClose);
        modal.removeEventListener('showModal', handleShow);
      }
    };
  }, [paymentList, paymentState.summaryOrders, setPaymentList]);

  // Siparişler değiştiğinde ürünleri güncelle
  useEffect(() => {
    const products = paymentState.summaryOrders?.[0]?.items || [];
    setRemainingProducts(products);
  }, [paymentState.summaryOrders]);


  // Kalan tutarı hesaplama fonksiyonu
  const calculateRemainingAmount = () => {
    // API'den gelen toplam tutarı kullan (indirimler zaten düşülmüş)
    const total = paymentState.summaryTotal || 0;

    // API'den gelen ödenen tutarı kullan
    const totalPaid = paymentState.summaryOrders?.[0]?.totalPaid || 0;

    // Yeni eklenen ödemeleri topla
    const newPaymentsTotal = paymentList.reduce(
        (sum, payment) => sum + parseFloat(payment.amount),
        0
    );

    // Toplam ödeme (mevcut + yeni)
    const totalPayments = totalPaid + newPaymentsTotal;

    // Kalan tutar = Toplam - Ödemeler
    const remaining = total - totalPayments;

    // Kalan tutar negatif olamaz
    return Math.max(0, remaining);
  };

  const handleSavePartialPayment = async () => {
    if (paymentList.length === 0) {
        toast.error('Kaydedilecek ödeme bulunamadı!');
        return;
    }

    // Eğer zaten kaydetme işlemi devam ediyorsa, işlemi engelle
    if (isSaving) {
        return;
    }

    // Kaydetme işlemi başladı
    setIsSaving(true);

    try {
        await savePartialPayment({
            payments: paymentList.map(payment => ({
                method: payment.method,
                amount: payment.amount,
                orderItem_id: payment.orderItem_id || null,
                productName: payment.productName,
                order_id: payment.orderId
            }))
        });

        // Ödeme kaydedildikten sonra siparişi tekrar yükle
        if (onOrderUpdate) {
            await onOrderUpdate(table.id);
        }

        toast.success('Parçalı ödeme başarıyla kaydedildi!');
        // Önce ödeme listesini temizle, sonra modalı kapat
        setPaymentList([]); // Ödeme listesini temizle

    } catch (error) {
        console.error(error);
        toast.error('Parçalı ödeme kaydedilemedi!');
    } finally {
        // Kaydetme işlemi bitti
        setIsSaving(false);
    }
};

  // Ödenmiş tutarları güncelleme fonksiyonu
  const updatePaidAmounts = () => {
    // Eğer orders yoksa boş dön
    if (!paymentState.summaryOrders?.[0]) {
        return;
    }

    const updatedProducts = paymentState.summaryOrders[0].items.map((item) => {
        // Ürün için önceden ödenmiş tutar (API'den gelen)
        let savedPayments = 0;

        // Kaydedilmiş parçalı ödemeler
        if (paymentState.summaryOrders[0].partialPayments) {
            savedPayments = paymentState.summaryOrders[0].partialPayments
                ?.filter((payment) => payment.orderItem_id === item.id)
                .reduce((sum, payment) => sum + parseFloat(payment.amount), 0) || 0;
        }

        // Yeni eklenen ödemeler
        const newPayments = paymentList
            .filter(payment => payment.orderItem_id === item.id)
            .reduce((sum, payment) => sum + parseFloat(payment.amount), 0) || 0;

        const totalPaidAmount = savedPayments + newPayments;
        const totalPrice = parseFloat(item.price) * item.quantity;

        return {
            ...item,
            paidAmount: totalPaidAmount,
            isFullyPaid: totalPaidAmount >= totalPrice
        };
    });

    setRemainingProducts(updatedProducts);
};


  // Numerik tuş takımı işleyicisi
  const handleNumericPadClick = (value) => {
    if (value === '←') {
      setInputValue((prev) => prev.slice(0, -1) || '0.00');
    } else {
      setInputValue((prev) => {
        if (prev === '0.00') return value.toString();
        return prev + value;
      });
    }
  };


  const handleProductSelection = (product) => {
    if (product.isFullyPaid) {
      toast.error("Bu ürün tamamen ödenmiş!");
      return;
    }

    // Ürün zaten seçili mi kontrol et
    const existingIndex = selectedProducts.findIndex(p => p.id === product.id);

    // Ürünün kaç kez seçildiğini hesapla
    const selectedCount = selectedProducts.filter(p => p.id === product.id).length;

    // Eğer ürün maksimum adet sayısına ulaştıysa uyarı ver
    if (selectedCount >= product.quantity) {
      toast.error(`Bu ürün için maksimum ${product.quantity} adet seçebilirsiniz!`);
      return;
    }

    if (existingIndex >= 0) {
      // Ürün zaten seçilmiş, yeni bir kopyasını ekle
      setSelectedProducts([...selectedProducts, product]);

      // Mevcut input değerine bir adet fiyatını ekle
      const currentValue = parseFloat(inputValue) || 0;
      const newValue = currentValue + parseFloat(product.price);
      setInputValue(newValue.toFixed(2));
    } else {
      // Yeni ürün seçimi
      setSelectedProducts([...selectedProducts, product]);

      // Mevcut input değerine bir adet fiyatını ekle
      const currentValue = parseFloat(inputValue) || 0;
      const newValue = currentValue + parseFloat(product.price);
      setInputValue(newValue.toFixed(2));
    }
  };


  // Ödemeler değiştiğinde ödenen tutarları güncelle
  useEffect(() => {
    if (paymentState.summaryOrders?.[0]) {
        updatePaidAmounts();
    }
}, [paymentState.summaryOrders, paymentList]);

  // Ürün ödemesi ekleme işleyicisi
  const handleAddProductPayment = () => {
    const amount = parseFloat(inputValue);
    const remainingAmount = calculateRemainingAmount();

    // Tutar kontrolü
    if (isNaN(amount) || amount <= 0) {
        toast.error("Geçersiz ödeme tutarı!");
        return;
    }

    // Kalan tutardan fazla ödeme kontrolü
    if (amount > remainingAmount) {
        toast.error(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} ${currency}) fazla olamaz!`);
        return;
    }

    // Ödeme yöntemi kontrolü
    if (!paymentState.selectedPaymentType) {
        toast.error("Lütfen bir ödeme yöntemi seçin!");
        return;
    }

    const selectedPaymentType = paymentTypes.find(
        type => type.id === paymentState.selectedPaymentType
    );

    // Seçili ürün var mı kontrol et
    if (selectedProducts.length > 0) {
      // Ürünleri ID'lerine göre grupla
      const groupedProducts = {};
      selectedProducts.forEach(product => {
        if (!groupedProducts[product.id]) {
          groupedProducts[product.id] = {
            product,
            count: 1
          };
        } else {
          groupedProducts[product.id].count += 1;
        };
      });

      // Her ürün grubu için ödeme ekle
      Object.values(groupedProducts).forEach(({ product: selectedProduct, count }) => {
        // Ürünün birim fiyatı
        const productPrice = parseFloat(selectedProduct.price);

        // Bu ürün için toplam tutar (birim fiyat * seçilen adet)
        const productTotal = productPrice * count;

        // Tüm seçili ürünlerin toplam tutarı içindeki oranı
        const ratio = productTotal / amount;

        // Ödeme tutarı (toplam ödeme * oran)
        const paymentAmount = amount * ratio;

        const newPayment = {
            method: paymentState.selectedPaymentType,
            amount: paymentAmount,
            orderItem_id: selectedProduct.id,
            orderId: paymentState.summaryOrders[0].id,
            productName: `${selectedProduct.item_title} (${count} adet)`,
            payment_type: selectedPaymentType?.title || '',
            methodName: selectedPaymentType?.title || ''
        };

        // Yeni ödemeyi ekle
        setPaymentList(prev => [...prev, newPayment]);

        // Ürüne ait tüm ödemeleri hesapla (kaydedilmiş ve yeni eklenen)
        const savedPayments = paymentState.summaryOrders?.[0]?.partialPayments
            ?.filter(payment => payment.orderItem_id === selectedProduct.id)
            .reduce((sum, payment) => sum + parseFloat(payment.amount), 0) || 0;

        const newPaymentsForItem = [...paymentList, newPayment]
            .filter(payment => payment.orderItem_id === selectedProduct.id)
            .reduce((sum, payment) => sum + parseFloat(payment.amount), 0);

        const totalPaidForItem = savedPayments + newPaymentsForItem;
        const totalPrice = parseFloat(selectedProduct.price) * selectedProduct.quantity;

        // Ürünün ödenen tutarını güncelle
        setRemainingProducts((prev) =>
            prev.map((product) =>
                product.id === selectedProduct.id
                    ? {
                        ...product,
                        paidAmount: totalPaidForItem,
                        isFullyPaid: totalPaidForItem >= totalPrice
                      }
                    : product
            )
        );
      });
    } else {
      // Genel ödeme ekle (hiçbir ürün seçili değilse)
      const newPayment = {
          method: paymentState.selectedPaymentType,
          amount: amount,
          orderItem_id: null,
          orderId: paymentState.summaryOrders[0].id,
          productName: 'Genel Ödeme',
          payment_type: selectedPaymentType?.title || '',
          methodName: selectedPaymentType?.title || ''
      };

      setPaymentList(prev => [...prev, newPayment]);
    }

    setInputValue("0.00");
    setSelectedProducts([]);
};

  const handleApplyDiscount = async () => {
    const value = parseFloat(discountValue);
    if (isNaN(value) || value <= 0) {
        toast.error("Geçerli bir indirim tutarı girin!");
        return;
    }

    try {
        toast.loading("İndirim uygulanıyor...");

        // İndirim uygula
        await applyDiscount({
            order_id: paymentState.summaryOrders[0].id,
            order_item_id: null, // Sadece tüm siparişe indirim uygula
            discount_type: 'amount', // Sadece tutar indirimi uygula
            discount_value: value
        });

        // İndirim başarılı olduktan sonra siparişi tekrar yükle
        if (onOrderUpdate) {
            // table.id yerine order_id kullan
            const updatedData = await onOrderUpdate(paymentState.summaryOrders[0].id);
        }

        toast.dismiss();
        toast.success("İndirim başarıyla uygulandı!");

        // İndirim panelini kapat ve değeri sıfırla
        setShowDiscountPanel(false);
        setDiscountValue('0');

    } catch (error) {
        toast.dismiss();
        toast.error("İndirim uygulanırken bir hata oluştu!");
        console.error("Indirim hatası:", error);
    }
  };

  // Ödeme silme işleyicisi
  const handleRemovePayment = (index) => {
    const paymentToRemove = paymentList[index];

    // Sadece seçilen ödemeyi kaldır
    const updatedPaymentList = paymentList.filter((_, i) => i !== index);
    setPaymentList(updatedPaymentList);

    // Eğer ürüne özel bir ödeme ise, ürünün ödenen tutarını güncelle
    if (paymentToRemove.orderItem_id) {
      // Ürüne ait kalan ödemeleri hesapla
      const remainingPaymentsForItem = updatedPaymentList
        .filter(payment => payment.orderItem_id === paymentToRemove.orderItem_id)
        .reduce((sum, payment) => sum + parseFloat(payment.amount), 0);

      // Kaydedilmiş parçalı ödemeler
      const savedPayments = paymentState.summaryOrders?.[0]?.partialPayments
        ?.filter(payment => payment.orderItem_id === paymentToRemove.orderItem_id)
        .reduce((sum, payment) => sum + parseFloat(payment.amount), 0) || 0;

      // Toplam ödenen tutar
      const totalPaidForItem = savedPayments + remainingPaymentsForItem;

      // Ürünün ödenen tutarını güncelle
      setRemainingProducts((prevProducts) =>
        prevProducts.map((product) => {
          if (product.id === paymentToRemove.orderItem_id) {
            const totalPrice = parseFloat(product.price) * product.quantity;
            return {
              ...product,
              paidAmount: totalPaidForItem,
              isFullyPaid: totalPaidForItem >= totalPrice
            };
          }
          return product;
        })
      );
    }
  };

  // Ödeme onaylama işleyicisi
  const handleConfirmPayment = () => {
    const remaining = calculateRemainingAmount();

    if (remaining <= 0) {
      // SADECE YENİ EKLENEN ÖDEMELERİ GÖNDER
      // Kaydedilmiş ödemeler zaten backend'de var, tekrar göndermeye gerek yok
      const newPaymentsList = paymentList.map(payment => ({
        method: payment.method,
        amount: parseFloat(payment.amount)
      }));

      // Önce ödeme listesini temizle, sonra ödeme işlemini tamamla
      setPaymentList([]); // Ödeme listesini temizle

      // Backend'e sadece yeni ödemeleri gönder
      onPaymentComplete(table.id, newPaymentsList);
      toast.success('Ödeme başarıyla tamamlandı!');
      const modal = document.getElementById("modal-order-summary-complete-advanced");
      if (modal) {
        modal.close();
      }
    } else {
      toast.error(`Lütfen toplam tutarı tamamlayın! Kalan: ${remaining.toFixed(2)} ${currency}`);
    }
  };



  // Tüm ödemeleri birleştir (mevcut ve yeni eklenenler)
  const allPayments = [
    ...paymentList,
    ...(paymentState.summaryOrders?.[0]?.partialPayments || [])
  ];

  if (!table) {
    return (
      <dialog id="modal-order-summary-complete-advanced" className="modal">
        <p className="text-gray-600 mt-2"> Masa bilgisi alınamadı. Lütfen sistem yöneticisine başvurun.</p>
      </dialog>
    );
  }

  // Modal arayüzü
  return (
    <dialog id="modal-order-summary-complete-advanced" className="modal">
      <div className="modal-box w-[95vw] max-w-[95vw] p-0 max-h-[95vh]">
        {/* Modal başlığı */}
        <div className="flex items-center justify-between border-b p-4 bg-gray-50">
          <h3 className="font-bold text-lg">Ödeme Al</h3>
          <form method="dialog">
            <button className="hover:bg-red-100 border-none transition active:scale-95 bg-red-50 text-red-500 btn btn-sm btn-circle">
              <IconX size={18} stroke={iconStroke} />
            </button>
          </form>
        </div>

        <div className="flex h-[calc(95vh-4rem)] overflow-hidden">
          {/* Sol panel - Ürün listesi */}
          <div className="w-1/3 border-r p-4">
            <h4 className="font-bold text-gray-700 mb-4">Parçalı Ödeme</h4>
            <div className="overflow-y-auto h-[calc(95vh-10rem)]">
              {remainingProducts?.map((item) => {
                // Ürün seçili mi kontrol et
                const isSelected = selectedProducts.some(p => p.id === item.id);

                // Seçili ürün sayısını hesapla
                const selectedCount = selectedProducts.filter(p => p.id === item.id).length;

                return (
                <div
                  key={item.id}
                  className={`flex items-center justify-between mb-2 cursor-pointer p-2 rounded-lg ${
                    item.isFullyPaid
                      ? 'line-through text-gray-400 cursor-not-allowed'
                      : isSelected
                      ? 'bg-blue-100 border-blue-500'
                      : ''
                  }`}
                  onClick={() => !item.isFullyPaid && handleProductSelection(item)}
                >
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center">
                          {isSelected && (
                            <span className="mr-2 text-blue-500 font-bold">
                              {selectedCount > 1 ? `${selectedCount}x` : '✓'}
                            </span>
                          )}
                          <p className="font-medium text-sm">
                            {item.item_title}
                            {item.variant_title && (
                              <span className="text-gray-600 ml-1">
                                ({item.variant_title})
                              </span>
                            )}
                          </p>
                        </div>
                        {Array.isArray(item.addons) && item.addons.length > 0 && (
                          <p className="text-xs text-gray-500">
                            + {item.addons.map((addon) => addon.title).join(', ')}
                          </p>
                        )}

                      </div>
                      <div className="text-right ml-2 text-sm">
                        <p className="font-medium">
                          {parseFloat(item.price).toFixed(2)} x {item.quantity}
                        </p>
                        <p className="text-xs text-gray-500">
                          Ödenen: {item.paidAmount?.toFixed(2)} / Toplam: {(item.price * item.quantity).toFixed(2)} {currency}
                        </p>
                      </div>
                    </div>
                  </div>
                  {isSelected && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();

                        // Aynı üründen kaç tane seçili olduğunu bul
                        const selectedCount = selectedProducts.filter(p => p.id === item.id).length;

                        if (selectedCount > 1) {
                          // Birden fazla seçilmişse, sadece bir tanesini kaldır
                          const firstIndex = selectedProducts.findIndex(p => p.id === item.id);
                          const newSelectedProducts = [...selectedProducts];
                          newSelectedProducts.splice(firstIndex, 1);
                          setSelectedProducts(newSelectedProducts);
                        } else {
                          // Tek seçilmişse, tümünü kaldır
                          setSelectedProducts(selectedProducts.filter(p => p.id !== item.id));
                        }

                        // Input değerinden ürünün fiyatını çıkar
                        const currentValue = parseFloat(inputValue) || 0;
                        const newValue = Math.max(0, currentValue - parseFloat(item.price));
                        setInputValue(newValue.toFixed(2));
                      }}
                      className="p-1 ml-2"
                    >
                      <IconArrowBack size={20} stroke={iconStroke} />
                    </button>
                  )}
                </div>
              )})}
            </div>
          </div>

          {/* Orta panel - Numerik tuş takımı */}
          <div className="w-1/3 border-r p-4 overflow-y-auto">
            <h4 className="font-bold text-gray-700 mb-4">
              Kalan Tutar: <span className="text-red-600">{calculateRemainingAmount().toFixed(2)} {currency}</span>
            </h4>
            <div className="bg-gray-200 p-6 rounded-lg text-right text-4xl font-bold mb-2">
              {inputValue || '0.00'} {currency}
            </div>

            {/* Hızlı Tutar Butonları */}
            <div className="flex gap-2 mb-4">
              <button
                onClick={() => {
                  const remaining = calculateRemainingAmount();
                  setInputValue(remaining.toFixed(2));
                }}
                className="flex-1 py-2 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition"
              >
                Tümü
              </button>
              <button
                onClick={() => {
                  const remaining = calculateRemainingAmount();
                  const half = remaining / 2;
                  setInputValue(half.toFixed(2));
                }}
                className="flex-1 py-2 bg-yellow-500 text-white rounded-lg font-medium hover:bg-yellow-600 transition"
              >
                2/1
              </button>
              <button
                onClick={() => {
                  const remaining = calculateRemainingAmount();
                  const third = remaining / 3;
                  setInputValue(third.toFixed(2));
                }}
                className="flex-1 py-2 bg-green-500 text-white rounded-lg font-medium hover:bg-green-600 transition"
              >
                3/1
              </button>
            </div>

            <div className="grid grid-cols-3 gap-4">
              {[7, 8, 9, 4, 5, 6, 1, 2, 3, '.', 0, '←'].map((value, i) => (
                <button
                  key={i}
                  onClick={() => handleNumericPadClick(value)}
                  className={`w-full h-16 flex items-center justify-center bg-white rounded-lg shadow-md text-xl font-bold hover:bg-gray-100 transition ${
                    value === '←' ? 'text-red-500' : 'text-gray-800'
                  }`}
                >
                  {value}
                </button>
              ))}
            </div>
            <button
              onClick={handleAddProductPayment}
              className="w-full py-3 mt-4 bg-blue-500 text-white rounded-lg font-bold hover:bg-blue-600"
              disabled={calculateRemainingAmount() <= 0}
            >
              Ödeme Ekle
            </button>
          </div>

          {/* Sağ panel - Ödeme özeti ve yöntemleri */}
          <div className="w-1/3 p-4 overflow-y-auto">
            <div className="my-2">
              {/* Ödenen ve Kalan Tutar Bilgisi */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">


                {/* İndirim tutarı gösterimi */}
                {paymentState.discountTotal > 0 && (
                  <div className="flex justify-between items-center mt-2">
                    <span className="font-medium text-green-700">Toplam İndirim:</span>
                    <span className="font-bold text-green-700">
                      {(paymentState.discountTotal || 0).toFixed(2)} {currency}
                    </span>
                  </div>
                )}

                {/* Toplam */}
                <div className="flex justify-between items-center mt-2 border-t pt-2">
                  <span className="font-medium">Toplam:</span>
                  <span className="font-bold text-blue-600">
                    {(paymentState.summaryTotal || 0).toFixed(2)} {currency}
                  </span>
                </div>

                {/* Mevcut Ödemeler */}
                <div className="flex justify-between items-center mt-2">
                  <span className="font-medium">Mevcut Ödemeler:</span>
                  <span className="font-bold text-blue-600">
                    {(paymentState.summaryOrders?.[0]?.totalPaid || 0).toFixed(2)} {currency}
                  </span>
                </div>

                {/* Yeni Eklenen Ödemeler */}
                <div className="flex justify-between items-center mt-2">
                  <span className="font-medium">Yeni Eklenen Ödemeler:</span>
                  <span className="font-bold text-green-600">
                    {paymentList.reduce((sum, payment) => sum + parseFloat(payment.amount), 0).toFixed(2)} {currency}
                  </span>
                </div>

                {/* Kalan Tutar */}
                <div className="flex justify-between items-center mt-2 border-t pt-2">
                  <span className="font-medium">Kalan Tutar:</span>
                  <span className="font-bold text-red-600">
                    {calculateRemainingAmount().toFixed(2)} {currency}
                  </span>
                </div>
              </div>
            </div>

            {/* İndirim Bölümü */}
<div className="my-4 border-t pt-4">
    <div className="flex items-center justify-between mb-2">
        <h4 className="font-bold">İndirim</h4>
        <div className="flex gap-2">
            <button
                onClick={() => setShowDiscountPanel(!showDiscountPanel)}
                className="btn btn-sm btn-ghost"
            >
                {showDiscountPanel ? 'İptal' : 'İndirim Ekle'}
            </button>
            <button
                onClick={() => document.getElementById('modal-discount').showModal()}
                className="btn btn-sm btn-primary"
            >
                Yüzde İndirim
            </button>
        </div>
    </div>

    {showDiscountPanel && (
        <div className="space-y-2">
            <div className="flex gap-2">
                <select
                    value="order"
                    disabled
                    className="select select-bordered select-sm flex-1"
                >
                    <option value="order">Tüm Siparişe</option>
                </select>
            </div>

            <div className="flex gap-2">
                <input
                    type="number"
                    value={discountValue}
                    onChange={(e) => setDiscountValue(e.target.value)}
                    placeholder="Tutar"
                    className="input input-bordered input-sm flex-1"
                    min="0"
                />

                <button
                    onClick={handleApplyDiscount}
                    className="btn btn-sm btn-primary"
                    disabled={!discountValue || parseFloat(discountValue) <= 0}
                >
                    Tutar İndirimi Uygula
                </button>
            </div>
        </div>
    )}

    {/* Uygulanan İndirimler Listesi */}
    {(paymentState.summaryOrders?.[0]?.discounts?.length > 0 ||
      paymentState.summaryOrders?.[0]?.items?.some(item => item.discounts?.length > 0)) && (
        <div className="mt-2 p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="space-y-2 text-sm">
                {paymentState.summaryOrders[0].discounts?.map((discount, index) => (
                    <div key={index} className="flex justify-between items-center py-1 border-b border-green-100">
                        <span className="font-medium text-green-800">Genel İndirim</span>
                        <span className="font-bold text-green-600">
                            {discount.discount_value} {currency}
                        </span>
                    </div>
                ))}
                {paymentState.summaryOrders[0].items?.map(item =>
                    item.discounts?.map((discount, index) => (
                        <div key={`${item.id}-${index}`} className="flex justify-between items-center py-1 border-b border-green-100">
                            <span className="font-medium text-green-800">{item.item_title}</span>
                            <span className="font-bold text-green-600">
                                {discount.discount_value} {currency}
                            </span>
                        </div>
                    ))
                )}
            </div>
        </div>
    )}
</div>

            <h4 className="font-bold mb-4">Ödeme Yöntemleri</h4>
            <form className="grid grid-cols-2 gap-2">
              {paymentTypes.map((paymentType, i) => (
                <div key={i} className="relative">
                  <input
                    checked={paymentState.selectedPaymentType === paymentType.id}
                    onChange={() => onPaymentTypeSelect(paymentType.id)}
                    type="radio"
                    name="payment_type"
                    id={`${paymentType.icon}-${table.id}`}
                    value={paymentType.id}
                    className="peer hidden"
                  />
                  <label
                    htmlFor={`${paymentType.icon}-${table.id}`}
                    className="block border rounded-lg flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-green-500 peer-checked:text-green-500 peer-checked:font-bold cursor-pointer transition"
                  >
                    {paymentType.icon && <div>{PAYMENT_ICONS[paymentType.icon]}</div>}
                    <p className="text-xs">{paymentType.title}</p>
                  </label>
                </div>
              ))}
            </form>

            {/* Ödeme detayları bölümü */}
            <div className="mt-4">
              <h4 className="font-bold mb-4">Ödeme Detayları</h4>
              <div className="max-h-[25vh] overflow-y-auto">
                {allPayments.map((payment, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b">
                    <span className="text-gray-700">
                      {parseFloat(payment.amount).toFixed(2)} {currency} ({payment.methodName || payment.payment_type}) - {payment.productName || payment.product_name || 'Genel'}
                    </span>
                    {/* Sadece yeni eklenen ödemeleri silebilme */}
                    {paymentList.includes(payment) && (
                      <button
                        onClick={() => handleRemovePayment(index)}
                        className="p-2"
                      >
                        <IconTrash size={24} stroke={iconStroke} />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Butonlar kısmı */}
            <div className="flex gap-2 mt-4">
               {/* Kalan tutar sıfırsa sadece "Siparişi Kapat" butonu göster */}
               {calculateRemainingAmount() <= 0 ? (
                 <button
                     onClick={handleConfirmPayment}
                     className="w-full py-3 bg-green-500 text-white rounded-lg font-bold hover:bg-green-600"
                 >
                     Siparişi Kapat
                 </button>
               ) : (
                 <>
                   {/* Parçalı ödeme kaydetme butonu - Kalan tutar varsa göster */}
                   <button
                       onClick={handleSavePartialPayment}
                       disabled={paymentList.length === 0 || isSaving}
                       className="flex-1 py-3 bg-blue-500 text-white rounded-lg font-bold hover:bg-blue-600 disabled:opacity-50"
                   >
                       {isSaving ? 'Kaydediliyor...' : 'Ödemeyi Kaydet'}
                   </button>

                   {/* Siparişi Kapat butonu - Kalan tutar varsa disabled */}
                   <button
                       onClick={handleConfirmPayment}
                       disabled={true}
                       className="flex-1 py-3 bg-gray-400 text-white rounded-lg font-bold cursor-not-allowed opacity-50"
                   >
                       Siparişi Kapat
                   </button>
                 </>
               )}
            </div>

          </div>
        </div>
      </div>

      {/* İndirim Modal */}
      <DiscountModal
        orderId={paymentState.summaryOrders?.[0]?.id}
        onSuccess={() => onOrderUpdate && onOrderUpdate(paymentState.summaryOrders[0].id)}
        remainingAmount={calculateRemainingAmount()}
        currency={currency}
      />
    </dialog>
  );
};

export default PaymentModal;