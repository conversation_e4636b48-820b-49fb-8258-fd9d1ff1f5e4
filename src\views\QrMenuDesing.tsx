import React, { useEffect, useState } from "react";
import Page from "../components/Page";
import { saveTenantConfig, getTenantConfig } from "../controllers/settings.controller";
import { IconX, IconCarrot, IconCategory, IconMenuDeep, IconInfoCircle, IconLanguage } from "@tabler/icons-react";
import toast from "react-hot-toast";

export default function QRMenuCustomizer() {
  const [headerColor, setHeaderColor] = useState("#047857"); // Başlık arka plan rengi
  const [headerTextColor, setHeaderTextColor] = useState("#ffffff"); // Başlık yazı rengi
  const [buttonBgColor, setButtonBgColor] = useState("#fde68a"); // "Menüyü Görüntüle" butonu
  const [buttonTextColor, setButtonTextColor] = useState("#b45309");
  const [footerColor, setFooterColor] = useState("#111827"); // Alt bar arka plan
  const [footerTextColor, setFooterTextColor] = useState("#ffffff");
  const [complaintButtonColor, setComplaintButtonColor] = useState("#fee2e2");
  const [complaintTextColor, setComplaintTextColor] = useState("#b91c1c");
  const [suggestionButtonColor, setSuggestionButtonColor] = useState("#dbeafe");
  const [suggestionTextColor, setSuggestionTextColor] = useState("#1d4ed8");
  const [thankYouButtonColor, setThankYouButtonColor] = useState("#d1fae5");
  const [thankYouTextColor, setThankYouTextColor] = useState("#047857");
  const [bodyBgColor, setBodyBgColor] = useState("#f3f4f6"); // Sayfa arka plan rengi
  const [headTextColor, setheadTextColor] = useState("#FFFFFF"); // Sayfa arka plan rengi
  const [priceTextColor, setpriceTextColor] = useState("#70b56a"); // Sayfa arka plan rengi
  const [textColor, settextColor] = useState("#FFFFFF"); // Sayfa arka plan rengi


  // Toggle özellikleri
  const [showSlider, setShowSlider] = useState(true);
  const [showMenuButton, setShowMenuButton] = useState(true);
  const [showButtons, setShowButtons] = useState(true);
  const [isLoading, setIsLoading] = useState(true); // Yükleme durumu için


  useEffect(() => {
    const fetchTenantConfig = async () => {
      try {
        setIsLoading(true);
        const response = await getTenantConfig();
  
        if (response.success && response.data) {
          const config = response.data;
  
          // Gelen verilerle state'leri güncelle
          setHeaderColor(config.header_color || "#047857");
          setHeaderTextColor(config.header_text_color || "#ffffff");
          setButtonBgColor(config.menu_button_background_color || "#fde68a");
          setButtonTextColor(config.menu_button_text_color || "#b45309");
          setComplaintButtonColor(config.complaint_button_background_color || "#fee2e2");
          setComplaintTextColor(config.complaint_button_text_color || "#b91c1c");
          setSuggestionButtonColor(config.suggestion_button_background_color || "#dbeafe");
          setSuggestionTextColor(config.suggestion_button_text_color || "#1d4ed8");
          setThankYouButtonColor(config.thank_you_button_background_color || "#d1fae5");
          setThankYouTextColor(config.thank_you_button_text_color || "#047857");
          setFooterColor(config.footer_background_color || "#111827");
          setFooterTextColor(config.footer_text_color || "#ffffff");
          setBodyBgColor(config.background_color || "#f3f4f6");
          settextColor(config.text_color || "#f3f4f6");
          setpriceTextColor(config.price_text_color || "#f3f4f6");
          setheadTextColor(config.head_text_color || "#f3f4f6");
        }
      } catch (error) {
        console.error("Tenant config alınırken hata oluştu:", error);
        toast.error("Tenant config alınamadı!");
      } finally {
        setIsLoading(false);
      }
    };
  
    fetchTenantConfig();
  }, []);
  

  const handleSave = async () => {
    const configData = {
      header_color: headerColor,
      header_text_color: headerTextColor,
      menu_button_background_color: buttonBgColor,
      menu_button_text_color: buttonTextColor,
      complaint_button_background_color: complaintButtonColor,
      complaint_button_text_color: complaintTextColor,
      suggestion_button_background_color: suggestionButtonColor,
      suggestion_button_text_color: suggestionTextColor,
      thank_you_button_background_color: thankYouButtonColor,
      thank_you_button_text_color: thankYouTextColor,
      footer_background_color: footerColor,
      footer_text_color: footerTextColor,
      head_text_color: headTextColor,
      background_color: bodyBgColor,
      price_text_color: priceTextColor,
      text_color: textColor,
    };

    try {
      toast.loading("Kaydediliyor...");
      const response = await saveTenantConfig(configData);
      toast.dismiss();
      toast.success(response.message);
    } catch (error) {
      console.error(error);
      toast.dismiss();
      toast.error("Bir hata oluştu!");
    }
  };

  if (isLoading) {
    return (
      <Page className="px-8 py-6">
        <p>Yükleniyor...</p>
      </Page>
    );
  }


  return (
    <Page className="px-8 py-6 flex gap-8">
      {/* Önizleme Alanı */}
      <div className="w-full h-full md:w-1/2 border shadow" style={{ backgroundColor: bodyBgColor }}>
        {/* Başlık */}
        
        <header className="w-full py-4" style={{ backgroundColor: headerColor, color: headerTextColor }}>
          <div className="container mx-auto px-4 flex items-center justify-between">
            <h1 className="text-xl font-bold">Logo</h1>
            <IconMenuDeep size={28} stroke={2} className="cursor-pointer" />
          </div>
        </header>

        {/* Slider */}
        {showSlider && (
          <div className="w-full py-4">
            <div className="container mx-auto px-4">
              <div className="h-60 bg-gray-200 rounded-xl flex items-center justify-center overflow-hidden relative">
                <p className="text-gray-500">Slider Alanı</p>
              </div>
            </div>
          </div>
        )}

        {/* Menüyü Görüntüle */}
        {showMenuButton && (
          <div className="w-full">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 gap-4 mt-4">
                <button
                  className="flex h-20 items-center justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
                  style={{ backgroundColor: buttonBgColor, color: buttonTextColor }}
                >
                  <IconCategory size={24} stroke={1.5} /> Menüyü Görüntüle
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Şikayet ve Öneri Butonları */}
        {showButtons && (
          <div className="w-full">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-2 gap-4 mt-4">
                <button
                  className="flex items-center justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
                  style={{ backgroundColor: complaintButtonColor, color: complaintTextColor }}
                >
                  <IconX size={24} stroke={1.5} />
                  Şikayet
                </button>
                <button
                  className="flex items-center justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
                  style={{ backgroundColor: suggestionButtonColor, color: suggestionTextColor }}
                >
                  <IconCarrot size={24} stroke={1.5} />
                  Öneri
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Teşekkür Butonu */}
        {showButtons && (
          <div className="w-full">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 gap-4 mt-4">
                <button
                  style={{ backgroundColor: thankYouButtonColor, color: thankYouTextColor }}
                  className="flex items-center h-20 justify-center gap-2 p-4 rounded-xl text-center font-bold text-lg"
                >
                  😊 Teşekkür
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Alt Bar */}
        <footer
          className="w-full py-4 shadow-lg flex justify-between items-center px-6 mt-48"
          style={{ backgroundColor: footerColor, color: footerTextColor }}
        >
          <button className="hover:text-gray-400 transition">
            <IconInfoCircle size={28} />
          </button>
          <button className="hover:text-gray-400 transition">
            <IconLanguage size={28} />
          </button>
        </footer>
      </div>

      {/* Özelleştirme Paneli */}
      <div className="w-full">
        <h2 className="text-2xl font-bold mb-6">Tasarım Özelleştirme</h2>

        <button
        onClick={handleSave}
        className="px-4 py-2 bg-blue-500 text-white rounded-lg"
      >
        Kaydet
      </button>

        {/* Görünürlük Ayarları */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Görünürlük Ayarları</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label>
              <input
                type="checkbox"
                checked={showSlider}
                onChange={(e) => setShowSlider(e.target.checked)}
              />{" "}
              Slider Göster
            </label>
            <label>
              <input
                type="checkbox"
                checked={showMenuButton}
                onChange={(e) => setShowMenuButton(e.target.checked)}
              />{" "}
              Menüyü Görüntüle Butonu Göster
            </label>
            <label>
              <input
                type="checkbox"
                checked={showButtons}
                onChange={(e) => setShowButtons(e.target.checked)}
              />{" "}
              Diğer Butonları Göster
            </label>
          </div>
        </div>

        {/* Renk Ayarları Paneli */}
        <div className="w-full bg-white">
          {/* Genel Arka Plan Rengi */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Genel Arka Plan Rengi</h3>
            <input
              type="color"
              value={bodyBgColor}
              onChange={(e) => setBodyBgColor(e.target.value)}
              className="w-full h-12 rounded-lg border border-gray-300"
            />
          </div>
        
          {/* Başlık Ayarları */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Başlık</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Arka Plan Rengi</label>
                <input
                  type="color"
                  value={headerColor}
                  onChange={(e) => setHeaderColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Yazı Rengi</label>
                <input
                  type="color"
                  value={headerTextColor}
                  onChange={(e) => setHeaderTextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
            </div>
          </div>
        
          {/* Menü Butonu Ayarları */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Menü Butonu</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Arka Plan Rengi</label>
                <input
                  type="color"
                  value={buttonBgColor}
                  onChange={(e) => setButtonBgColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Yazı Rengi</label>
                <input
                  type="color"
                  value={buttonTextColor}
                  onChange={(e) => setButtonTextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
            </div>
          </div>
        
          {/* Şikayet Butonu Ayarları */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Şikayet Butonu</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Arka Plan Rengi</label>
                <input
                  type="color"
                  value={complaintButtonColor}
                  onChange={(e) => setComplaintButtonColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Yazı Rengi</label>
                <input
                  type="color"
                  value={complaintTextColor}
                  onChange={(e) => setComplaintTextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
            </div>
          </div>
        
          {/* Öneri Butonu Ayarları */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Öneri Butonu</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Arka Plan Rengi</label>
                <input
                  type="color"
                  value={suggestionButtonColor}
                  onChange={(e) => setSuggestionButtonColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Yazı Rengi</label>
                <input
                  type="color"
                  value={suggestionTextColor}
                  onChange={(e) => setSuggestionTextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
            </div>
          </div>
        
          {/* Teşekkür Butonu Ayarları */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Teşekkür Butonu</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Arka Plan Rengi</label>
                <input
                  type="color"
                  value={thankYouButtonColor}
                  onChange={(e) => setThankYouButtonColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Yazı Rengi</label>
                <input
                  type="color"
                  value={thankYouTextColor}
                  onChange={(e) => setThankYouTextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
            </div>
          </div>

           {/* Teşekkür Butonu Ayarları */}
           <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Ürün Yazı Renkler</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Ürün Başlık Rengi</label>
                <input
                  type="color"
                  value={headTextColor}
                  onChange={(e) => setheadTextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Ürün Fiyat Rengi</label>
                <input
                  type="color"
                  value={priceTextColor}
                  onChange={(e) => setpriceTextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Ürün Açıklama Rengi ve Kampanya Yazıları</label>
                <input
                  type="color"
                  value={textColor}
                  onChange={(e) => settextColor(e.target.value)}
                  className="w-full h-10 rounded-lg border border-gray-300"
                />
              </div>
            </div>
          </div>
        
             {/* Alt Bar Ayarları */}
             <div className="mb-6">
               <h3 className="text-lg font-semibold mb-2">Alt Bar</h3>
               <div className="grid grid-cols-2 gap-4">
                 <div>
                   <label className="text-sm font-medium">Arka Plan Rengi</label>
                   <input
                     type="color"
                     value={footerColor}
                     onChange={(e) => setFooterColor(e.target.value)}
                     className="w-full h-10 rounded-lg border border-gray-300"
                   />
                 </div>
                 <div>
                   <label className="text-sm font-medium">Yazı/İkon Rengi</label>
                   <input
                     type="color"
                     value={footerTextColor}
                     onChange={(e) => setFooterTextColor(e.target.value)}
                     className="w-full h-10 rounded-lg border border-gray-300"
                   />
                 </div>
               </div>
             </div>
           </div>
        </div>
    </Page>
  );
}
