import {
  IconCarrot,
  IconChevronRight,
  IconMenu,
  IconX,
  IconChevronLeft
} from "@tabler/icons-react";
import React, { useEffect, useState, useRef } from "react";
import { iconStroke } from "../../config/config";

import {
  getCart,
  setCart,
  getQRMenuInit,
} from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import toast from "react-hot-toast";
import { useParams, useSearchParams, useNavigate, useLocation  } from "react-router-dom";
import { getQRMenuLink } from "../../helpers/QRMenuHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";

export default function MenuPage() {

  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;

  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;
  const location = useLocation();
  const defaultCategory = location.state?.selectedCategory || "all";

  const { t } = useTranslation(['menu_item', 'category', 'menu_description']);

  // Kategoriler için scroll ref
  const categoryScrollRef = useRef(null);

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    storeTable: null,
    categories: [],
    currentCategory: defaultCategory,
    menuItems: [],
    searchQuery: "",
    currentItem: null,
    cartItems: [],
    currentItemId: null,
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  useEffect(() => {
    if (categoryScrollRef.current) {
      // Seçili kategoriye ait elemente scroll yapacağız
      const selectedElement = document.getElementById(`cat-${defaultCategory}`);
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        });
      }
    }
  }, [defaultCategory]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);

      const storedCart = getCart();
      if (res.status == 200) {
        const data = res.data;

        const currency = CURRENCIES.find(
          (c) => c.cc == data?.storeSettings?.currency
        );
        if (data?.translations) {
                  updateI18nResources(data.translations);
        }

        setState({
          ...state,
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          menuItems: data?.menuItems,
          storeTable: data?.storeTable || null,
          cartItems: [...storedCart],
          currency: currency?.symbol || "",
        });
      }
    } catch (error) {
    }
  };

  const {
    isLoading,
    storeSettings,
    categories,
    menuItems,
    currentCategory,
    searchQuery,
    currentItem,
    cartItems,
    currentItemId,
    currency,
    sew_points
  } = state;

  const storeName = storeSettings?.store_name || "";
  const is_qr_menu_enabled = storeSettings?.is_qr_menu_enabled || false;
  const is_qr_order_enabled = storeSettings?.is_qr_order_enabled1 || false;
  const storeImage = storeSettings?.store_image;

  // Modern tema için özel stiller
  const bodyStyles = {
    background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
    color: storeSettings?.text_color || "#1e293b",
    minHeight: "100vh"
  };

  const headerStyles = {
    background: "linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)",
    color: "#ffffff",
    boxShadow: "0 4px 12px rgba(37, 99, 235, 0.3)"
  };

  const headtextStlyes = {
    color: storeSettings?.head_text_color || "#1e293b",
    fontWeight: "600"
  };

  const pricetextStlyes = {
    color: "#2563eb",
    fontWeight: "700"
  };

  const descriptionStlyes = {
    color: storeSettings?.text_color || "#64748b",
  };

  if (isLoading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center" style={bodyStyles}>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Lütfen bekleyin...</p>
        </div>
      </div>
    );
  }

  if (!qrcode) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center" style={bodyStyles}>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
          <p className="text-red-600 font-semibold">Broken Link!</p>
        </div>
      </div>
    );
  }

  if (!is_qr_menu_enabled) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center" style={bodyStyles}>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
          <p className="text-red-600 font-semibold">Menu Not Available!</p>
        </div>
      </div>
    );
  }

  const QR_MENU_LINK = getQRMenuLink(qrcode);

  const btnShare = async () => {
    const shareData = {
      title: "Menu",
      text: "Menu",
      url: QR_MENU_LINK,
    };

    try {
      if (navigator.canShare) {
        if (navigator?.canShare(shareData)) {
          await navigator.share(shareData);
        }
      } else {
        await navigator.clipboard.writeText(QR_MENU_LINK);
        toast.success("Menu Link Copied!");
      }
    } catch (error) {
    }
  };

  const btnOpenMenuItemDetail = (
    addons,
    variants,
    category_id,
    category_title,
    id,
    imageURL,
    price,
    tax_id,
    tax_rate,
    tax_title,
    tax_type,
    title,
    description,
    sew_points
  ) => {
    setState({
      ...state,
      currentItem: {
        addons: addons,
        variants: variants,
        category_id: category_id,
        category_title: category_title,
        id,
        image: imageURL,
        price,
        tax_id,
        tax_rate,
        tax_title,
        tax_type,
        title,
        description,
        sew_points
      },
    });
    document.getElementById("modal_item_detail").showModal();
  };

  // cart
  function addItemToCart(item) {
    const modifiedItem = {
      ...item,
      quantity: 1,
      notes: null,
    };

    const newCart = cartItems;

    newCart.push(modifiedItem);

    setState({
      ...state,
      cartItems: [...newCart],
    });
    setCart(newCart);
  }

  const btnAddMenuItemToCartWithVariantsAndAddon = () => {
    let price = 0;
    let selectedVariantId = null;
    const selectedAddonsId = [];

    const itemVariants = document.getElementsByName("variants");
    itemVariants.forEach((item) => {
      if (item.checked) {
        selectedVariantId = item.value;
        return;
      }
    });

    price = parseFloat(currentItem.price);

    const itemAddons = document.getElementsByName("addons");
    itemAddons.forEach((item) => {
      if (item.checked) {
        selectedAddonsId.push(item.value);
      }
    });

    const addons = currentItem?.addons || [];
    const variants = currentItem?.variants || [];

    let selectedVariant = null;
    if (selectedVariantId) {
      selectedVariant = variants.find((v) => v.id == selectedVariantId);
      price = parseFloat(selectedVariant.price);
    }

    let selectedAddons = [];
    if (selectedAddonsId.length > 0) {
      selectedAddons = selectedAddonsId.map((addonId) =>
        addons.find((addon) => addon.id == addonId)
      );
      selectedAddons.forEach((addon) => {
        const addonPrice = parseFloat(addon.price);
        price += addonPrice;
      });
    }

    const itemCart = {
      ...currentItem,
      price: price,
      variant_id: selectedVariantId,
      variant: selectedVariant,
      addons_ids: selectedAddonsId,
      addons: selectedAddons,
    };

    addItemToCart(itemCart);
  };

  return (
    <div className="w-full" style={bodyStyles}>
      <header className="w-full py-6 sticky top-0 z-50" style={headerStyles}>
        <div className="container mx-auto px-4 flex items-center justify-between">
          <IconChevronLeft
                      size={28}
                      stroke={3}
                      className="cursor-pointer hover:bg-white/20 rounded-full p-1 transition-all"
                      onClick={() => navigate(-1)}
                    />
          <div className="flex items-center">
            {storeImage ? (
              <img
                src={getImageURL(storeImage)}
                alt={storeName}
                className="h-12 object-contain"
              />
            ) : (
              <h1 className="text-xl font-bold">{storeName}</h1>
            )}
          </div>
          <div className="flex items-center w-6"> </div>
        </div>
      </header>

      <div className="container mx-auto px-4">
        {/* Modern Search Bar */}
        <div className="bg-white/90 backdrop-blur-sm shadow-xl rounded-2xl p-3 w-full md:w-96 mx-auto mt-6 flex gap-2 sticky top-28 z-50 border border-white/20">
          <input
            type="search"
            name="search"
            id="search"
            className="bg-gray-50 rounded-xl outline-none px-4 py-3 flex-1 text-gray-800 placeholder-gray-500 border-0 focus:ring-2 focus:ring-blue-500"
            placeholder="Ürün ara..."
            value={searchQuery}
            onChange={(e) => {
              setState({
                ...state,
                searchQuery: e.target.value,
              });
            }}
          />
        </div>

        {/* Modern Menu Items */}
        <div className="p-2 w-full md:w-96 mx-auto mt-6 flex flex-col gap-6 min-h-screen">
          {menuItems
            .filter((item) => {
              const { category_id } = item;
              if (currentCategory == "all") {
                return true;
              }
              if (currentCategory == category_id) {
                return true;
              }
              return false;
            })
            .filter((menuItem) => {
              if (!searchQuery) {
                return true;
              }
              return new String(menuItem.title)
                .trim()
                .toLowerCase()
                .includes(searchQuery.trim().toLowerCase());
            })
            .map((item, i) => {
              const {
                addons,
                variants,
                category_id,
                category_title,
                id,
                image,
                price,
                description,
                tax_id,
                tax_rate,
                tax_title,
                tax_type,
                title,
                sew_points,
              } = item;

              const imageURL = getImageURL(image);
              const hasVariantOrAddon =
                variants?.length > 0 || addons?.length > 0;

              return (
                <div
                  key={id}
                  className={`w-full bg-white/95 backdrop-blur-md rounded-3xl p-6 flex gap-6 shadow-xl border border-blue-100/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:scale-[1.02] ${
                    !is_qr_order_enabled
                      ? " cursor-pointer"
                      : ""
                  }`}
                  onClick={
                    !is_qr_order_enabled
                      ? () =>
                          btnOpenMenuItemDetail(
                            addons,
                            variants,
                            category_id,
                            category_title,
                            id,
                            image ? imageURL : null,
                            price,
                            tax_id,
                            tax_rate,
                            tax_title,
                            tax_type,
                            title,
                            description,
                            sew_points
                          )
                      : undefined
                  }
                >
                  <div>
                    <div className="rounded-2xl w-36 h-36 object-cover relative bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100 flex items-center justify-center text-blue-600 shadow-lg ring-2 ring-blue-200/50">
                      {image ? (
                        <img
                          src={imageURL}
                          alt={title}
                          className="w-full h-full absolute top-0 left-0 rounded-xl object-cover z-0"
                        />
                      ) : (
                        <IconCarrot size={32} />
                      )}
                      {/* Modern Add Button */}
                      {is_qr_order_enabled && (
                        <button
                          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-2 px-6 rounded-full font-bold hover:from-blue-600 hover:to-blue-700 shadow-lg transition-all duration-300 hover:scale-105"
                          onClick={(e) => {
                            e.stopPropagation();

                            if (hasVariantOrAddon) {
                              btnOpenMenuItemDetail(
                                addons,
                                variants,
                                category_id,
                                category_title,
                                id,
                                image ? imageURL : null,
                                price,
                                tax_id,
                                tax_rate,
                                tax_title,
                                tax_type,
                                title,
                                description,
                                sew_points
                              );
                            } else {
                              addItemToCart(item);
                            }
                          }}
                        >
                          EKLE
                        </button>
                      )}
                    </div>

                    {is_qr_order_enabled &&
                      (variants.length > 0 || addons.length > 0) && (
                        <div className="text-center mt-3">
                          <p className="text-xs text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded-full">Özelleştirebilir</p>
                        </div>
                      )}
                  </div>

                  <div className="flex-1">
                    <p className="text-lg font-semibold" style={headtextStlyes}>{t(`menu_item:${id}`, { defaultValue: title })}</p>
                    <p className="text-lg font-bold" style={pricetextStlyes}>
                      {price} {currency}
                    </p>
                    <div className="flex gap-2 mt-2 text-sm" style={descriptionStlyes}>
                        <p>{t(`menu_description:${id}`)}</p>
                    </div>
                    {sew_points && (
                      <div className="flex gap-2 mt-2 text-sm">
                        <p className="font-bold bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">HollyPuan: {sew_points}</p>
                      </div>
                    )}
                    <div className="flex gap-3 mt-3 text-xs">
                      {variants.length > 0 && (
                        <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">{variants.length} Seçenek</span>
                      )}
                      {addons.length > 0 && (
                        <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">{addons.length} Ekstra</span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
        </div>

        {is_qr_order_enabled && <div className="h-28" />}

        {/* Modern Cart Button */}
        {is_qr_order_enabled && cartItems.length > 0 && (
          <div className="p-4 w-full md:w-96 mx-auto fixed bottom-0 left-0 right-0">
            <button
              onClick={() => {
                navigate(`/m/${qrcode}/cart`, {
                  state: { storeTable: state.storeTable, currency: currency },
                });
              }}
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-4 px-6 flex justify-between items-center rounded-2xl shadow-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 hover:scale-105"
            >
              <p className="text-lg font-bold">
                {cartItems.length} Ürün eklendi
              </p>
              <p className="text-white text-lg font-bold px-3 py-1 bg-white/20 rounded-xl flex gap-2 items-center">
                Sepeti gör
                <IconChevronRight size={20} stroke={3} />
              </p>
            </button>
          </div>
        )}

        {/* Modern Modal */}
        <dialog
          id="modal_item_detail"
          className="modal modal-bottom sm:modal-middle"
        >
          <div className="modal-box bg-white/95 backdrop-blur-sm border border-white/20 shadow-2xl">
            <div className="absolute top-4 right-4">
              <form method="dialog">
                <button className="btn btn-circle bg-gray-100 hover:bg-gray-200 border-0">
                  <IconX stroke={iconStroke} />
                </button>
              </form>
            </div>

            <div className="w-full flex gap-4">
              <div className="rounded-xl w-32 h-32 object-cover relative bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center text-blue-500 shadow-md">
                {currentItem?.image ? (
                  <img
                    src={currentItem?.image}
                    alt={currentItem?.title}
                    className="w-full h-full absolute top-0 left-0 rounded-xl object-cover z-0"
                  />
                ) : (
                  <IconCarrot size={32} />
                )}
              </div>
              <div className="flex-1">
                <p className="text-xl font-semibold text-gray-800">{currentItem?.title}</p>
                <p className="text-lg font-bold text-blue-600">
                  {currentItem?.price} {currency}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  {currentItem?.description}
                </p>
                {currentItem?.sewPoints && (
                  <p className="text-sm font-bold mt-2 bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
                    HollyPuan: {currentItem.sewPoints}
                  </p>
                )}
                <div className="flex gap-2 mt-3 text-xs">
                  {currentItem?.variants.length > 0 && (
                    <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full">{currentItem?.variants.length} Seçenek</span>
                  )}
                  {currentItem?.addons.length > 0 && (
                    <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full">{currentItem?.addons.length} Ekstra</span>
                  )}
                </div>
              </div>
            </div>

            <div className="my-6 flex flex-col sm:flex-row gap-4">
              {currentItem?.variants.length > 0 && (
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 mb-3">Seçenekler</h3>
                  <div className="flex flex-col gap-2">
                    {currentItem?.variants?.map((variant, index) => {
                      const { id, title, price } = variant;
                      return (
                        <label
                          key={index}
                          className="cursor-pointer flex items-center gap-3 p-3 rounded-xl bg-gray-50 hover:bg-blue-50 transition-colors"
                        >
                          <input
                            type="radio"
                            className="radio radio-primary"
                            name="variants"
                            id={id}
                            value={id}
                            defaultChecked={index === 0}
                          />
                          <span className="font-medium text-gray-700">
                            {title} - {price} {currency}
                          </span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              )}

              {currentItem?.addons.length > 0 && (
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 mb-3">Ekstralar</h3>
                  <div className="flex flex-col gap-2">
                    {currentItem?.addons?.map((addon, index) => {
                      const { id, title, price } = addon;
                      return (
                        <label
                          key={index}
                          className="cursor-pointer flex items-center gap-3 p-3 rounded-xl bg-gray-50 hover:bg-green-50 transition-colors"
                        >
                          <input
                            type="checkbox"
                            name="addons"
                            className="checkbox checkbox-primary"
                            value={id}
                          />
                          <span className="font-medium text-gray-700">
                            {title} ( + {price} {currency} )
                          </span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {is_qr_order_enabled && (
              <div className="modal-action flex justify-end w-full">
                <form method="dialog" className="w-full">
                  <button
                    onClick={() => {
                      btnAddMenuItemToCartWithVariantsAndAddon();
                    }}
                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 hover:scale-105 shadow-lg"
                  >
                    Sepete Ekle
                  </button>
                </form>
              </div>
            )}
          </div>
        </dialog>
      </div>
    </div>
  );
}
