import React, { useEffect, useState } from "react";
import {
  IconChevronRight,
  IconChevronLeft,
} from "@tabler/icons-react";
import {
  getCart,
  setCart,
  getQRMenuInit,
} from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import i18n from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";
import { ALLERGENS } from "../../constants/allergens";


export default function MenuPage() {
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const location = useLocation();
  const selectedCategory = location.state?.selectedCategory || null;

  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const { t } = useTranslation(['menu_item', 'category', 'menu_description']);

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    storeTable: null,
    categories: [],
    menuItems: [],
    searchQuery: "",
    cartItems: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  // Dil değiştiğinde namespace'leri yeniden yükle
  useEffect(() => {
    if (i18n.language) {
      console.log("Modern MenuPage - Dil değişti:", i18n.language);
      i18n.loadNamespaces(['menu_item', 'category', 'menu_description']);
    }
  }, [i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);

      const storedCart = getCart();
      if (res.status == 200) {
        const data = res.data;

        const currency = CURRENCIES.find(
          (c) => c.cc == data?.storeSettings?.currency
        );
        if (data?.translations) {
          console.log("Modern MenuPage - Çeviriler yükleniyor:", data.translations.length);
          updateI18nResources(data.translations);
        } else {
          console.log("Modern MenuPage - Çeviri verisi bulunamadı");
        }

        setState({
          ...state,
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          menuItems: data?.menuItems,
          storeTable: data?.storeTable || null,
          cartItems: [...storedCart],
          currency: currency?.symbol || "",
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - Menü - SewPOS`;
        }
      }
    } catch (error) {
      console.error("Menu loading error:", error);
    }
  };

  const {
    isLoading,
    storeSettings,
    categories,
    menuItems,
    searchQuery,
    cartItems,
    currency,
  } = state;

  const storeName = storeSettings?.store_name || "";
  const is_qr_menu_enabled = storeSettings?.is_qr_menu_enabled || false;
  const is_qr_order_enabled = storeSettings?.is_qr_order_enabled1 || false;
  const storeImage = storeSettings?.store_image;

  if (isLoading) {
    return <QRMenuLoading />;
  }

  if (!qrcode) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center font-serif">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border-2 border-amber-200">
          <p className="text-amber-800 font-bold text-xl">Broken Link!</p>
        </div>
      </div>
    );
  }

  if (!is_qr_menu_enabled) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center font-serif">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border-2 border-amber-200">
          <p className="text-amber-800 font-bold text-xl">Menu Not Available!</p>
        </div>
      </div>
    );
  }

  // cart functions
  function addItemToCart(item) {
    const modifiedItem = {
      ...item,
      quantity: 1,
      notes: null,
    };

    const newCart = cartItems;
    newCart.push(modifiedItem);

    setState({
      ...state,
      cartItems: [...newCart],
    });
    setCart(newCart);
  }

  // Kategoriye göre ürünleri grupla
  const getMenuItemsByCategory = () => {
    const filteredItems = menuItems.filter((item) => {
      if (searchQuery) {
        return item.title.toLowerCase().includes(searchQuery.toLowerCase());
      }
      return true;
    });

    const groupedItems = {};

    // Eğer belirli bir kategori seçildiyse, sadece o kategoriyi göster
    if (selectedCategory && selectedCategory !== "all") {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        const categoryItems = filteredItems.filter(item => item.category_id === category.id);
        if (categoryItems.length > 0) {
          groupedItems[category.id] = {
            category: category,
            items: categoryItems
          };
        }
      }
    } else {
      // Tüm kategorileri göster
      categories.forEach(category => {
        const categoryItems = filteredItems.filter(item => item.category_id === category.id);
        if (categoryItems.length > 0) {
          groupedItems[category.id] = {
            category: category,
            items: categoryItems
          };
        }
      });
    }

    return groupedItems;
  };

  const menuByCategory = getMenuItemsByCategory();

  return (
    <div
  className="flex flex-col justify-between min-h-screen w-full"
  style={{ backgroundColor: '#f8f7f3', fontFamily: 'Georgia, "Times New Roman", serif' }}
>

      {/* MINIMAL HEADER */}
      <header className="sticky top-0 z-10" style={{ backgroundColor: '#f8f7f3', }}>
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <IconChevronLeft
              size={24}
              className="cursor-pointer text-gray-600 hover:text-gray-800"
              onClick={() => navigate(-1)}
            />
            <div className="text-center">
              {storeImage ? (
                <img
                  src={getImageURL(storeImage)}
                  alt={storeName}
                  className="h-8 object-contain mx-auto"
                />
              ) : (
                <h1 className="text-lg font-semibold text-gray-900 ">
                  {storeName}
                </h1>
              )}
            </div>
            <div className="w-6"></div>
          </div>
        </div>
      </header>
  
      
  
      {/* PDF STYLE MENU */}
      <div className="flex-1 max-w-4xl mx-auto px-6 pb-8 w-full">
        {Object.keys(menuByCategory).length === 0 ? (
          <div className="text-center py-16">
            <p className="text-gray-500 text-sm " >
              {searchQuery
                ? "Aradığınız ürün bulunamadı"
                : "Menü yükleniyor..."
              }
            </p>
          </div>
        ) : (
          Object.values(menuByCategory).map(({ category, items }) => (
            <div key={category.id} className="mb-8">
              {/* Category Title - PDF Style */}
              <h2 className="text-base font-bold text-gray-900 mb-4 pb-2 border-b border-gray-300 " >
                {(() => {
                  const translatedText = t(`category:${category.id}`, { defaultValue: category.title });
                  console.log(`Modern MenuPage Kategori Çeviri:`, {
                    key: `category:${category.id}`,
                    translated: translatedText,
                    default: category.title,
                    currentLang: i18n.language,
                    hasResource: i18n.hasResourceBundle(i18n.language, 'category')
                  });
                  return translatedText;
                })()}
              </h2>
  
              {/* Items List - PDF Style */}
              <div className="space-y-3">
                {items.map((item) => {
                  const { id, price, title, description } = item;

                  return (
                    <div key={id} className="py-2">
                      <div className="flex justify-between items-start">
                        <div className="flex-1 pr-4">
                          <span className="text-gray-900 text-sm  font-medium">
                            {(() => {
                              const translatedText = t(`menu_item:${id}`, { defaultValue: title });
                              console.log(`Modern MenuPage Çeviri Test:`, {
                                key: `menu_item:${id}`,
                                translated: translatedText,
                                default: title,
                                currentLang: i18n.language,
                                hasResource: i18n.hasResourceBundle(i18n.language, 'menu_item')
                              });
                              return translatedText;
                            })()}
                          </span>
                          {description && (
                            <p className="text-gray-600 text-xs  mt-1 leading-relaxed">
                              {t(`menu_description:${id}`, { defaultValue: description })}
                            </p>
                          )}
{item.allergens && item.allergens.length > 0 && (
  <div className="flex gap-2 mt-1 flex-wrap items-center">
    {item.allergens.map((allergenKey) => {
      // ALLERGENS dizisinden label bilgisi için eşleştirme
      const allergenInfo = ALLERGENS.find(a => a.value === allergenKey);
      if (!allergenInfo) return null;

      return (
       
          <img
            src={`/assets/allergens/${allergenKey}.svg`}
            alt={allergenInfo.label}
            className="w-5 h-5"
            loading="lazy"
          />
          
  
      );
    })}
  </div>
)}

                        </div>
                        {price && parseFloat(price) > 0 && (
                          <span className="text-gray-900 font-medium text-sm flex-shrink-0">
                            {price} {currency}
                          </span>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))
        )}
      </div>
  
      {/* LANGUAGE FOOTER */}
      <footer className="bg-gray-50 border-t border-gray-200 mt-12">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex justify-center">
            <div className="flex items-center space-x-6">
              <button
                onClick={() => i18n.changeLanguage('tr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'tr' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Türkçe
              </button>
              <button
                onClick={() => i18n.changeLanguage('en')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'en' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                English
              </button>
              <button
                onClick={() => i18n.changeLanguage('fr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'fr' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Français
              </button>
              <button
                onClick={() => i18n.changeLanguage('de')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'de' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Deutsch
              </button>
              <button
                onClick={() => i18n.changeLanguage('ru')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ru' ? 'text-gray-900' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Русский
              </button>
            </div>
          </div>
        </div>
      </footer>

      {/* MINIMAL CART */}
      {is_qr_order_enabled && <div className="h-20" />}

      {is_qr_order_enabled && cartItems.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200 z-50">
          <div className="max-w-4xl mx-auto">
            <button
              onClick={() => {
                navigate(`/m/${qrcode}/cart`, {
                  state: { storeTable: state.storeTable, currency: currency },
                });
              }}
              className="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 transition-colors flex items-center justify-between"
            >
              <span>Sepet ({cartItems.length})</span>
              <IconChevronRight size={20} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
  
}