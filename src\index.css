/* @import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700;1,900&display=swap'); */
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* width */
::-webkit-scrollbar {
    width: 1px;
}

/* Track */
::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #d4d4d4;
    border-radius: 100px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #989898;
}

@layer base {
    html {
        font-family: 'Nunito', sans-serif;
        font-optical-sizing: auto;
    }
}

.outline-text {
    text-shadow:
       1px 1px 0 #000,
     -1px -1px 0 #000,
      1px -1px 0 #000,
      -1px 1px 0 #000,
       1px 1px 0 #000;
}

@keyframes fastPulse {
    0%, 100% {
      transform: scale(1);
      background-color: #ef4444; /* red-500 */
    }
    50% {
      transform: scale(1.05); /* çok az büyüt */
      background-color: #dc2626; /* red-600 gibi daha koyu */
    }
  }

  .animate-fastPulse {
    animation: fastPulse 0.8s infinite;
  }

/* Modal açıkken body'yi sabit tut ve scroll çubuğunu gizle */
body.modal-open {
  overflow: hidden !important;
  padding-right: 0 !important;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Masa büyüyüp küçülme animasyonu */
@keyframes tablePulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-table-pulse {
  animation: tablePulse 1.2s infinite ease-in-out;
}

