/**
 * <PERSON><PERSON> ekranı için yardımcı fonksiyonlar
 */

// PIN ayarları için localStorage anahtarı
const PIN_SETTINGS_KEY = "SEWPOS__PIN_SETTINGS";

/**
 * PIN ayarlarını localStorage'dan alır
 * @returns {Object} PIN ayarları
 */
export function getPinSettings() {
  try {
    const settingsString = localStorage.getItem(PIN_SETTINGS_KEY);
    return settingsString ? JSON.parse(settingsString) : getDefaultPinSettings();
  } catch (error) {
    console.error("PIN ayarları alınırken hata oluştu:", error);
    return getDefaultPinSettings();
  }
}

/**
 * Varsayılan PIN ayarlarını döndürür
 * @returns {Object} Varsayılan PIN ayarları
 */
export function getDefaultPinSettings() {
  return {
    autoLockEnabled: true,
    autoLockTime: 10/60, // 10 saniye (dakika cinsinden)
    lastLockTime: Date.now()
  };
}

/**
 * PIN ayarlarını localStorage'a kaydeder
 * @param {Object} settings PIN ayarları
 */
export function savePinSettings(settings) {
  try {
    localStorage.setItem(PIN_SETTINGS_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error("PIN ayarları kaydedilirken hata oluştu:", error);
  }
}

/**
 * Kullanıcı için OTOMATİK kilit ekranının devre dışı bırakılıp bırakılmayacağını kontrol eder
 * @param {Object} user Kullanıcı bilgileri
 * @returns {boolean} Otomatik kilit ekranı devre dışı bırakılmalı mı?
 */
export function shouldDisableAutoLock(user) {
  if (!user) return false;

  // disableScreenLock değeri true ise veya kullanıcı KASIYER rolüne sahipse
  return user.disableScreenLock === true || (user.scope && user.scope.includes("KASIYER"));
}

/**
 * Kullanıcı için kilit ekranının devre dışı bırakılıp bırakılmayacağını kontrol eder
 * @param {Object} user Kullanıcı bilgileri
 * @returns {boolean} Kilit ekranı devre dışı bırakılmalı mı?
 */
export function shouldDisableLockScreen(user) {
  if (!user) return false;

  // Sadece disableScreenLock değeri true ise kilit ekranını devre dışı bırak
  // KASIYER rolü için manuel kilitleme çalışmalı
  return user.disableScreenLock === true;
}

/**
 * Kullanıcı giriş yaptığında PIN ayarlarını günceller
 * @param {Object} user Kullanıcı bilgileri
 * @param {Object} serverPinSettings Sunucudan gelen PIN ayarları (opsiyonel)
 */
export function updatePinSettingsOnLogin(user, serverPinSettings = null) {
  // Varsayılan PIN ayarlarını al
  const defaultSettings = getDefaultPinSettings();

  // Sunucudan gelen PIN ayarları varsa onları kullan, yoksa varsayılan ayarları kullan
  const pinSettings = serverPinSettings || defaultSettings;

  // Kullanıcının disableScreenLock değeri true ise, bunu loglayalım
  if (shouldDisableLockScreen(user)) {
    console.log("Kilit ekranı kullanıcı ayarlarına göre devre dışı bırakıldı");
  }

  // PIN ayarlarını kaydet
  savePinSettings(pinSettings);
}

/**
 * PIN doğrulandığında PIN ayarlarını günceller
 * @param {Object} user Kullanıcı bilgileri
 */
export function updatePinSettingsOnVerify(user) {
  // Yeni PIN ayarlarını oluştur
  const pinSettings = getDefaultPinSettings();

  // PIN ayarlarını kaydet
  savePinSettings(pinSettings);

  // Kullanıcının disableScreenLock değeri true ise, bunu loglayalım
  if (shouldDisableLockScreen(user)) {
    console.log("Kilit ekranı kullanıcı ayarlarına göre devre dışı bırakıldı");
  }
}

/**
 * Kullanıcının boşta kalma süresini kontrol eder ve gerekirse ekranı kilitler
 * @param {number} lastActivityTime Son aktivite zamanı (timestamp)
 * @param {Object} pinSettings PIN ayarları
 * @param {Function} lockFunction Kilitleme fonksiyonu
 * @returns {boolean} Ekran kilitlendi mi?
 */
export function checkIdleTimeAndLock(lastActivityTime, pinSettings, lockFunction) {
  if (!pinSettings.autoLockEnabled) return false;

  const currentTime = Date.now();
  const idleTimeLimit = pinSettings.autoLockTime * 60 * 1000; // Dakikayı milisaniyeye çevir

  if (currentTime - lastActivityTime >= idleTimeLimit) {
    lockFunction();
    return true;
  }

  return false;
}
