{"login": {"title": "<PERSON><PERSON>", "email_label": "Email", "email_placeholder": "Enter Your email here...", "password_label": "Password", "password_placeholder": "Enter Your Password here...", "forgot_password": "Forgot Password?", "login_button": "<PERSON><PERSON>", "or": "OR", "create_account": "Create Account", "loading_message": "Please wait...", "username_error": "Please provide username!", "password_error": "Please provide password!", "success_message": "Login successful!", "error_message": "Something went wrong!"}, "home": {"cafe_restaurant": "Cafe. Restaurant.", "hotel_bar": "Hotel. Bar."}, "register": {"title": "Register", "business_name_label": "Business Name", "business_name_placeholder": "Enter Your Business Name here...", "email_label": "Email", "email_placeholder": "Enter Your Email here...", "password_label": "Password", "password_placeholder": "Enter Your Password here...", "register_button": "Register", "or": "OR", "signin_button": "Signin", "loading_message": "Please wait...", "business_name_error": "Please provide business name!", "email_error": "Please provide email!", "password_error": "Please provide password!", "valid_email_error": "Please provide valid email!", "error_message": "We're getting issues while processing the request, try later!", "signup_today": "Signup Today &", "increase_productivity": "Increase Productivity"}, "users": {"loading": "Loading...", "error_loading": "Error Loading details! Please try later!", "name_error": "Please provide name!", "username_error": "Please provide username!", "password_error": "Please provide password!", "valid_email_error": "Please provide valid email!", "loading_message": "Please wait...", "error_message": "Something went wrong!", "delete_confirm": "Are you sure! This process is irreversible!", "invalid_request": "Invalid Request!", "new_password_error": "Provide new password!", "title": "Users", "new": "New", "scopes": "<PERSON><PERSON><PERSON>", "edit_user": "Edit User", "reset_password": "Reset Password", "delete_user": "Delete User", "add_new_user": "Add New User", "name_label": "Name", "name_placeholder": "Enter Full Name here...", "required": "Required", "email_label": "Email", "email_placeholder": "Enter Email here...", "password_label": "Password", "password_placeholder": "Enter Password here...", "phone_label": "Phone", "phone_placeholder": "Enter Phone Number here...", "designation_label": "Designation", "designation_placeholder": "Enter Designation here...", "scope_label": "<PERSON><PERSON>", "scope_tooltip": "scopes are like permission", "scope_placeholder": "Select Scope", "scope_select": "Select Scope to add", "close": "Close", "save": "Save", "update_user": "Update User", "username_label": "Username", "username_placeholder": "Enter <PERSON>rname here..."}, "reset_password": {"title": "Reset Password", "new_password_label": "New Password", "new_password_placeholder": "Enter Your New password here...", "reset_button": "Reset Password", "loading_message": "Please wait...", "invalid_request": "Invalid request!", "new_password_error": "Please provide new password!", "success_message": "Password reset successful!", "error_message": "Error processing your request, Please try later!"}, "reservations": {"title": "Reservations", "new": "New", "search_placeholder": "Search Reservation", "search_button": "Search", "showing_search_result": "Showing Search Result for \"{{search}}\"", "showing_reservations_for": "Showing Reservations for {{filter}}", "no_reservation": "Table for... no one? 🍽️❌", "double_checked": "We double-checked—still no reservations.", "filter": "Filter", "filter_label": "Filter", "from": "From", "to": "To", "close": "Close", "apply": "Apply", "add_reservation": "Add Reservation", "select_customer": "Select Customer", "required": "Required", "type_to_find": "Type something to find...", "date": "Date", "select_date": "Select Date", "table": "Table", "select_table": "Select Table", "capacity": "Capacity", "people_count": "People Count", "enter_people_count": "Enter People Count", "status": "Status", "select_status": "Select Status", "booked": "Booked", "paid": "Paid", "cancelled": "Cancelled", "notes": "Notes", "enter_notes": "Enter Notes Here...", "save": "Save", "update_reservation": "Update Reservation", "loading_message": "Please wait...", "error_message": "Error loading details! Please try later!", "no_result": "No result found!", "customer_search_error": "Please provide customer phone number to search!", "select_customer_error": "Please select customer!", "select_date_error": "Please select date & time!", "delete_confirm": "Are you sure! This process is irreversible!", "invalid_request": "Invalid request!", "filters": {"today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "last_7days": "Last 7 Days", "this_month": "This Month", "last_month": "Last Month", "custom": "Custom Date Range"}}, "reports": {"title": "Reports", "filters": {"today": "Today", "yesterday": "Yesterday", "last_7days": "Last 7 Days", "this_month": "This Month", "last_month": "Last Month", "custom": "Custom Date Range"}, "loading_message": "Please wait...", "error_message": "Error Loading Reports Data, Please try later!", "saved_successfully": "Saved Successfully!", "error_saving_file": "Error saving file! Try later!", "filters_button": "Filters", "download_button": "Download", "showing_data_for": "Showing Data for {{filter}}", "top_selling_items": "Top Selling Items", "no_bestsellers": "Uh-oh! 🚨", "no_bestsellers_message": "No Bestsellers found.", "orders": "Orders", "avg_order_value": "Avg. Order Value", "total_customers": "Total Customers", "new_customers": "New Customers", "repeat_customers": "Repeat Customers", "revenue": "Revenue", "payments_by_methods": "Payments By Methods", "net_sales": "Net Sales", "tax": "Tax", "service_charge_total": "Service Charge Total", "filter": "Filter", "filter_label": "Filter", "from": "From", "to": "To", "close": "Close", "apply": "Apply"}, "qr_menu": {"loading_message": "Please wait...", "broken_link": "Broken Link!", "menu_not_available": "Menu Not Available!", "menu": "<PERSON><PERSON>", "menu_link_copied": "Menu Link Copied!", "search_placeholder": "Search Menu...", "all": "All", "add": "ADD", "customisable": "Customisable", "category": "Category", "variants_available": "Variants available", "addons_available": "Addons available", "items_added": "Items Added", "view_cart": "View Cart", "variants": "Variants", "addons": "Addons", "add_to_cart": "ADD TO CART", "price_disclaimer": "Shown prices might be inclusive/exclusive of applicable taxes; refer to the final bill for payment amount."}, "profile": {"title": "My Profile", "name_label": "Name:", "designation_label": "Designation:"}, "print_token": {"token_no": "Token No."}, "print_receipt": {"phone": "Phone", "email": "Email", "order_type": "Order Type", "payment_method": "Payment Method", "receipt_no": "Receipt No.", "addons": "Addons", "notes": "Notes", "subtotal": "Subtotal (excl. tax)", "tax": "Tax", "service_charge": "Service Charge", "total": "Total", "token_no": "Token No."}, "payment_success": {"title": "Success!", "message": "Subscription created successfully, Please logout and Sign in again to access the App!", "logout_button": "Logout"}, "payment_cancelled": {"title": "Cancelled!", "message": "Payment Cancelled/Failed, you can try again!", "go_back_button": "Go Back to Profile"}, "order_success": {"thank_you_message": "Thank you for choosing us!", "order_placed_message": "Order Placed Successfully"}, "order_failed": {"oops_message": "Oops! Something went wrong", "order_failed_message": "Order Failed", "try_again_message": "Try Again!"}, "orders": {"title": "Orders", "refresh": "Refresh", "no_orders": "No orders in the queue! 📋", "token": "Token:", "print_receipt": "Print Receipt", "collect_feedback": "Col<PERSON>", "cancel": "Cancel", "complete": "Complete", "pay_complete": "Pay & Complete", "preparing": "Preparing", "completed": "Complete", "delivered": "Delivered", "cancelled": "Cancel", "order_item_status_success": "Order Item status successfully updated!", "cancel_order_alert": "Are you sure, you are cancelling the order! This process is not reversible. 🛑✋", "complete_order_alert": "Are you sure, you are completing the order! 🛑✋", "pay_complete_order": "Pay & Complete Order!", "items_net_total": "Items Net Total", "tax_total": "Tax Total", "service_charge_total": "Service Charge Total", "payable_total": "Payable Total", "print_receipt_option": "Print Receipt?", "pay_complete_order_button": "Pay & Complete Order", "collect_feedback_title": "Col<PERSON>", "collect_feedback_message": "\"A little scan, a big impact, We'll improve—that's a fact!\" ✨", "open_in_new_tab": "Open in New Tab", "loading_message": "Please wait...", "success": "Success", "close": "Close", "alert": "<PERSON><PERSON>", "dismiss": "<PERSON><PERSON><PERSON>", "confirm": "Confirm", "feedback_message": "\"A little scan, a big impact,<br/>We'll improve—that's a fact!\" ✨", "orders_loaded": "Orders Loaded!", "error_loading_orders": "Error loading orders! Please try later!", "error_processing_request": "Error processing your request, Please try later!", "please_finish_order_for_feedback_message": "Please finish the order to collect feedback!", "no_orders_img_alt": "no orders", "dine_out_or_delivery": "Dine Out / Delivery", "addons": "Addons:", "notes": "Notes:", "or": "OR", "feedback_qr_img_alt": "feedback qr", "payment_not_collected_error": "Action cannot be processed because payment has not been collected. Please use the 'Pay & Complete' option instead."}, "no_access": {"title": "Oops! You don't have access to this area! Relogin to check again.", "go_back": "Go back"}, "kitchen": {"title": "Kitchen", "refresh": "Refresh", "no_pending_orders": "No pending orders right now. Time for a deep breath… or a coffee break! ☕", "token": "Token:", "start_making": "Start Making", "complete": "Complete", "preparing": "Preparing", "completed": "Complete", "cancelled": "Cancel", "delivered": "Delivered", "loading_message": "Please wait...", "error_loading_orders": "Error loading orders! Please try later!", "error_processing_request": "Error processing your request, Please try later!", "addons": "Addons:", "notes": "Notes:", "no_orders_img_alt": "no orders"}, "invoices": {"title": "Invoices", "search_placeholder": "Search Invoices", "search": "Search", "filter": "Filter", "close": "Close", "apply": "Apply", "no_invoices": "404: Invoices Not Found 🔍❌ Maybe check a different date or filter!", "invoice_id": "Invoice ID:", "tokens": "Tokens", "date": "Date", "subtotal": "Subtotal", "tax": "Tax", "service_charge": "Service Charge", "total": "Total", "delivery_type": "Delivery Type", "customer": "Customer", "table": "Table", "action": "Action", "view_receipt": "View Receipt", "print_receipt": "Print Receipt", "showing_invoices_for": "Showing Invoices for {{filter}}", "showing_search_result": "Showing Search Result for \"{{search}}\"", "loading_message": "Please wait...", "error_loading_details": "Error loading details! Please try later!", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday", "last_7days": "Last 7 Days", "this_month": "This Month", "last_month": "Last Month", "custom": "Custom", "from": "From", "to": "To", "error_loading_orders": "Error loading orders! Please try later!", "no_result_found": "No result found!", "something_went_wrong": "Something went wrong! Try later!", "error_processing_request": "Error processing your request, Please try later!", "please_wait": "Please wait..."}, "inactive_subscription": {"no_active_subscription_admin": "You don't have any active Subscription! Get one by clicking below! If already subscribed, then signout and login again!", "no_active_subscription_user": "You don't have any active Subscription!", "subscribe_button": "Subscribe", "price_per_month": "per month", "features": {"unlimited_orders": "✅ Unlimited Orders", "monthly_renewals": "✅ Monthly Renewals", "unlimited_devices": "✅ Unlimited Devices", "live_kitchen_orders": "✅ Live Kitchen Orders"}}, "forgot_password": {"title": "Forgot Password", "email_label": "Email", "email_placeholder": "Enter Your email here...", "reset_button": "Reset Password", "loading_message": "Please wait...", "username_error": "Please provide username!", "error_message": "Error processing your request, Please try later!"}, "feedback": {"dashboard_title": "Feedback Dashboard", "overall_feedback": "Overall Feedback", "peoples": "Peoples", "loved_it": "Loved it", "good": "Good", "average": "Average", "bad": "Bad", "worst": "Worst", "food_quality": "Food Quality", "ambiance": "Ambiance", "staff_behavior": "Staff Behavior", "service": "Service", "recommend": "Would You Recommend?", "search_placeholder": "Search...", "search_button": "Search", "export_button": "Export", "date": "Date", "customer": "Customer", "phone": "Phone", "invoice": "Invoice", "overall_feedback_column": "Overall Feedback", "remarks": "Remarks", "action": "Action", "view_button": "View", "filter_button": "Filter", "filter_title": "Filter", "filter_label": "Filter", "from": "From", "to": "To", "close_button": "Close", "apply_button": "Apply", "no_result_found": "No result found!", "something_went_wrong": "Something went wrong! Try later!", "loading_message": "Please wait...", "understand_feedback": "Understand the Feedback", "loved_it_description": "People really liked, it means 4.5 to 5 star ratings.", "good_description": "People Liked your service. means 3.5 to 4.4 star ratings.", "average_description": "Not Good Not bad. means 2.5 to 3.4 star ratings.", "bad_description": "Not Good. means 1.5 to 2.4 star ratings.", "worst_description": "Very Bad, customers didn't liked at all, 1 to 1.4 star rating.", "today": "Today", "yesterday": "Yesterday", "last_7days": "Last 7 Days", "this_month": "This Month", "last_month": "Last Month", "custom": "Custom", "average_rating": "Average Rating", "feedback": "<PERSON><PERSON><PERSON>", "close": "Close", "apply": "Apply", "food_quality_question": "How was the Food Quality?", "food_quality_greeting": "Did the flavors dance and sing?\n Tell us your thoughts on everything! 🎶", "service_question": "How was the Service?", "service_greeting": "Did we serve with a smile and care?\n Let us know, don't be unfair! 😊", "staff_behavior_question": "How was the Staff Behavior?", "staff_behavior_greeting": "Was the team kind and sweet?\n Did they make your meal a treat? 🍬", "ambiance_question": "How was the Ambiance?", "ambiance_greeting": "Was the vibe just right tonight?\n Did it make your evening bright? ✨", "recommend_question": "Would you Recommend?", "recommend_greeting": "Would you tell your friends to dine?\nOr is there room for us to shine? ✨", "provide_feedback": "Please provide your valuable Feedback", "enter_details": "Enter your details to save your response!", "name_label": "Name", "name_placeholder": "Enter your name here...", "phone_label": "Phone", "phone_placeholder": "Enter your phone here...", "email_label": "Email", "email_placeholder": "Enter your email here...", "birthdate_label": "Birthdate", "birthdate_placeholder": "Enter your Birth Date here...", "remarks_label": "Remarks", "remarks_placeholder": "Enter your Remarks here...", "required": "Required", "next": "Next", "submit": "Submit", "select_option_error": "Please select an option before proceeding!", "fill_required_fields_error": "Please fill in all required fields!", "error_saving_details": "Error saving details!"}, "feedback_success": {"thank_you_message": "Thank you for choosing us!", "feedback_saved_message": "Feed<PERSON> Saved Successfully"}, "dashboard": {"title": "Dashboard", "reservations": "Reservations", "no_reservations": "No Reservations found.", "top_selling_items": "Top Selling Items", "no_top_selling_items": "No Bestsellers today.", "orders": "Orders", "new_customers": "New Registered Customers", "repeat_customers": "Repeat Customers", "view_more_reports": "View More Data in Reports", "loading_message": "Please wait...", "error": "Error loading details!"}, "customers": {"title": "Customers", "new": "New", "search_placeholder": "Search Customer", "search": "Search", "actions": "Actions", "download_visible": "Download Visible", "download_all": "Download All", "import": "Import", "no_customers_found": "Hmmm… that's odd! 🤨 No Customers found.", "phone": "Phone", "name": "Name", "email": "Email", "birth_date": "Birth Date", "gender": "Gender", "created_at": "Created At", "updated_at": "Updated At", "edit": "Edit", "delete": "Delete", "update_customer": "Update Customer", "required": "Required", "close": "Close", "save": "Save", "please_wait": "Please wait...", "error_loading_details": "Error loading details! Please Try Later!", "something_went_wrong": "Something went wrong!", "no_data_found": "No data found!", "please_provide_phone": "Please Provide Customer's Phone!", "please_provide_name": "Please Provide Customer's Name!", "please_provide_valid_email": "Please Provide Valid Email!", "please_provide_valid_phone": "Please provide valid phone no.!", "are_you_sure": "Are you sure! This Process is irreversible!", "showing": "Showing", "of": "of", "female": "Female", "male": "Male", "other": "Other", "phone_placeholder": "Enter Phone Number here...", "name_placeholder": "Enter your name here...", "email_placeholder": "Enter Your Email here...", "birthdate_placeholder": "Enter your Birth Date here..."}, "customers_import": {"title": "Import Customers", "breadcrumb_customers": "Customers", "breadcrumb_import": "Import", "download_template": "Download Template", "select_file": "Select .csv file to import customers", "file_note": "*First download the template file, and modify the data accordingly then upload. Note: if duplicate data was found then it will be overwritten, if this is not intended then remove the duplicate values from your data.", "file_ready": "{{fileName}} is ready to upload.", "customers_count": "<b>{{count}} customers</b> available in file. Press Proceed to start the process.", "proceed_button": "Proceed", "error_downloading_file": "Error downloading file!", "column_mismatch": "Column order mismatch, please download Template CSV file, and fill data in it, then upload!", "no_data": "The CSV file has no data!", "no_data_found": "No data found in selected file!", "loading_message": "Please wait...", "error_processing_request": "Error processing your request! try later!"}, "cart": {"title": "CART", "back_button": "Back", "empty_cart": "Your cart is empty.", "total": "Total", "checkout": "Checkout", "continue_as": "Continue as", "guest": "Guest", "or": "or", "using_phone_number": "Using Phone Number", "name_label": "Name", "name_placeholder": "Enter your name", "phone_label": "Phone Number", "phone_placeholder": "Enter your phone number", "place_order": "Place Order", "notes": "Notes", "add_notes": "Add Notes", "items_total": "Items Total", "tax_total": "Tax Total", "service_charge_total": "Service Charge Total", "payable_total": "Payable Total", "name_required": "Name is Required.", "valid_phone_error": "Enter a valid phone number.", "please_wait": "Please wait...", "something_went_wrong": "Something went wrong!", "order_success": "Order placed successfully!", "order_failed": "Order failed!", "notes_placeholder": "Enter Notes here...", "close": "Close", "save": "Save"}, "appbar": {"search_placeholder": "Search", "search_button": "Search", "profile": "Profile", "language": "Language", "logout": "Logout", "dashboard": "Dashboard", "pos": "POS - Point of Sale", "kitchen": "Kitchen", "orders": "Orders", "reservations": "Reservations", "customers": "Customers", "invoices": "Invoices", "users": "Users", "reports": "Reports", "store_settings": "Store Settings", "print_settings": "Print Settings", "store_tables": "Store Tables", "menu_items": "Menu Items", "tax_setup": "Tax Setup", "devices": "Devices", "support": "Support", "press_esc_to_close": "Press ESC key or click X button to close", "my_devices": "My Devices", "change_language": "Change Language", "select_language": "Select Language"}, "toast": {"please_wait": "Please wait...", "something_went_wrong": "Something went wrong! Try later!", "logout_success": "Logout successful!"}, "navbar": {"dashboard": "Dashboard", "pos": "POS", "orders": "Orders", "kitchen": "Kitchen", "offerings": "Offerings", "reservation": "Reservation", "customers": "Customers", "invoices": "Invoices", "back_office": "Back Office", "feedbacks": "Feedbacks", "users": "Users", "reports": "Reports", "settings": "Settings"}, "pos_menu": {"not_found_title": "Bummer!", "not_found_message": "The chef couldn't find anything.", "not_found_carrot": "In the meantime, here's a carrot 🥕 to keep you company!", "variants": "Variants", "addons": "Addons", "add": "ADD"}, "rating": {"worst": "Worst", "bad": "Bad", "average": "Avg.", "good": "Good", "loved": "Loved"}, "subscription": {"details": "Subscription Details", "status": "Status", "active": "Active", "inactive": "In-Active", "renews_at": "Renews at", "cancel_subscription": "Cancel Subscription", "cancel_confirm": "Are you sure? This will immediately cancel your subscription, and you will lose out access to the product.", "cancel_success": "Subscription cancelled successfully!", "cancel_error": "Oops! We got issues while processing your request! Please try after sometime! Or contact customer support!"}, "settings": {"details": "Details", "print_settings": "Print Settings", "tables": "Tables", "menu_items": "Menu Items", "tax_setup": "Tax Setup", "payment_types": "Payment Types", "store_details": "Store Details", "store_image": "Store Image", "no_image": "No Image", "store_name": "Store Name", "store_name_placeholder": "Enter Store Name here...", "address": "Address", "address_placeholder": "Enter Store Address here...", "email": "Email", "email_placeholder": "Enter Email here...", "phone": "Phone", "phone_placeholder": "Enter Phone here...", "currency": "<PERSON><PERSON><PERSON><PERSON>", "select_currency": "Select Currency", "enable_qr_menu": "Enable QR Menu", "qr_menu_tooltip": "This will enable digital menu which can be accessed via link and QR code!", "download_qr_code": "Download QR Code", "view_digital_menu": "View Digital Menu", "enable_qr_order": "Enable Order Via QR Menu", "qr_order_tooltip": "This will enable ordering via QR menu which can be accessed via link and QR code!", "enable_feedback": "Enable Collecting Fe<PERSON>back", "feedback_tooltip": "This will enable collecting feedback from customer!", "save": "Save", "please_wait": "Please wait...", "error_loading_data": "Error loading data, Try Later!", "something_went_wrong": "Something went wrong!", "success_message": "Setting<PERSON> saved successfully!", "error_message": "Error saving settings!"}, "reservation": {"view_notes": "View Notes", "status": "Status", "created_at": "Created at"}, "landing_page": {"features": "Features", "pricing": "Pricing", "contact": "Contact", "login": "<PERSON><PERSON>", "get_started": "Get Started", "all_in_one_pos": "All-in-One POS", "for_your_business": "for Your Food & Beverage Business.", "hero_description": "Effortless POS. Unparalleled Growth. RestroPRO POS empowers you with the tools you need to streamline operations, increase staff productivity, and gain valuable customer insights. Make data-driven decisions, optimize your menu, and watch your foodservice business flourish.", "view_pricing": "View Pricing", "minimal_ui": "Minimal UI", "minimal_ui_description": "Effortless Interface, RestroPRO POS boasts a clean and intuitive design. No cluttered screens, just the essentials you need to manage your business with ease.", "pos": "POS", "pos_description": "RestroPRO POS simplifies sales. Manage orders, categories & variants with ease. Send to kitchen instantly & accept payments securely. All-in-one for a smooth flow.", "live_updates": "Live Updates", "live_updates_description": "Kitchen in Sync, Never miss a beat. Live order updates send details directly to your kitchen, ensuring accuracy and minimizing prep time.", "per_month": "per month", "unlimited_orders": "✅ Unlimited Orders", "monthly_renewals": "✅ Monthly Renewals", "unlimited_devices": "✅ Unlimited Devices", "live_kitchen_orders": "✅ Live Kitchen Orders", "have_any_queries": "Have any queries?", "contact_us": "Contact us", "privacy_policy": "Privacy Policy", "refund_policy": "Refund Policy", "terms_conditions": "Terms & Conditions", "made_with_love": "Made with ❤️ by UIFLOW", "language": "Language", "footer_description": "App for Restaurant, Cafe, Bars, Food Trucks, Quick Serving Restaurants."}, "categories": {"title": "Categories", "new": "New", "add_new_category": "Add New Category", "category_title": "Category Title", "enter_category_title": "Enter Category Title...", "update_category": "Update Category", "please_provide_category_title": "Please provide Category Title!", "save": "Save", "close": "Close", "actions": "Actions", "please_wait": "Please wait...", "error_loading_data": "Error loading data, Try Later!", "something_went_wrong": "Something went wrong!", "are_you_sure": "Are you sure! This process is irreversible!"}, "contact_support": {"title": "Contact Support", "need_help": "Need Help?", "description": "Facing any issues, Custom Feature, New App Development, New Project, connect with us we will respond in 24 hrs. Mail: <b><u>{{email}}</u></b>", "send_mail": "Send mail!", "copy": "Copy", "copied_to_clipboard": "Copied to clipboard!", "or_open_with": "or open with...", "default_mail_app": "Default Mail App", "gmail_in_browser": "GMail in Browser"}, "devices": {"title": "Devices", "please_wait": "Please wait...", "error_loading_devices": "Error loading devices, Please try later!", "created_at": "Created At", "this_device": "This Device", "remove_device_confirm": "Are you sure! This device will be logged out!", "something_went_wrong": "Something went wrong!"}, "menu_items": {"title": "<PERSON><PERSON>", "new": "New", "categories": "Categories", "add_new_item": "Add New Item", "item_title": "Title", "item_description": "Description", "item_price": "Price", "item_net_price": "Net Price", "item_category": "Category", "item_tax": "Tax", "enter_item_title": "Enter Item Title", "enter_item_description": "Enter Item Description", "enter_item_price": "Enter Item Price", "enter_item_net_price": "Enter Item Net Price", "select_category": "Select Category", "select_tax": "Select Tax", "save": "Save", "close": "Close", "please_wait": "Please wait...", "error_loading_details": "Error loading details! Please Try Later!", "please_enter_title": "Please enter title!", "please_provide_valid_price": "Please provide valid price!", "something_went_wrong": "Something went wrong!", "process_irreversible": "Are you sure! This process is irreversible!", "variants": "Variants", "visibility_toggle": "Visibility Toggle", "edit": "Edit", "delete": "Delete", "none": "None", "max_chars": "- (max 500 characters)"}, "tax_setup": {"title": "Tax Setup", "new": "New", "service_charge_label": "Service Charge %", "service_charge_placeholder": "Enter Percentage..", "add_new_tax": "Add New Tax", "update_tax": "Update Tax", "tax_title_label": "Title", "tax_title_placeholder": "Enter Tax Title", "tax_rate_label": "Rate", "tax_rate_placeholder": "Enter Tax Rate", "tax_type_label": "Type", "tax_type_placeholder": "Select Tax Type", "exclusive": "Exclusive", "inclusive": "Inclusive", "actions": "Actions", "save": "Save", "close": "Close", "please_wait": "Please wait...", "error_loading_data": "Error loading data, Try Later!", "provide_title_error": "Provide Title!", "invalid_tax_rate_error": "Invalid value provided in Tax Rate!", "select_tax_type_error": "Select Tax Type!", "something_went_wrong_error": "Something went wrong!", "delete_confirm": "Are you sure! This process is irreversible!", "service_charge_error": "Please provide service charge!", "invalid_service_charge_error": "Invalid service charge value!", "loading_message": "Please wait...", "error_message": "Something went wrong!"}, "table_settings": {"please_wait": "Please wait...", "error_loading_data": "Error loading data, Try Later!", "are_you_sure": "Are you sure! This process is irreversible!", "something_went_wrong": "Something went wrong!", "provide_title_error": "Please provide Title!", "provide_floor_error": "Please provide Floor or use '-'", "provide_seating_capacity_error": "Please provide Seating Capacity or '0'", "provide_valid_seating_capacity_error": "Please provide Valid Seating Capacity count or '0'", "enable_qr_menu_error": "Please enable QR menu from store settings!", "store_tables": "Store Tables", "new": "New", "download_table_qr": "Download Table QR", "add_new_table": "Add New Table", "title": "Title", "enter_table_title": "Enter Table Title", "floor": "Floor", "enter_floor_title": "Enter Floor Title", "seating_capacity": "Seating Capacity", "enter_seating_capacity": "Enter Seating Capacity", "close": "Close", "save": "Save", "update_table": "Update Table"}, "print_settings": {"title": "Print Settings", "enable_print": "Enable Print", "enable_print_tooltip": "This will Print Receipt when you create order!", "show_store_details": "Show Store Details", "show_store_details_tooltip": "Details like Address, Name, Phone will appear in Receipt!", "show_customer_details": "Show Customer Details", "show_customer_details_tooltip": "Customer Name, Phone, etc. will appear in Receipt!", "format_page_size": "Format (Page Size)", "select_page_size": "Select Page Size", "header": "Header", "header_placeholder": "<PERSON><PERSON>er here...", "footer": "Footer", "footer_placeholder": "En<PERSON>er here...", "show_notes": "Show Notes", "show_notes_tooltip": "Extra notes will appear in Receipt!", "print_token": "Print Token", "print_token_tooltip": "Along with receipt, the token number is printed for maintaining queue in your store, and this will be given to <PERSON> as well. Every day token number will be resetted automatically.", "save": "Save", "please_wait": "Please wait...", "error_loading_data": "Error loading data, Try Later!", "something_went_wrong": "Something went wrong!", "success_message": "Setting<PERSON> saved successfully!", "error_message": "Error saving settings!"}, "payment_types": {"title": "Payment Types", "new": "New", "add_new_payment_type": "Add New Payment Type", "payment_type_title": "Payment Type Title", "enter_payment_type": "Enter Payment Type", "icon": "Icon", "select_icon": "Select Icon", "active": "Active?", "save": "Save", "close": "Close", "please_wait": "Please wait...", "error_loading_data": "Error loading data, Try Later!", "something_went_wrong": "Something went wrong!", "success_message": "Payment type saved successfully!", "error_message": "Error saving payment type!", "delete_confirm": "Are you sure! This process is irreversible!", "update_payment_type": "Update Payment Type"}, "menu_item": {"please_wait": "Please wait...", "error_loading_details": "Error loading details! Please Try Later!", "title": "Title", "description": "Description", "price": "Price", "net_price": "Net Price", "category": "Category", "tax": "Tax", "save": "Save", "show_variants": "Show Variants", "show_addons": "Show Addons", "add_variant": "<PERSON><PERSON>", "add_addon": "<PERSON><PERSON>", "variant_title": "Variant Title", "variant_price": "<PERSON><PERSON><PERSON>", "addon_title": "Addon Title", "addon_price": "<PERSON><PERSON>", "close": "Close", "update_variant": "Update <PERSON><PERSON><PERSON>", "update_addon": "Update Addon", "final_price_note": "*Entered price = Final price, overrides base price.", "addon_price_note": "*Final price of Item = Base Price + Entered Price, adds to base price in POS.", "confirm_delete": "Are you sure! This process is irreversible!", "provide_title_error": "Please provide title!", "provide_valid_price_error": "Please provide valid price!", "provide_variant_title_error": "Please provide variant title!", "provide_valid_variant_price_error": "Please provide valid variant price!", "provide_addon_title_error": "Please provide addon title!", "provide_valid_addon_price_error": "Please provide valid addon price!", "upload_image_error": "We're getting issue while processing your request, Please try later!", "remove_image_confirm": "Are you sure! This operation is irreversible!", "none": "None", "price_increase": "Price Increase", "add_new_variant": "Add New Variant", "add_new_addon": "Add New Addon", "enter_variant_title": "En<PERSON> Variant Title", "enter_variant_price": "<PERSON><PERSON>", "enter_addon_title": "En<PERSON> Title", "enter_addon_price": "<PERSON><PERSON>"}, "superadmin_login": {"title": "SuperAdmin", "login": "<PERSON><PERSON>", "email_label": "Email", "email_placeholder": "Enter Your email here...", "password_label": "Password", "password_placeholder": "Enter Your Password here...", "login_button": "<PERSON><PERSON>", "loading_message": "Please wait...", "username_error": "Please provide username!", "password_error": "Please provide password!", "success_message": "Login successful!", "error_message": "Something went wrong!"}, "superadmin_contact_support": {"title": "Contact Support", "need_help": "Need Help?", "description": "Facing any issues, Custom Feature, New App Development, New Project, connect with us we will respond in 24 hrs. Mail: <b><u>{{email}}</u></b>", "send_mail": "Send mail!", "copy": "Copy", "copied_to_clipboard": "Copied to clipboard!", "or_open_with": "or open with...", "default_mail_app": "Default Mail App", "gmail_in_browser": "GMail in Browser"}, "superadmin_dashboard": {"title": "Dashboard", "please_wait": "Please wait...", "error_loading": "Error Loading details! Please try later!", "active_tenants": "Active Tenants", "mrr": "MRR", "arr": "ARR", "store_sales": "Store Sales Volume Today", "store_sales_info": "*The amount shown is converted to USD; the final amount may vary.", "orders_processed": "Orders Processed Today", "view_more": "View more in reports"}, "superadmin_reports": {"title": "Reports", "filters": {"today": "Today", "yesterday": "Yesterday", "last_7days": "Last 7 Days", "this_month": "This Month", "last_month": "Last Month", "custom": "Custom Date Range"}, "loading_message": "Please wait...", "error_message": "Error Loading Reports Data, Please try later!", "showing_data_for": "Showing Data for {{filter}}", "top_selling_items": "Top Selling Items", "no_bestsellers": "No Top Selling items found for Selected Filter!", "active_tenants": "Active Tenants", "mrr": "MRR", "arr": "ARR", "store_sales_volume": "Store Sales Volume", "store_sales_info": "*The amount shown is converted to USD; the final amount may vary.", "orders_processed": "Orders Processed", "all_tenants_customers": "All Tenant's Customers", "filter": "Filter", "from": "From", "to": "To", "close": "Close", "apply": "Apply"}, "superadmin_tenant_subscription_history": {"tenant_details": "Tenant Details", "tenant_name": "Tenant Name", "status": "Status", "active": "Active", "inactive": "InActive", "users": "Users", "address": "Address", "email": "Email", "phone": "Phone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "qr_menu": "QR Menu", "enabled": "Enabled", "disabled": "Disabled", "payment_history": "Payment History", "subscription_price": "{{price}}/month", "cancelled": "Cancelled", "created": "Created", "renewed": "Renewed", "cancelled_on": "Cancelled on : ", "paid_on": "Paid on: ", "billing_period": "Billing Period: ", "tenants": "Tenants", "subscription_history": "Subscription History"}, "superadmin_tenants": {"title": "Tenants", "add_tenant": "Add Tenant", "active": "Active", "inactive": "Inactive", "all": "All", "search_placeholder": "Search", "export_active_tenants": "Export Active Tenants", "export_inactive_tenants": "Export Inactive Tenants", "export_all_tenants": "Export All Tenants", "no_tenants_found": "No Tenants Found!", "tenant_details": "Tenant Details", "status": "Status", "subscription_start": "Subscription Start", "subscription_end": "Subscription End", "plan": "Plan", "actions": "Actions", "showing": "Showing", "of": "of", "filter": "Filter", "from": "From", "to": "To", "apply": "Apply", "close": "Close", "add_new_tenant": "Add New Tenant", "tenant_name": "Tenant Name", "required": "- (Required)", "email": "Email", "password": "Password", "active_label": "Active", "save": "Save", "update_tenant": "Update Tenant", "enter_tenant_name": "Enter Tenant Name here...", "enter_email": "Enter Email here...", "enter_password": "Enter Password here...", "enter_tenant_email": "Enter Tenant's Email", "enter_tenant_name_placeholder": "Enter Tenant's Name", "are_you_sure": "Are you sure ?", "irreversible_action": "This action is irreversible", "warning": "Warning", "permanently_deleted": "All accounts associated with this tenant will be permanently deleted", "cancel": "Cancel", "yes_delete": "Yes, Delete", "please_provide_name": "Please provide name!", "please_provide_email": "Please provide Email!", "please_provide_password": "Please provide password!", "please_provide_valid_email": "Please provide valid email!", "error_loading_tenants_data": "Error loading Tenants Data! Please try later!", "data_exported_successfully": "Data exported successfully!", "failed_to_export_data": "Failed to export Data. Please try again.", "something_went_wrong": "Something went wrong!", "please_wait": "Please wait...", "month": "month"}, "pos": {"title": "POS - Point of Sale", "new_order": "New", "qr_menu_orders": "QR Menu Orders", "drafts_list": "Drafts List", "table_orders": "Table Orders", "search_customer": "Search Customer", "walkin_customer": "WALKIN CUSTOMER", "select_dining_option": "Select Dining Option", "dinein": "<PERSON><PERSON>-<PERSON>", "delivery": "Delivery", "takeaway": "Take Away", "select_table": "Select Table", "cart_is_empty": "Cart is empty.", "draft": "Draft", "send_to_kitchen": "Send to Kitchen", "create_receipt_pay": "Create Receipt & Pay", "add_notes": "Add Notes", "notes": "Notes", "save": "Save", "close": "Close", "apply": "Apply", "filters": "Filters", "select_category": "Select Category", "select_variant_addons": "Select Variant & Addons", "add": "Add", "save_cart_to_drafts": "Save Cart Items to Drafts", "reference": "Reference", "drafts": "Drafts", "ref": "Ref", "cart_items": "Cart Items", "send_order_to_kitchen": "Send Order to Kitchen", "items_net_total": "Items Net Total", "tax_total": "Tax Total", "service_charge_total": "Service Charge Total", "payable_total": "Payable Total", "collect_payment_send_to_kitchen": "Collect Payment & Send order to Kitchen", "order_sent_to_kitchen": "Order sent to Kitchen!", "order_id": "Order ID", "print_token": "Print Token", "qr_orders": "QR Orders", "table": "Table", "customer": "Customer", "cart_items_count": "{{count}} Cart Items", "search_customer_placeholder": "Enter Customer Phone here...", "type_to_find": "Type something to find...", "error_loading": "Error loading details! Please try later!", "something_went_wrong": "Something went wrong!", "please_wait": "Please wait...", "error_processing_request": "Error processing your request! Please try later!", "no_result_found": "No result found!", "invalid_request": "Invalid request!", "delete_confirm": "Are you sure? This process is irreversible!", "loading_message": "Loading...", "error_message": "Error processing your request, Please try later!", "cart": "<PERSON><PERSON>", "items": "Items", "quantity": "Quantity", "price": "Price", "total": "Total", "variant": "<PERSON><PERSON><PERSON>", "addons": "Addons", "notes_placeholder": "Enter Notes here...", "enter_reference_name": "Enter Reference Name here...", "please_provide_phone": "Please provide phone number to search!", "error_getting_details": "Error getting details! please try later!", "please_provide_valid_phone": "Please provide valid phone number!", "no_options_message": "Type something to find...", "add_customer": "Add Customer", "customer_name": "Customer Name", "customer_phone": "Customer Phone", "customer_email": "Customer <PERSON><PERSON>", "customer_birthdate": "Customer Birthdate", "customer_remarks": "Customer Remarks", "add_customer_success": "Customer added successfully!", "add_customer_error": "Error adding customer! Please try later!", "clear": "Clear", "confirm": "Confirm", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "loading": "Loading...", "getting_issues": "We're getting issue while clearing orders! Please try later!", "empty_cart": "Cart is empty!", "all": "All", "variants": "Variants", "save_cart_items_to_drafts": "Save cart items to draft", "collect_payment_send_order_to_kitchen": "Collect Payment & Send to Kitchen", "not_found_img_alt": "Not found", "person": "person", "100_max_characters": "100 character max."}, "superadmin_navbar": {"dashboard": "Dashboard", "tenants": "Tenants", "reports": "Reports"}, "inventory": {"loading_text": "Please wait...", "error_loading_text": "Error loading details! Please try later!", "toast_item_name_required": "Item Name is required", "toast_quantity_positive_required": "Current Stock Quantity must be a positive number", "toast_unit_required": "Please select a unit", "toast_min_quantity_positive_required": "Minimum Stock Quantity must be a positive number", "toast_min_quantity_less_than_stock": "Minimum Stock Quantity must be less than the current stock quantity", "toast_invalid_request": "Invalid Request!", "toast_select_movement_type": "Please select a movement type", "toast_invalid_quantity": "Please enter a valid quantity.", "toast_default_error": "Something went wrong!", "title": "Inventory Stock", "dashboard": "Dashboard", "search_placeholder": "Search items...", "add_stock_item": "Add Stock Items", "stock_movements": "Stock Movements", "status_all": "All Stock Items", "status_in": "Available Items", "status_low": "Low Stock Items", "status_out": "Out of Stock Items", "no_items_message": "Oops! No inventory stock items found!", "no_items_image_alt": "img", "table_item": "<PERSON><PERSON>", "table_current_stock": "Current Stock", "table_min_stock": "<PERSON>", "table_stock_status": "Stock Status", "table_updated_on": "Updated On", "table_actions": "Actions", "stock_status_in": "In Stock", "stock_status_low": "Low Stock", "stock_status_out": "Out of Stock", "dropdown_add_stock_movement": "Add Stock Movement", "dropdown_update_item": "Update Item", "dropdown_delete_item": "Delete Item", "modal_add_title": "Add Stock Item", "modal_close": "Close", "form_item_name_label": "Item Name", "form_item_name_placeholder": "Item Name", "form_item_unit_label": "Unit", "form_item_unit_pc": "Piece (pc)", "form_item_unit_kg": "Kilogram (kg)", "form_item_unit_g": "Gram (g)", "form_item_unit_l": "Liter (L)", "form_item_unit_ml": "Milliliter (mL)", "form_item_qty_label": "Current Stock Qty", "form_item_qty_placeholder": "Enter Current Stock Qty...", "form_item_min_qty_label": "<PERSON>", "form_item_min_qty_placeholder": "Enter Min Stock Qty...", "form_save_button": "Save", "modal_update_title": "Update Item", "form_update_item_name_label": "Item Name", "form_update_unit_label": "Unit", "form_update_qty_label": "Current Stock Qty", "form_update_qty_placeholder": "Enter Current Stock Qty...", "form_update_min_qty_label": "<PERSON>", "form_update_min_qty_placeholder": "Enter Min Stock Qty...", "modal_add_stock_movement_title": "Add New Stock Movement", "modal_add_stock_movement_type": "Movement Type", "form_add_stock_qty_label": "Quantity", "form_add_stock_qty_placeholder": "Enter Quantity to add...", "form_add_stock_notes_label": "Remarks (optional)", "form_add_stock_notes_placeholder": "Shipment details, manual adjustment reason etc...", "modal_delete_title": "Delete Item", "modal_delete_confirm": "Are you sure you want to delete this item? This action is irreversible. 🛑✋", "modal_delete_cancel": "Cancel", "modal_delete_confirm_button": "Yes, Delete!", "breadcrumbs_inventory": "Inventory", "breadcrumbs_movements": "Movements", "page_title": "Stock Movements", "filters_button": "Filters", "filters_dialog_title": "Filter", "filters_filter_label": "Filter", "filters_from_date_label": "From", "filters_to_date_label": "To", "filters_apply_button": "Apply", "filters_close_button": "Close", "filters_option_today": "Today", "filters_option_yesterday": "Yesterday", "filters_option_last_7_days": "Last 7 Days", "filters_option_this_month": "This Month", "filters_option_last_month": "Last Month", "filters_option_custom": "Custom Date Range", "export_button": "Export", "export_success_message": "Inventory logs exported successfully!", "export_error_message": "Failed to export Data. Please try again.", "export_no_data_message": "No data to export!", "loading_message": "Please wait...", "error_loading_message": "Error loading details! Please try later!", "showing_data_for": "Showing Data for", "stock_movements_all": "All Stock Movements", "stock_movements_in": "Stock In", "stock_movements_out": "Stock Out", "stock_movements_wastage": "Stock Wastage", "no_stock_movements_message": "No stock movements found!", "no_stock_movements_image_alt": "No Inventory", "table_header_item": "<PERSON><PERSON>", "table_header_quantity": "Quantity", "table_header_movement": "Movement", "table_header_date_time": "Date & Time", "table_header_remarks": "Remarks", "table_header_updated_by": "Updated By", "movement_type_in": "IN", "movement_type_out": "OUT", "movement_type_wastage": "WASTAGE", "default_no_remarks": "-", "default_no_updated_by": "-", "default_no_date": "N/A", "breadcrumbs_dashboard": "Dashboard", "page_title_dashboard": "Inventory Dashboard", "cummulative_inventory_movements": "Cummulative Inventory Movements", "usage_vs_current_stock": "Usage VS Current Stock", "table_header_number": "#", "table_header_in": "IN", "table_header_out": "OUT", "table_header_wastage": "WASTAGE", "table_header_used": "USED", "table_header_current_stock": "CURRENT STOCK", "table_header_min_qty": "MIN QTY", "table_header_status": "STATUS", "status_in_stock": "In Stock", "status_low_stock": "Low Stock", "status_out_of_stock": "Out of Stock", "no_inventory_movements_message": "No inventory movements found", "no_usage_vs_stock_message": "No usage vs stock data found", "no_data_image_alt": "No data", "insufficient_stock_message_pos": "Insufficient stock: One or more ingredients are missing. Please restock or select a different item.", "low_stock": "Low Stock", "qty": "Qty"}}