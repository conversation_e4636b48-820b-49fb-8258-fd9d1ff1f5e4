import ApiClient from "../helpers/ApiClient";
import useSWR from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

export function useReports({ type, from = null, to = null }) {
  // Tarih ve saat bilgilerini içeren parametreleri kontrol et
  let fromParam = from;
  let toParam = to;

  // Eğer tarih parametreleri ISO formatında ise (tarih ve saat içeriyorsa) do<PERSON><PERSON><PERSON> kullan
  // Aksi takdirde null olarak bırak

  const APIURL = `/reports?type=${type}${fromParam ? `&from=${fromParam}` : ''}${toParam ? `&to=${toParam}` : ''}`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}