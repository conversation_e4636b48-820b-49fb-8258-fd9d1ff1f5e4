import { API_URL } from "../config/config";
import { getAuthToken } from "../helpers/UserDetails";

// İptal Nedenleri (Cancellation Reasons)
export const getCancellationReasons = async () => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/cancellation`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İptal nedenleri alınamadı");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İptal nedenleri alınırken hata oluştu:", error);
    throw error;
  }
};

export const getCancellationReasonById = async (id) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/cancellation/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İptal nedeni alınamadı");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İptal nedeni alınırken hata oluştu:", error);
    throw error;
  }
};

export const addCancellationReason = async (reasonData) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/cancellation`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(reasonData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İptal nedeni eklenemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İptal nedeni eklenirken hata oluştu:", error);
    throw error;
  }
};

export const updateCancellationReason = async (id, reasonData) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/cancellation/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(reasonData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İptal nedeni güncellenemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İptal nedeni güncellenirken hata oluştu:", error);
    throw error;
  }
};

export const deleteCancellationReason = async (id) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/cancellation/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İptal nedeni silinemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İptal nedeni silinirken hata oluştu:", error);
    throw error;
  }
};

// İkram Nedenleri (Complimentary Reasons)
export const getComplimentaryReasons = async () => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/complimentary`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İkram nedenleri alınamadı");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İkram nedenleri alınırken hata oluştu:", error);
    throw error;
  }
};

export const getComplimentaryReasonById = async (id) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/complimentary/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İkram nedeni alınamadı");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İkram nedeni alınırken hata oluştu:", error);
    throw error;
  }
};

export const addComplimentaryReason = async (reasonData) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/complimentary`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(reasonData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İkram nedeni eklenemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İkram nedeni eklenirken hata oluştu:", error);
    throw error;
  }
};

export const updateComplimentaryReason = async (id, reasonData) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/complimentary/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(reasonData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İkram nedeni güncellenemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İkram nedeni güncellenirken hata oluştu:", error);
    throw error;
  }
};

export const deleteComplimentaryReason = async (id) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/complimentary/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "İkram nedeni silinemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("İkram nedeni silinirken hata oluştu:", error);
    throw error;
  }
};

// Fire Nedenleri (Waste Reasons)
export const getWasteReasons = async () => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/waste`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Fire nedenleri alınamadı");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fire nedenleri alınırken hata oluştu:", error);
    throw error;
  }
};

export const getWasteReasonById = async (id) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/waste/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Fire nedeni alınamadı");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fire nedeni alınırken hata oluştu:", error);
    throw error;
  }
};

export const addWasteReason = async (reasonData) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/waste`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(reasonData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Fire nedeni eklenemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fire nedeni eklenirken hata oluştu:", error);
    throw error;
  }
};

export const updateWasteReason = async (id, reasonData) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/waste/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(reasonData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Fire nedeni güncellenemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fire nedeni güncellenirken hata oluştu:", error);
    throw error;
  }
};

export const deleteWasteReason = async (id) => {
  try {
    const token = getAuthToken();
    const response = await fetch(`${API_URL}/reasons/waste/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Fire nedeni silinemedi");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fire nedeni silinirken hata oluştu:", error);
    throw error;
  }
};
