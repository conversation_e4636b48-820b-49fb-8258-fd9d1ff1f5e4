import React, { useRef, useState, useEffect } from "react";
import Page from "../../components/Page";
import { IconClock, IconCheck, IconTrash, IconAlertCircle } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { useBusinessHours, updateBusinessHours, deleteBusinessHours } from "../../controllers/business-hours.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";

export default function BusinessHoursPage() {
  const dayStartTimeRef = useRef();
  const dayEndTimeRef = useRef();
  const isOvernightRef = useRef();

  const { APIURL, data: businessHours, error, isLoading } = useBusinessHours();

  const [previewData, setPreviewData] = useState({
    dayStartTime: "",
    dayEndTime: "",
    isOvernight: false
  });

  // Form verilerini güncelle
  useEffect(() => {
    if (businessHours) {
      const startTime = businessHours.day_start_time ? businessHours.day_start_time.substring(0, 5) : "";
      const endTime = businessHours.day_end_time ? businessHours.day_end_time.substring(0, 5) : "";
      
      setPreviewData({
        dayStartTime: startTime,
        dayEndTime: endTime,
        isOvernight: businessHours.is_overnight || false
      });

      // Ref'leri güncelle
      if (dayStartTimeRef.current) dayStartTimeRef.current.value = startTime;
      if (dayEndTimeRef.current) dayEndTimeRef.current.value = endTime;
      if (isOvernightRef.current) isOvernightRef.current.checked = businessHours.is_overnight || false;
    }
  }, [businessHours]);

  // Form değişikliklerini takip et
  const handleFormChange = () => {
    const startTime = dayStartTimeRef.current?.value || "";
    const endTime = dayEndTimeRef.current?.value || "";
    const overnight = isOvernightRef.current?.checked || false;

    setPreviewData({
      dayStartTime: startTime,
      dayEndTime: endTime,
      isOvernight: overnight
    });
  };

  if (isLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }

  if (error) {
    console.error(error);
    return <Page className="px-8 py-6">Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!</Page>;
  }

  const btnSaveBusinessHours = async () => {
    const dayStartTime = dayStartTimeRef.current.value;
    const dayEndTime = dayEndTimeRef.current.value;
    const isOvernight = isOvernightRef.current.checked;

    if (!dayStartTime || !dayEndTime) {
      toast.error("Başlangıç ve bitiş saatleri boş olamaz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateBusinessHours(dayStartTime, dayEndTime, isOvernight);

      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message || "Çalışma saatleri başarıyla güncellendi!");
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const btnDeleteBusinessHours = async () => {
    if (!confirm("Çalışma saatlerini silmek istediğinizden emin misiniz?")) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteBusinessHours();

      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message || "Çalışma saatleri başarıyla silindi!");
        
        // Form'u temizle
        dayStartTimeRef.current.value = "";
        dayEndTimeRef.current.value = "";
        isOvernightRef.current.checked = false;
        setPreviewData({ dayStartTime: "", dayEndTime: "", isOvernight: false });
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  // Örnek senaryolar
  const getExampleScenario = () => {
    if (!previewData.dayStartTime || !previewData.dayEndTime) {
      return "Lütfen başlangıç ve bitiş saatlerini girin";
    }

    if (previewData.isOvernight) {
      return `Örnek: ${previewData.dayStartTime} - ${previewData.dayEndTime} (Ertesi gün) - Gece yarısını geçen işletme`;
    } else {
      return `Örnek: ${previewData.dayStartTime} - ${previewData.dayEndTime} - Normal işletme saatleri`;
    }
  };

  return (
    <Page className="px-8 py-6">
      <div className="flex items-center gap-2 mb-6">
        <IconClock stroke={iconStroke} />
        <h1 className="text-2xl font-bold">İşletme Çalışma Saatleri</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sol Taraf - Form */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Çalışma Saatleri Ayarları</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gün Başlangıç Saati
              </label>
              <input
                type="time"
                ref={dayStartTimeRef}
                onChange={handleFormChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-restro-border-green-light"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gün Bitiş Saati
              </label>
              <input
                type="time"
                ref={dayEndTimeRef}
                onChange={handleFormChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-restro-border-green-light"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                ref={isOvernightRef}
                onChange={handleFormChange}
                className="h-4 w-4 text-restro-green focus:ring-restro-border-green-light border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Gece yarısını geçen işletme (örn: 16:00 - 05:00)
              </label>
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <button
              onClick={btnSaveBusinessHours}
              className="flex items-center gap-2 bg-restro-green text-white px-4 py-2 rounded-lg hover:bg-restro-green-dark transition"
            >
              <IconCheck size={18} stroke={iconStroke} />
              Kaydet
            </button>

            {businessHours && (
              <button
                onClick={btnDeleteBusinessHours}
                className="flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition"
              >
                <IconTrash size={18} stroke={iconStroke} />
                Sil
              </button>
            )}
          </div>
        </div>

        {/* Sağ Taraf - Önizleme ve Bilgi */}
        <div className="space-y-6">
          {/* Mevcut Ayarlar */}
          {businessHours && (
            <div className="bg-green-50 rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-green-800 mb-3">Mevcut Ayarlar</h3>
              <div className="space-y-2 text-sm">
                <p><strong>Başlangıç:</strong> {businessHours.day_start_time}</p>
                <p><strong>Bitiş:</strong> {businessHours.day_end_time}</p>
                <p><strong>Gece Yarısını Geçiyor:</strong> {businessHours.is_overnight ? "Evet" : "Hayır"}</p>
                <p><strong>Durum:</strong> {businessHours.is_active ? "Aktif" : "Pasif"}</p>
              </div>
            </div>
          )}

          {/* Önizleme */}
          <div className="bg-blue-50 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">Önizleme</h3>
            <p className="text-sm text-blue-700">{getExampleScenario()}</p>
          </div>

          {/* Bilgilendirme */}
          <div className="bg-yellow-50 rounded-lg shadow p-6">
            <div className="flex items-start gap-2">
              <IconAlertCircle size={20} className="text-yellow-600 mt-0.5" />
              <div>
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">Önemli Bilgiler</h3>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Normal işletme: 08:00 - 23:59 gibi</li>
                  <li>• Gece yarısını geçen: 16:00 - 05:00 gibi</li>
                  <li>• 24 saat açık için: 00:00 - 23:59 ve "Gece yarısını geçen" işaretli</li>
                  <li>• Bu ayarlar sistem genelinde kullanılacaktır</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Page>
  );
}
