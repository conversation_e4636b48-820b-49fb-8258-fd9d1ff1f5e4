import React, { useRef, useEffect } from "react";
import Page from "../../components/Page";
import { IconPencil, IconPlus, IconTrash, IconUpload } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { addCategory, deleteCategory, updateCategory, useCategories, uploadCategoryPhoto, usePrinters } from "../../controllers/settings.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";
import { getImageURL } from "../../helpers/ImageHelper";
import imageCompression from "browser-image-compression";

export default function CategoriesPage() {
  // Refs for Add Modal
  const categoryTitleRef = useRef();
  const categoryParentRef = useRef();

  // URL parametrelerini kontrol et
  useEffect(() => {
    // URL'den action parametresini al
    const queryParams = new URLSearchParams(window.location.search);
    const action = queryParams.get('action');

    // localStorage'dan modal açma bilgisini kontrol et
    const storedAction = localStorage.getItem('openModal');

    // Eğer action=add parametresi varsa veya localStorage'da kayıtlıysa, modalı aç
    if (action === 'add' || storedAction === 'add') {
      // Modalı aç
      document.getElementById('add-category-modal').showModal();

      // localStorage'dan temizle
      localStorage.removeItem('openModal');

      // URL'den parametreyi temizle (opsiyonel)
      if (action === 'add') {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }, []);

  // Refs for Update Modal
  const categoryIdRef = useRef();
  const categoryTitleUpdateRef = useRef();
  const categoryParentUpdateRef = useRef();
  const categoryPrinterUpdateRef = useRef();

  const handleFileChange = async (e, categoryId) => {
    const file = e.target.files[0];
    if (!file) return;
    try {
      toast.loading("Lütfen bekleyin...");
      const compressedImage = await imageCompression(file, {
        maxSizeMB: 0.5,
        maxWidthOrHeight: 512,
        useWebWorker: true,
      });
      const formData = new FormData();
      formData.append("image", compressedImage);
      const res = await uploadCategoryPhoto(categoryId, formData);
      if (res.status === 200) {
        toast.dismiss();
        toast.success(res.data.message);
        await mutate(APIURL);
        location.reload();
      }
    } catch (error) {
      console.error("Resim yükleme hatası:", error);
      toast.dismiss();
      const message =
        error?.response?.data?.message ||
        "Bir şeyler ters gitti, lütfen tekrar deneyin!";
      toast.error(message);
    }
  };

  const { APIURL, data: categories, error, isLoading } = useCategories();
  const { data: printers } = usePrinters();

  if (isLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }
  if (error) {
    console.error(error);
    return (
      <Page className="px-8 py-6">
        Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!
      </Page>
    );
  }

  async function btnAdd() {
    const title = categoryTitleRef.current.value;
    // Parent kategori seçimi (opsiyonel)
    const parentId = categoryParentRef.current.value;
    if (!title) {
      toast.error("Lütfen Kategori Başlığını belirtin!");
      return;
    }
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await addCategory(title, parentId);
      if (res.status === 200) {
        categoryTitleRef.current.value = "";
        categoryParentRef.current.value = "";
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message =
        error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  }

  const btnShowUpdate = async (id, title, printerId, parentId) => {
    categoryIdRef.current.value = id;
    categoryTitleUpdateRef.current.value = title;
    categoryPrinterUpdateRef.current.value = printerId || "";
    categoryParentUpdateRef.current.value = parentId || "";
    document.getElementById("modal-update").showModal();
  };

  const btnUpdate = async () => {
    const id = categoryIdRef.current.value;
    const title = categoryTitleUpdateRef.current.value;
    const printerId = categoryPrinterUpdateRef.current.value;
    const parentId = categoryParentUpdateRef.current.value;
    if (!title) {
      toast.error("Lütfen başlık belirtin!");
      return;
    }
    try {
      toast.loading("Lütfen bekleyin...");
      const payload = { title, printerId, parent_id: parentId };
      const res = await updateCategory(id, payload);
      if (res.status === 200) {
        categoryIdRef.current.value = null;
        categoryTitleUpdateRef.current.value = "";
        categoryParentUpdateRef.current.value = "";
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message =
        error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const btnDelete = async (id) => {
    const isConfirm = window.confirm(
      "Emin misin! Bu süreç geri döndürülemez!"
    );
    if (!isConfirm) return;
    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteCategory(id);
      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message =
        error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <Page className="px-8 py-6">
      <div className="flex items-center gap-6">
        <h3 className="text-3xl font-light">Kategoriler</h3>
        <button
          onClick={() => document.getElementById("modal-add").showModal()}
          className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
        >
          <IconPlus size={22} stroke={iconStroke} /> Yeni
        </button>
      </div>

      <div className="mt-8 w-full">
        <table className="w-full border overflow-x-auto">
          <thead>
            <tr>
              <th className="px-3 py-2 bg-gray-100 font-medium text-gray-500 text-start w-20">
                #
              </th>
              <th className="px-3 py-2 bg-gray-100 font-medium text-gray-500 text-start w-32">
                Resim
              </th>
              <th className="px-3 py-2 bg-gray-100 font-medium text-gray-500 text-start w-96">
                Başlık
              </th>
              <th className="px-3 py-2 bg-gray-100 font-medium text-gray-500 text-start w-28">
                Eylemler
              </th>
            </tr>
          </thead>
          <tbody>
            {categories && categories.length > 0 ? (
              categories.map((category, index) => {
                return (
                  <tr key={index}>
                    <td className="px-3 py-2 text-start">{index + 1}</td>
                    <td className="px-3 py-2 text-start">
                      {category.cat_image ? (
                        <img
                          src={getImageURL(category.cat_image)}
                          alt={category.title}
                          className="w-12 h-12 object-cover rounded-lg"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 flex items-center justify-center rounded-lg">
                          <span className="text-gray-500">📷</span>
                        </div>
                      )}
                    </td>
                    <td className="px-3 py-2 text-start">
                      {category.title}
                      {category.parent_title && (
                        <span className="ml-2 text-xs text-gray-500">
                          (Üst: {category.parent_title})
                        </span>
                      )}
                    </td>
                    <td className="px-3 py-2 text-start flex gap-2 items-center">
                      <button
                        onClick={() =>
                          btnShowUpdate(
                            category.id,
                            category.title,
                            category.printer ? category.printer.id : "",
                            category.parent_id
                          )
                        }
                        className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-100 transition active:scale-95"
                      >
                        <IconPencil stroke={iconStroke} />
                      </button>
                      <button
                        onClick={() => btnDelete(category.id)}
                        className="w-8 h-8 rounded-full flex items-center justify-center text-red-500 hover:bg-gray-100 transition active:scale-95"
                      >
                        <IconTrash stroke={iconStroke} />
                      </button>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan="4" className="text-center py-4 text-gray-500">
                  Kategori bulunamadı
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Add Category Modal */}
      <dialog id="modal-add" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Kategori Ekle</h3>
          <div className="my-4">
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">
              Kategori Başlık
            </label>
            <input
              ref={categoryTitleRef}
              type="text"
              name="title"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50"
              placeholder="Kategori adını giriniz..."
            />
          </div>
          <div className="my-4">
            <label htmlFor="parent" className="mb-1 block text-gray-500 text-sm">
              Üst Kategori (Opsiyonel)
            </label>
            <select
              ref={categoryParentRef}
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50"
            >
              <option value="">Üst kategori seçilmemiş</option>
              {categories &&
                categories.map((cat) => (
                  <option key={cat.id} value={cat.id}>
                    {cat.title}
                  </option>
                ))}
            </select>
          </div>
          <div className="modal-action">
            <form method="dialog">
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                onClick={() => {
                  btnAdd();
                }}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 px-4 py-3 bg-restro-green text-white ml-3"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>

      {/* Update Category Modal */}
      <dialog id="modal-update" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Kategoriyi Güncelle</h3>
          <div className="my-4 relative">
            {/* Upload image options */}
            <div className="absolute bottom-2 md:bottom-auto md:top-4 md:right-4 flex items-center gap-2">
              <label
                htmlFor="file"
                className="flex items-center justify-center w-9 h-9 rounded-full bg-white shadow hover:bg-slate-100 cursor-pointer transition active:scale-95"
              >
                <IconUpload stroke={iconStroke} size={18} />
                <input
                  onChange={(e) => handleFileChange(e, categoryIdRef.current.value)}
                  type="file"
                  name="file"
                  id="file"
                  className="hidden"
                  accept="image/*"
                />
              </label>
            </div>
            {/* Hidden ID */}
            <input type="hidden" ref={categoryIdRef} />
            <label htmlFor="title" className="mb-1 block text-gray-500 text-sm">
              Kategori Başlık
            </label>
            <input
              ref={categoryTitleUpdateRef}
              type="text"
              name="title"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50"
              placeholder="Kategori adını giriniz..."
            />
            <div className="my-4">
              <label htmlFor="parent" className="mb-1 block text-gray-500 text-sm">
                Üst Kategori (Opsiyonel)
              </label>
              <select
                ref={categoryParentUpdateRef}
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50"
              >
                <option value="">Üst kategori seçilmemiş</option>
                {categories &&
                  categories.map((cat) => (
                    <option key={cat.id} value={cat.id}>
                      {cat.title}
                    </option>
                  ))}
              </select>
            </div>
            <div className="my-4">
              <label htmlFor="printer" className="mb-1 block text-gray-500 text-sm">
                Yazıcı Seç
              </label>
              <select
                ref={categoryPrinterUpdateRef}
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50"
              >
                <option value="">Yazıcı Seç (Opsiyonel)</option>
                {printers &&
                  printers.map((printer) => (
                    <option key={printer.id} value={printer.id}>
                      {printer.name}
                    </option>
                  ))}
              </select>
            </div>
          </div>
          <div className="modal-action">
            <form method="dialog">
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                onClick={() => {
                  btnUpdate();
                }}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 px-4 py-3 bg-restro-green text-white ml-3"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>
    </Page>
  );
}
