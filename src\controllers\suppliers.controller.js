import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

// Tedarikçiler (Suppliers)
export function useSuppliers() {
  const APIURL = `/suppliers`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export function useSupplier(id) {
  const APIURL = `/suppliers/${id}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export async function addSupplier(name, address, phone, email, contactPerson) {
  try {
    const response = await ApiClient.post("/suppliers/add", {
      name,
      address,
      phone,
      email,
      contactPerson
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateSupplier(id, name, address, phone, email, contactPerson, isActive) {
  try {
    const response = await ApiClient.post(`/suppliers/update/${id}`, {
      name,
      address,
      phone,
      email,
      contactPerson,
      isActive
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteSupplier(id) {
  try {
    const response = await ApiClient.delete(`/suppliers/delete/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

export default {
  useSuppliers,
  useSupplier,
  addSupplier,
  updateSupplier,
  deleteSupplier
};