import React, { useEffect, useState } from "react";
import { IconChevronLeft, IconCategory } from "@tabler/icons-react";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";

export default function CategoryPage() {
  const { t, i18n } = useTranslation(['category']);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("category");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        const currency = CURRENCIES.find(
          (c) => c.cc === data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          currency: currency?.symbol || "",
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, categories } = state;
  const storeName = storeSettings?.store_name || "";

  const bodyStyles = {
    backgroundColor: storeSettings?.background_color || "#f3f4f6",
    color: storeSettings?.text_color || "#000000",
  };

  const headerStyles = {
    backgroundColor: storeSettings?.header_color || "#047857",
    color: storeSettings?.header_text_color || "#ffffff",
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="container mx-auto px-4 flex h-screen items-center justify-center">
          Lütfen bekleyin...
        </div>
      </div>
    );
  }

  return (
    <div className="w-full" style={bodyStyles}>
      <header className="w-full py-4 sticky top-0 z-50" style={headerStyles}>
        <div className="container mx-auto px-4 flex items-center justify-between">
          <IconChevronLeft
            size={28}
            stroke={3}
            className="cursor-pointer"
            onClick={() => navigate(-1)}
          />
          <div className="flex items-center">
            {storeSettings?.store_image ? (
              <img
                src={getImageURL(storeSettings.store_image)}
                alt={storeName}
                className="h-12 object-contain"
              />
            ) : (
              <h1 className="text-xl font-bold">{storeName}</h1>
            )}
          </div>
          <div className="flex items-center w-6"></div>
        </div>
      </header>

      <div className="container mx-auto px-4">
        <div className="text-center py-6">
          <h2 className="text-2xl font-bold mb-4">{t("categories")}</h2>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pb-8">
          {categories.map((category) => {
            const { id, name, image } = category;
            const imageURL = getImageURL(image);

            return (
              <div
                key={id}
                className="bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => {
                  navigate(
                    encryptedTableId
                      ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                      : `/m/${qrcode}/menu`,
                    {
                      state: { selectedCategory: id },
                    }
                  );
                }}
              >
                <div className="flex flex-col items-center text-center space-y-3">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    {image ? (
                      <img
                        src={imageURL}
                        alt={name}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <IconCategory size={32} className="text-gray-500" />
                    )}
                  </div>
                  <h3 className="text-sm font-medium text-gray-800">
                    {t(`category:${id}`, { defaultValue: name })}
                  </h3>
                </div>
              </div>
            );
          })}

          <div
            className="bg-green-600 rounded-xl p-4 shadow-md hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => {
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                  : `/m/${qrcode}/menu`,
                {
                  state: { selectedCategory: "all" },
                }
              );
            }}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center">
                <IconCategory size={32} className="text-white" />
              </div>
              <h3 className="text-sm font-medium text-white">
                {t("allCategories")}
              </h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
