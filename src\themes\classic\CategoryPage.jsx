import {
  IconChevronLeft,
  IconMenuDeep,
  IconPhoto,
  IconInfoCircle,
  IconX,
  IconLanguage,
} from "@tabler/icons-react";
import React, { useEffect, useState } from "react";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";


export default function CategoryPage() {
  const { t, i18n } = useTranslation(["translation", "category"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [isInfoModalOpen, setInfoModalOpen] = useState(false);
  const [isLanguageModalOpen, setLanguageModalOpen] = useState(false);
  const languages = [
    { code: "tr", label: "Türkçe" },
    { code: "en", label: "English" },
    { code: "fr", label: "Français" },
    { code: "de", label: "Deutsch" },
  ];

  const [currentParent, setCurrentParent] = useState(null);

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("category");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        const currency = CURRENCIES.find(
          (c) => c.cc === data?.storeSettings?.currency
        );
        // tenant_id'yi localStorage'a kaydet (örnek)
        if (data?.storeSettings?.tenant_id) {
          window.localStorage.setItem("tenant_id", data.storeSettings.tenant_id);
        }
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          currency: currency?.symbol || "",
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - Kategoriler - SewPOS`;
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const { isLoading, storeSettings, categories, currency } = state;
  const storeName = storeSettings?.store_name || "";

  const bodyStyles = {
    backgroundColor: storeSettings?.background_color || "#f3f4f6",
    color: storeSettings?.text_color || "#000000",
  };
  const headerStyles = {
    backgroundColor: storeSettings?.header_color || "#047857",
    color: storeSettings?.header_text_color || "#ffffff",
  };
  const footerStyles = {
    backgroundColor: storeSettings?.footer_background_color || "#111827",
    color: storeSettings?.footer_text_color || "#ffffff",
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="container mx-auto px-4 flex h-screen items-center justify-center">
          {t("loading")}
        </div>
      </div>
    );
  }

  const filteredCategories = categories.filter((cat) =>
    currentParent === null ? cat.parent_id === null : cat.parent_id === currentParent
  );

  const handleCategoryClick = (category) => {
    const hasChildren = categories.some((cat) => cat.parent_id === category.id);
    if (hasChildren) {
      setCurrentParent(category.id);
    } else {
      const url = encryptedTableId
        ? `/m/${qrcode}/menu?table=${encryptedTableId}`
        : `/m/${qrcode}/menu`;
      navigate(url, {
        state: {
          selectedCategory: category.id,
          selectedCategoryTitle: t(category.id, { ns: "category", defaultValue: category.title }),
        },
      });
    }
  };

  const handleBack = () => {
    setCurrentParent(null);
  };

  return (
    <div className="w-full" style={bodyStyles}>
      <header className="w-full py-4 sticky top-0 z-50" style={headerStyles}>
        <div className="container mx-auto px-4 flex items-center justify-between">
          <IconChevronLeft
            size={28}
            stroke={3}
            className="cursor-pointer"
            onClick={() => {
              if (currentParent !== null) {
                handleBack();
              } else {
                navigate(-1);
              }
            }}
          />
          <div className="flex items-center">
            {storeSettings?.store_image ? (
              <img
                src={getImageURL(storeSettings.store_image)}
                alt={storeName}
                className="h-10 object-contain"
              />
            ) : (
              <h1 className="text-xl font-bold">{storeName}</h1>
            )}
          </div>
          <div className="flex items-center w-6"> </div>
        </div>
      </header>

      <div className="container mx-auto px-4">
        <div className="flex flex-wrap justify-center gap-4 pb-24 pt-6">
          {filteredCategories.length > 0 ? (
            filteredCategories.map((category, index) => {
              const isFullWidth = index % 3 === 0;
              return (
                <div
                  key={category.id}
                  className={`relative rounded overflow-hidden shadow-md border border-gray-200 cursor-pointer ${
                    isFullWidth ? "w-full" : "w-[calc(50%-0.5rem)]"
                  }`}
                  onClick={() => handleCategoryClick(category)}
                >
                  {category.cat_image ? (
                    <img
                      src={getImageURL(category.cat_image)}
                      alt={category.title}
                      className="w-full h-48 object-cover"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gray-100 flex items-center justify-center">
                      <IconPhoto size={48} stroke={1.5} className="text-gray-400" />
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 w-full bg-black bg-opacity-50 text-white text-center py-2">
                    <p className="text-md font-semibold">
                      {t(category.id, { ns: "category", defaultValue: category.title })}
                    </p>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="text-center py-8 text-gray-500">
              {t("noSubcategories")}
            </div>
          )}
        </div>
      </div>

      <div
        className="fixed bottom-0 left-0 w-full py-4 shadow-lg flex justify-between items-center px-6"
        style={footerStyles}
      >
        <button
          className="hover:text-gray-400 transition"
          onClick={() => setInfoModalOpen(true)}
        >
          <IconInfoCircle size={28} />
        </button>
        <button
          className="hover:text-gray-400 transition"
          onClick={() => setLanguageModalOpen(true)}
        >
          <IconLanguage size={28} />
        </button>
      </div>

      {isInfoModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50"
          onClick={() => setInfoModalOpen(false)}
        >
          <div
            className="bg-white rounded-xl w-96 p-8 shadow-2xl relative animate-fade-in"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-500 hover:text-red-500 transition duration-200"
              onClick={() => setInfoModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-800 mb-4">{storeName}</h2>
              <p className="text-gray-600 text-sm mb-6">
                {storeSettings.address && (
                  <>
                    <span className="block font-medium text-gray-700">{t("address")}:</span>
                    {storeSettings.address}
                  </>
                )}
                {storeSettings.phone && (
                  <>
                    <span className="block font-medium text-gray-700 mt-4">{t("phone")}:</span>
                    {storeSettings.phone}
                  </>
                )}
                {storeSettings.email && (
                  <>
                    <span className="block font-medium text-gray-700 mt-4">{t("email")}:</span>
                    {storeSettings.email}
                  </>
                )}
              </p>
            </div>
          </div>
        </div>
      )}

      {isLanguageModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
          onClick={() => setLanguageModalOpen(false)}
        >
          <div
            className="bg-white rounded-lg w-96 p-6 shadow-xl relative"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-800 transition"
              onClick={() => setLanguageModalOpen(false)}
            >
              <IconX size={24} />
            </button>
            <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">
              {t("languageSelection")}
            </h2>
            <div className="flex justify-center">
              <select
                className="text-sm border border-gray-300 rounded-lg py-2 px-4 text-gray-700"
                value={i18n.language}
                onChange={(e) => {
                  const selectedLanguage = e.target.value;
                  i18n.changeLanguage(selectedLanguage);
                  setLanguageModalOpen(false);
                }}
              >
                {languages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}