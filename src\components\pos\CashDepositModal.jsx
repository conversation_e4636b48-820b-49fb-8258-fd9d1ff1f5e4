import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { IconCashPlus, IconX, IconAlertTriangle } from '@tabler/icons-react';
import { makeCashDeposit } from '../../controllers/cash-register.controller';

const CashDepositModal = ({ paymentTypes, onSuccess }) => {
  const [amount, setAmount] = useState('');
  const [reason, setReason] = useState('');
  const [paymentTypeId, setPaymentTypeId] = useState('');
  const [loading, setLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validasyon
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      toast.error('Lütfen geçerli bir miktar girin.');
      return;
    }

    if (!reason.trim()) {
      toast.error('<PERSON>ütfen bir sebep girin.');
      return;
    }

    if (!paymentTypeId) {
      toast.error('Lütfen bir ödeme türü seçin.');
      return;
    }

    // Onay diyaloğunu göster
    setShowConfirmation(true);
  };

  // Para girişini gerçekleştir
  const confirmDeposit = async () => {
    setLoading(true);

    try {
      const depositData = {
        amount: parseFloat(amount),
        reason: reason.trim(),
        payment_type_id: parseInt(paymentTypeId)
      };

      const response = await makeCashDeposit(depositData);

      if (response.data.success) {
        toast.success(response.data.message || 'Para girişi başarıyla kaydedildi.');

        // Formu sıfırla
        setAmount('');
        setReason('');
        setShowConfirmation(false);

        // Modal'ı kapat
        document.getElementById('modal-cash-deposit').close();

        // Başarı callback'ini çağır
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast.error(response.data.message || 'Para girişi kaydedilirken bir hata oluştu.');
      }
    } catch (error) {
      console.error('Para girişi hatası:', error);
      toast.error(error.response?.data?.message || 'Para girişi kaydedilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <dialog id="modal-cash-deposit" className="modal modal-bottom sm:modal-middle">
      <div className="modal-box">
        <div className="flex items-center justify-between">
          <h3 className="font-bold text-lg flex items-center">
            <IconCashPlus className="mr-2" size={20} />
            Kasaya Para Girişi
          </h3>
          <form method="dialog">
            <button
              className="btn btn-circle btn-sm bg-red-50 hover:bg-red-100 text-red-500 border-none"
              onClick={() => setShowConfirmation(false)}
            >
              <IconX size={18} />
            </button>
          </form>
        </div>

        {showConfirmation ? (
          <div className="mt-4">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
              <div className="flex items-start">
                <IconAlertTriangle className="text-green-500 mr-3 mt-0.5" size={24} />
                <div>
                  <h4 className="font-bold text-green-800">İşlemi Onaylayın</h4>
                  <p className="text-green-700 mt-1">
                    {parseFloat(amount).toFixed(2)} TL tutarında para girişi yapmak istediğinize emin misiniz?
                  </p>
                  <p className="text-green-700 mt-1">
                    <strong>Sebep:</strong> {reason}
                  </p>
                  <p className="text-green-700 mt-1">
                    <strong>Ödeme Türü:</strong> {paymentTypes.find(type => type.id === parseInt(paymentTypeId))?.title || 'Bilinmiyor'}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <button
                type="button"
                onClick={() => setShowConfirmation(false)}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none"
              >
                İptal
              </button>
              <button
                type="button"
                onClick={confirmDeposit}
                disabled={loading}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none disabled:bg-green-400"
              >
                {loading ? 'İşleniyor...' : 'Onaylıyorum, Para Girişi Yap'}
              </button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="mt-4">
            <div className="mb-4">
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                Miktar
              </label>
              <input
                type="number"
                id="amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0.00"
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
              Sebep <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Para girişi sebebi"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="paymentType" className="block text-sm font-medium text-gray-700 mb-1">
              Ödeme Türü <span className="text-red-500">*</span>
            </label>
            <select
              id="paymentType"
              value={paymentTypeId}
              onChange={(e) => setPaymentTypeId(e.target.value)}
              className={`w-full px-3 py-2 border ${!paymentTypeId ? 'border-red-300 bg-red-50' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500`}
              required
            >
              <option value="">Ödeme türü seçin</option>
              {paymentTypes && paymentTypes.map((type) => (
                <option key={type.id} value={type.id}>
                  {type.title}
                </option>
              ))}
            </select>
            {!paymentTypeId && (
              <p className="mt-1 text-sm text-red-500">Lütfen bir ödeme türü seçin</p>
            )}
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              onClick={() => document.getElementById('modal-cash-deposit').close()}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none"
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none disabled:bg-green-400"
            >
              {loading ? 'İşleniyor...' : 'Para Girişi Yap'}
            </button>
          </div>
        </form>
        )}
      </div>
    </dialog>
  );
};

export default CashDepositModal;
