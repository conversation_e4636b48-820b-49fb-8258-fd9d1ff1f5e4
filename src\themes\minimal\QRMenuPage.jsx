import React, { useEffect, useState } from "react";
import {
  getQRMenuInit,
  sendFeedback,
} from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import {
  IconCategory,
  IconInfoCircle,
  IconLanguage,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandInstagram,
  IconBrandWhatsapp,
  IconArrowRight,
  IconDots,
} from "@tabler/icons-react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    campaigns: null,
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("dynamic");
  }, [qrcode, i18n.language]);

  // Sayfa başlığını güncelle
  useEffect(() => {
    if (storeSettings?.store_name) {
      document.title = `${storeSettings.store_name} - Menu - SewPOS`;
    }
  }, [storeSettings]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          campaigns: data.campaigns,
          storeSettings: data?.storeSettings,
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, campaigns } = state;
  const storeName = storeSettings?.store_name || t("storeName");
  const slides = storeSettings?.slides?.split(",") || [];

  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [slides.length]);

  if (isLoading) {
    return <QRMenuLoading />;
  }

  return (
    <div className="w-full min-h-screen bg-white font-mono">
      {/* MINIMAL HEADER */}
      <header className="bg-black text-white">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-white"></div>
              {storeSettings?.store_image ? (
                <img
                  src={getImageURL(storeSettings.store_image)}
                  alt={storeName}
                  className="h-8 object-contain filter invert"
                />
              ) : (
                <h1 className="text-2xl font-bold uppercase tracking-widest">{storeName}</h1>
              )}
            </div>
            <div className="text-sm uppercase tracking-wider">
              {new Date().toLocaleDateString('tr-TR')}
            </div>
          </div>
        </div>
      </header>

      {/* MINIMAL CONTENT */}
      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Main Image/Slider */}
        {slides.length > 0 ? (
          <div className="mb-16">
            <div className="relative border border-gray-300">
              <img
                src={getImageURL(slides[currentSlide])}
                alt={`Slide ${currentSlide + 1}`}
                className="w-full h-64 object-cover grayscale"
              />
              <div className="absolute bottom-4 left-4 bg-black text-white px-3 py-1 text-xs uppercase tracking-wider">
                {currentSlide + 1} / {slides.length}
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-16 h-64 border border-gray-300 flex items-center justify-center">
            <p className="text-gray-500 uppercase tracking-wider text-sm">NO IMAGE</p>
          </div>
        )}

        {/* Navigation Menu */}
        <div className="space-y-1 mb-16">
          <div className="border-b border-gray-300 pb-4 mb-8">
            <h2 className="text-3xl font-bold uppercase tracking-widest">MENU</h2>
          </div>

          <button
            onClick={() =>
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/category?table=${encryptedTableId}`
                  : `/m/${qrcode}/category`
              )
            }
            className="group w-full border-b border-gray-100 hover:border-black transition-colors py-6 flex items-center justify-between"
          >
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-black group-hover:w-4 transition-all duration-300"></div>
              <span className="text-xl font-bold uppercase tracking-wide group-hover:tracking-widest transition-all">
                {t("menuButton", { defaultValue: "VIEW MENU" })}
              </span>
            </div>
            <IconArrowRight size={20} className="group-hover:translate-x-1 transition-transform" />
          </button>

          <button className="group w-full border-b border-gray-100 hover:border-black transition-colors py-6 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-black group-hover:w-4 transition-all duration-300"></div>
              <span className="text-xl font-bold uppercase tracking-wide group-hover:tracking-widest transition-all">
                {t("about", { defaultValue: "ABOUT" })}
              </span>
            </div>
            <IconArrowRight size={20} className="group-hover:translate-x-1 transition-transform" />
          </button>

          <button className="group w-full border-b border-gray-100 hover:border-black transition-colors py-6 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-black group-hover:w-4 transition-all duration-300"></div>
              <span className="text-xl font-bold uppercase tracking-wide group-hover:tracking-widest transition-all">
                {t("contact", { defaultValue: "CONTACT" })}
              </span>
            </div>
            <IconArrowRight size={20} className="group-hover:translate-x-1 transition-transform" />
          </button>
        </div>

        {/* Social Links */}
        {(storeSettings?.facebook || storeSettings?.twitter || storeSettings?.instagram || storeSettings?.whatsapp) && (
          <div className="border-t border-gray-300 pt-8">
            <h3 className="text-sm uppercase tracking-widest mb-6 text-gray-500">SOCIAL</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {storeSettings?.facebook && (
                <a
                  href={storeSettings.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group border border-gray-300 hover:border-black hover:bg-black hover:text-white transition-all p-4 flex items-center justify-center"
                >
                  <IconBrandFacebook size={20} />
                </a>
              )}
              {storeSettings?.twitter && (
                <a
                  href={storeSettings.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group border border-gray-300 hover:border-black hover:bg-black hover:text-white transition-all p-4 flex items-center justify-center"
                >
                  <IconBrandTwitter size={20} />
                </a>
              )}
              {storeSettings?.instagram && (
                <a
                  href={storeSettings.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group border border-gray-300 hover:border-black hover:bg-black hover:text-white transition-all p-4 flex items-center justify-center"
                >
                  <IconBrandInstagram size={20} />
                </a>
              )}
              {storeSettings?.whatsapp && (
                <a
                  href={storeSettings.whatsapp}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group border border-gray-300 hover:border-black hover:bg-black hover:text-white transition-all p-4 flex items-center justify-center"
                >
                  <IconBrandWhatsapp size={20} />
                </a>
              )}
            </div>
          </div>
        )}

        {/* Campaigns */}
        {campaigns && campaigns.length > 0 && (
          <div className="border-t border-gray-300 pt-8 mt-8">
            <h3 className="text-sm uppercase tracking-widest mb-6 text-gray-500">CAMPAIGNS</h3>
            <div className="space-y-4">
              {campaigns.slice(0, 3).map((campaign, index) => (
                <div key={campaign.id} className="border-b border-gray-100 pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-bold uppercase tracking-wide">{campaign.campaign_name}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {new Date(campaign.start_date).toLocaleDateString("tr-TR")} - {new Date(campaign.end_date).toLocaleDateString("tr-TR")}
                      </p>
                    </div>
                    <div className="text-lg font-light">
                      {String(index + 1).padStart(2, '0')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* MINIMAL FOOTER */}
      <footer className="bg-black text-white mt-16">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <IconDots size={20} />
              <span className="text-sm uppercase tracking-wider">DIGITAL MENU</span>
            </div>
            <div className="flex items-center space-x-6">
              <button className="hover:bg-white hover:text-black transition-colors p-2">
                <IconInfoCircle size={20} />
              </button>
              <button className="hover:bg-white hover:text-black transition-colors p-2">
                <IconLanguage size={20} />
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
