import React, { useState } from 'react';
import { IconX, IconDiscount, IconPercentage } from '@tabler/icons-react';
import { toast } from 'react-hot-toast';
import { iconStroke } from '../../../config/config';
import { applyDiscount } from '../../../controllers/orders.controller';

const DiscountModal = ({ orderId, onSuccess, remainingAmount = 0, currency = 'TL' }) => {
  const [discountValue, setDiscountValue] = useState('0');
  const [isApplying, setIsApplying] = useState(false);

  // Yüzde indirim butonları
  const percentageButtons = [5, 10, 15, 25, 30];

  // Yüzde indirim hesaplama fonksiyonu
  const calculatePercentageDiscount = (percentage) => {
    if (remainingAmount <= 0) {
      toast.error("İndirim uygulanacak tutar bulunamadı!");
      return;
    }

    // Yüzde indirim tutarını hesapla
    const discountAmount = (remainingAmount * percentage) / 100;

    // İndirim tutarını 2 ondalık basamağa yuvarla
    const roundedDiscount = Math.round(discountAmount * 100) / 100;

    // İndirim tutarını input alanına yaz
    setDiscountValue(roundedDiscount.toFixed(2));
  };

  const handleApplyDiscount = async () => {
    const value = parseFloat(discountValue);
    if (isNaN(value) || value <= 0) {
      toast.error("Geçerli bir indirim tutarı girin!");
      return;
    }

    // Eğer zaten indirim uygulama işlemi devam ediyorsa, işlemi engelle
    if (isApplying) {
      return;
    }

    // İndirim uygulama işlemi başladı
    setIsApplying(true);

    try {
      toast.loading("İndirim uygulanıyor...");

      // İndirim uygula
      await applyDiscount({
        order_id: orderId,
        order_item_id: null, // Sadece tüm siparişe indirim uygula
        discount_type: 'amount', // Sadece tutar indirimi uygula
        discount_value: value
      });

      // İndirim başarılı olduktan sonra siparişi tekrar yükle
      if (onSuccess) {
        await onSuccess();
      }

      toast.dismiss();
      toast.success("İndirim başarıyla uygulandı!");

      // İndirim değerini sıfırla
      setDiscountValue('0');

      // Modal'ı kapat
      document.getElementById('modal-discount').close();
    } catch (error) {
      toast.dismiss();
      toast.error("İndirim uygulanırken bir hata oluştu!");
      console.error("İndirim hatası:", error);
    } finally {
      // İndirim uygulama işlemi bitti
      setIsApplying(false);
    }
  };

  return (
    <dialog id="modal-discount" className="modal modal-bottom sm:modal-middle">
      <div className="modal-box max-w-2xl w-full">
        <div className="flex items-center justify-between">
          <h3 className="font-bold text-xl flex items-center">
            <IconDiscount className="mr-2" size={24} />
            İndirim Uygula
          </h3>
          <form method="dialog">
            <button className="btn btn-circle bg-red-50 hover:bg-red-100 text-red-500 border-none">
              <IconX size={20} />
            </button>
          </form>
        </div>

        <div className="mt-4">
          {/* Kalan Tutar Bilgisi */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="font-medium text-lg">Kalan Tutar:</span>
              <span className="font-bold text-blue-600 text-xl">
                {remainingAmount.toFixed(2)} {currency}
              </span>
            </div>
          </div>

          {/* Yüzde İndirim Butonları */}
          <div className="mb-6">
            <label className="block text-lg font-medium text-gray-700 mb-3">
              Yüzde İndirim
            </label>
            <div className="grid grid-cols-5 gap-3">
              {percentageButtons.map((percentage) => (
                <button
                  key={percentage}
                  type="button"
                  onClick={() => calculatePercentageDiscount(percentage)}
                  className="flex items-center justify-center px-4 py-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-lg font-medium"
                >
                  <IconPercentage size={20} className="mr-2" />
                  {percentage}%
                </button>
              ))}
            </div>
          </div>

          {/* İndirim Tutarı Input */}
          <div className="mb-6">
            <label htmlFor="discountValue" className="block text-lg font-medium text-gray-700 mb-2">
              İndirim Tutarı <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="discountValue"
              value={discountValue}
              onChange={(e) => setDiscountValue(e.target.value)}
              placeholder="0.00"
              step="0.01"
              min="0"
              className="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
              required
            />
          </div>

          <div className="flex justify-end gap-3 mt-8">
            <button
              type="button"
              onClick={() => document.getElementById('modal-discount').close()}
              className="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 focus:outline-none text-lg font-medium"
            >
              İptal
            </button>
            <button
              type="button"
              onClick={handleApplyDiscount}
              disabled={isApplying || !discountValue || parseFloat(discountValue) <= 0}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none disabled:bg-green-400 text-lg font-medium"
            >
              {isApplying ? 'Uygulanıyor...' : 'Tutar İndirimi Uygula'}
            </button>
          </div>
        </div>
      </div>
    </dialog>
  );
};

export default DiscountModal;
