import React, { useState, useEffect } from 'react';
import { IconX, IconCash, IconCashRegister, IconArrowUp, IconArrowDown, IconCalculator } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { toast } from "react-hot-toast";
import { getUserDetailsInLocalStorage } from "../../helpers/UserDetails";
import {
  useCashRegisters,
  useUserActiveSession,
  openCashRegisterSession,
  closeCashRegisterSession
} from "../../controllers/cash-register.controller";

const CashRegisterSessionModal = ({ onSessionChange, maliModeActive }) => {
  const [selectedRegisterId, setSelectedRegisterId] = useState("");
  const [openingNotes, setOpeningNotes] = useState("");
  const [closingAmount, setClosingAmount] = useState("");
  const [closingNotes, setClosingNotes] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showTransactions, setShowTransactions] = useState(false);
  const [showCashCalculator, setShowCashCalculator] = useState(false);
  const [cashInHand, setCashInHand] = useState("");
  const [calculationResult, setCalculationResult] = useState(null);

  const { data: cashRegisters, error: registersError } = useCashRegisters();
  const { data: activeSession, error: sessionError, mutate: mutateSession } = useUserActiveSession();

  const user = getUserDetailsInLocalStorage();
  const currency = user?.currency || "₺";

  // Handle open session
  const handleOpenSession = async (e) => {
    e.preventDefault();

    if (!selectedRegisterId) {
      toast.error("Lütfen bir kasa seçin!");
      return;
    }

    // Açılış tutarı default olarak 0
    const defaultOpeningAmount = 0;

    try {
      setIsLoading(true);
      toast.loading("Kasa oturumu açılıyor...");

      const response = await openCashRegisterSession({
        cash_register_id: selectedRegisterId,
        opening_amount: defaultOpeningAmount,
        opening_notes: openingNotes
      });

      toast.dismiss();

      if (response.status === 201) {
        toast.success("Kasa oturumu başarıyla açıldı!");
        mutateSession();

        // Clear form
        setSelectedRegisterId("");
        setOpeningNotes("");

        // Close modal
        document.getElementById("modal-cash-register-session").close();

        // Notify parent component
        if (onSessionChange) {
          onSessionChange();
        }
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || "Kasa oturumu açılırken bir hata oluştu!");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle close session
  const handleCloseSession = async (e) => {
    e.preventDefault();

    if (!activeSession) {
      toast.error("Aktif bir kasa oturumu bulunamadı!");
      return;
    }

    if (!closingAmount || isNaN(parseFloat(closingAmount)) || parseFloat(closingAmount) < 0) {
      toast.error("Geçerli bir kapanış tutarı giriniz!");
      return;
    }

    try {
      setIsLoading(true);
      toast.loading("Kasa oturumu kapatılıyor...");

      const response = await closeCashRegisterSession(activeSession.id, {
        closing_amount: parseFloat(closingAmount),
        closing_notes: closingNotes
      });

      toast.dismiss();

      if (response.status === 200) {
        toast.success("Kasa oturumu başarıyla kapatıldı!");
        mutateSession();

        // Clear form
        setClosingAmount("");
        setClosingNotes("");

        // Close modal
        document.getElementById("modal-cash-register-session").close();

        // Notify parent component
        if (onSessionChange) {
          onSessionChange();
        }
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || "Kasa oturumu kapatılırken bir hata oluştu!");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Set expected amount as default closing amount
  useEffect(() => {
    if (activeSession) {
      const openingAmount = parseFloat(activeSession.opening_amount) || 0;
      const totalTransactions = parseFloat(activeSession.total_transactions) || 0;
      const expectedAmount = openingAmount + totalTransactions;
      setClosingAmount(expectedAmount.toFixed(2));
    }
  }, [activeSession]);

  // Nakit hesaplama fonksiyonu
  const calculateCash = () => {
    if (!cashInHand || isNaN(parseFloat(cashInHand))) {
      toast.error("Lütfen kasadaki nakit tutarını giriniz!");
      return;
    }

    const withdrawals = parseFloat(activeSession.total_withdrawals || 0);
    const deposits = parseFloat(activeSession.total_deposits || 0);
    const openingAmount = parseFloat(activeSession.opening_amount || 0);
    const cashAmount = parseFloat(cashInHand);

    // Formül: Para Çıkış - Para Giriş - Açılış Tutarı + Kasadaki Nakit
    const result = withdrawals - deposits - openingAmount + cashAmount;

    return {
      withdrawals,
      deposits,
      openingAmount,
      cashAmount,
      result
    };
  };

  return (
    <dialog id="modal-cash-register-session" className="modal">
      <div className="modal-box w-11/12 max-w-5xl">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-bold text-lg">
            {activeSession ? "Kasa Oturumu Kapat" : "Kasa Oturumu Aç"}
          </h3>
          <form method="dialog">
            <button className="btn btn-sm btn-circle btn-ghost">
              <IconX size={18} stroke={iconStroke} />
            </button>
          </form>
        </div>

        {registersError || sessionError ? (
          <div className="alert alert-error">
            <p>Veriler yüklenirken bir hata oluştu. Lütfen sayfayı yenileyip tekrar deneyin.</p>
          </div>
        ) : (
          <>
            {activeSession ? (
              // Close Session Form
              <form onSubmit={handleCloseSession}>

                {/* Mali Mode - Ödeme Türü Toplamları */}
                {maliModeActive && activeSession?.payment_totals_by_type && activeSession.payment_totals_by_type.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-semibold mb-3">Kasadaki Nakit ve Kredi Kartı Toplamları</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {activeSession.payment_totals_by_type.map((payment, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4 border">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div>
                                <p className="font-medium text-sm">{payment.payment_type_name}</p>
                                <p className="text-xs text-gray-500">
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-lg">{parseFloat(payment.total_amount).toFixed(2)} {currency}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Para Giriş/Çıkış İşlemleri - Mali mode aktifken gizle */}
                {!maliModeActive && activeSession?.transactions && activeSession.transactions.length > 0 && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">Para Giriş/Çıkış İşlemleri</h4>
                      <button
                        type="button"
                        className="text-sm text-blue-600 hover:underline"
                        onClick={() => setShowTransactions(!showTransactions)}
                      >
                        {showTransactions ? "Gizle" : "Göster"}
                      </button>
                    </div>

                            {showTransactions && (
                      <div className="space-y-4">
                        {/* Toplam Özet */}
                        <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <IconArrowUp size={20} className="text-green-600" />
                              <span className="font-semibold text-green-800">Toplam Para Girişi</span>
                            </div>
                            <div className="text-xl font-bold text-green-600">
                              {parseFloat(activeSession.total_deposits || 0).toFixed(2)} {currency}
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <IconArrowDown size={20} className="text-red-600" />
                              <span className="font-semibold text-red-800">Toplam Para Çıkışı</span>
                            </div>
                            <div className="text-xl font-bold text-red-600">
                              {parseFloat(activeSession.total_withdrawals || 0).toFixed(2)} {currency}
                            </div>
                          </div>
                        </div>

                        {/* İşlemler Tablosu */}
                        <div className="overflow-x-auto max-h-60 overflow-y-auto border rounded-lg">
                          <table className="table table-compact w-full">
                            <thead>
                              <tr className="bg-gray-100">
                                <th className="text-left">İşlem</th>
                                <th className="text-left">Ödeme Tipi</th>
                                <th className="text-left">Tutar</th>
                                <th className="text-left">Not</th>
                                <th className="text-left">Tarih</th>
                              </tr>
                            </thead>
                            <tbody>
                              {activeSession.transactions.map((transaction, index) => (
                                <tr key={index} className="border-t hover:bg-gray-50">
                                  <td className="py-2 px-3">
                                    <div className="flex items-center">
                                      {transaction.transaction_type === "deposit" ? (
                                        <IconArrowUp size={16} className="text-green-600 mr-1" />
                                      ) : (
                                        <IconArrowDown size={16} className="text-red-600 mr-1" />
                                      )}
                                      {transaction.transaction_type_display}
                                    </div>
                                  </td>
                                  <td className="py-2 px-3">{transaction.payment_type}</td>
                                  <td className="py-2 px-3 font-medium">
                                    <span className={transaction.transaction_type === "deposit" ? "text-green-600" : "text-red-600"}>
                                      {transaction.transaction_type === "deposit" ? "+" : "-"}
                                      {parseFloat(transaction.amount).toFixed(2)} {currency}
                                    </span>
                                  </td>
                                  <td className="py-2 px-3 text-sm text-gray-600">{transaction.notes || "-"}</td>
                                  <td className="py-2 px-3 text-sm text-gray-600">
                                    {new Date(transaction.created_at).toLocaleString('tr-TR')}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>

                        {/* Nakitimi Hesapla Butonu */}
                        <div className="text-center">
                          <button
                            type="button"
                            onClick={() => setShowCashCalculator(true)}
                            className="btn btn-outline btn-primary flex items-center gap-2 mx-auto"
                          >
                            <IconCalculator size={18} stroke={iconStroke} />
                             Nakit Satışı Hesapla
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                <div className="form-control mb-3">
                  <label className="label">
                    <span className="label-text">Kapanış Tutarı* <span className="text-red-500">⚠️</span></span>
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={closingAmount}
                    onChange={(e) => setClosingAmount(e.target.value)}
                    placeholder="Kasadaki tutarı giriniz"
                    className="input input-bordered"
                    required
                    disabled={isLoading}
                  />
                  <div className="text-xs text-red-600 mt-1">
                    Gün sonu toplamları ve kasanızda kalan nakit para toplamı girin.
                  </div>
                </div>
                <div className="form-control mb-3">
                  <label className="label">
                    <span className="label-text">Kapanış Notu</span>
                  </label>
                  <textarea
                    value={closingNotes}
                    onChange={(e) => setClosingNotes(e.target.value)}
                    placeholder="Kapanış notu giriniz"
                    className="textarea textarea-bordered"
                    disabled={isLoading}
                  ></textarea>
                </div>
                <div className="modal-action">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isLoading}
                  >
                    {isLoading ? "İşleniyor..." : "Oturumu Kapat"}
                  </button>
                </div>
              </form>
            ) : (
              // Open Session Form
              <form onSubmit={handleOpenSession}>
                <div className="form-control mb-3">
                  <label className="label">
                    <span className="label-text">Kasa Seçin*</span>
                  </label>
                  <select
                    value={selectedRegisterId}
                    onChange={(e) => setSelectedRegisterId(e.target.value)}
                    className="select select-bordered w-full"
                    required
                    disabled={isLoading}
                  >
                    <option value="">Kasa seçin</option>
                    {cashRegisters &&
                      cashRegisters
                        .filter(register => register.is_active === 1 && register.has_active_session === 0)
                        .map((register) => (
                          <option key={register.id} value={register.id}>
                            {register.name}
                          </option>
                        ))}
                  </select>
                  {cashRegisters && cashRegisters.filter(register => register.is_active === 1 && register.has_active_session === 0).length === 0 && (
                    <p className="text-error text-sm mt-1">
                      Kullanılabilir kasa bulunamadı. Tüm kasalar kullanımda veya pasif durumda olabilir.
                    </p>
                  )}
                </div>

                <div className="form-control mb-3">
                  <label className="label">
                    <span className="label-text">Açılış Notu</span>
                  </label>
                  <textarea
                    value={openingNotes}
                    onChange={(e) => setOpeningNotes(e.target.value)}
                    placeholder="Açılış notu giriniz"
                    className="textarea textarea-bordered"
                    disabled={isLoading}
                  ></textarea>
                </div>
                <div className="modal-action">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isLoading || !cashRegisters || cashRegisters.filter(register => register.is_active === 1 && register.has_active_session === 0).length === 0}
                  >
                    {isLoading ? "İşleniyor..." : "Oturumu Aç"}
                  </button>
                </div>
              </form>
            )}
          </>
        )}
      </div>

      {/* Nakit Hesaplama Modal */}
      {showCashCalculator && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-11/12 max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-bold text-lg flex items-center gap-2">
                <IconCalculator size={20} stroke={iconStroke} />
                Nakit Hesaplama
              </h3>
              <button
                onClick={() => {
                  setShowCashCalculator(false);
                  setCashInHand("");
                  setCalculationResult(null);
                }}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <IconX size={18} stroke={iconStroke} />
              </button>
            </div>

            {/* Uyarı Mesajı */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-2 mb-4">
              <p className="text-xs text-red-700 text-center">
                ⚠️ Kasa para giriş ve çıkışlarınızın doğru olduğundan emin olunuz.
                Kasada kalan nakit kısmını devir veya çıkış yaptığnız tutar dışında elinizde kalan bozuk veya bu kasa içine koyduğnuuz tutar olacak.
              </p>
            </div>

            <div className="space-y-4">
              {/* Mevcut Veriler */}
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Para Çıkış Toplamı:</span>
                  <span className="font-medium text-red-600">
                    {parseFloat(activeSession?.total_withdrawals || 0).toFixed(2)} {currency}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Para Giriş Toplamı:</span>
                  <span className="font-medium text-green-600">
                    {parseFloat(activeSession?.total_deposits || 0).toFixed(2)} {currency}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Açılış Tutarı:</span>
                  <span className="font-medium text-blue-600">
                    {parseFloat(activeSession?.opening_amount || 0).toFixed(2)} {currency}
                  </span>
                </div>
              </div>

              {/* Kasadaki Nakit Girişi */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kasada Kalan Nakit (Kasada kalan para yok ise 0 giriniz.)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={cashInHand}
                  onChange={(e) => setCashInHand(e.target.value)}
                  placeholder="Kasadaki nakit tutarını giriniz"
                  className="input input-bordered w-full"
                />
              </div>

              {/* Hesaplama Butonu */}
              <button
                onClick={() => {
                  const calculation = calculateCash();
                  if (calculation) {
                    setCalculationResult(calculation);
                  }
                }}
                className="btn btn-primary w-full flex items-center gap-2"
                disabled={!cashInHand}
              >
                <IconCalculator size={18} stroke={iconStroke} />
                Hesapla
              </button>

              {/* Hesaplama Sonucu */}
              {calculationResult && (
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <h4 className="font-semibold text-blue-800 mb-2 text-center">Bu kasadakş nakit satış.</h4>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {calculationResult.result.toFixed(2)} {currency}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </dialog>
  );
};

export default CashRegisterSessionModal;
