import React from "react";
import jsPDF from "jspdf";
import "jspdf/dist/jspdf.es.min.js";
import { toast } from "react-hot-toast";

const ExportMenuPDF = ({ menuItems = [], categories = [] }) => {
  const handleExportPDF = () => {
    try {
      // Veri kontrolü
      console.log("Categories:", categories);
      console.log("MenuItems:", menuItems);

      if (!categories.length || !menuItems.length) {
        toast.error("Menü verileri bulunamadı!");
        return;
      }

      const pdf = new jsPDF();

      // Türkçe karakter desteği için Times font kullan
      try {
        pdf.setFont("times", "normal");
      } catch (e) {
        pdf.setFont("helvetica", "normal");
      }

      // PDF ayarları
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 15;
      const columnWidth = (pageWidth - (margin * 3)) / 2; // 2 sütun için
      const lineHeight = 6;
      let currentY = margin;
      let currentColumn = 0; // 0: sol sütun, 1: sağ sütun

      // Başlık
      pdf.setFontSize(16);
      pdf.setFont("OpenSans", "bold");
      pdf.text("MENÜ", pageWidth / 2, currentY, { align: "center" });
      currentY += lineHeight * 2;

      // Kategorilere göre grupla
      const groupedItems = {};
      categories.forEach(category => {
        if (!category || !category.id) return; // Geçersiz kategoriyi atla

        const categoryItems = menuItems.filter(item =>
          item && item.category_id === category.id
        );

        if (categoryItems.length > 0) {
          groupedItems[category.id] = {
            category: category,
            items: categoryItems
          };
        }
      });

      // Sütun pozisyonu hesaplama fonksiyonu
      const getColumnX = (column) => {
        return column === 0 ? margin : margin + columnWidth + margin;
      };

      // Yeni sütuna geç fonksiyonu
      const moveToNextColumn = () => {
        if (currentColumn === 0) {
          currentColumn = 1;
        } else {
          currentColumn = 0;
          currentY += lineHeight * 2; // Kategoriler arası boşluk
        }
      };

      // Sayfa kontrolü fonksiyonu
      const checkPageBreak = (neededSpace = 20) => {
        if (currentY > pageHeight - neededSpace) {
          pdf.addPage();
          currentY = margin;
          currentColumn = 0;
        }
      };

      // Her kategori için
      Object.values(groupedItems).forEach(({ category, items }) => {
        // Kategori için yer kontrolü
        checkPageBreak(30);

        const columnX = getColumnX(currentColumn);

        // Kategori başlığı
        pdf.setFontSize(12);
        pdf.setFont("OpenSans", "bold");
        const categoryName = category.name || category.title || `Kategori ${category.id}`;
        pdf.text(categoryName.toUpperCase(), columnX, currentY);

        // Kategori altı çizgi
        pdf.setLineWidth(0.3);
        pdf.line(columnX, currentY + 2, columnX + columnWidth, currentY + 2);
        currentY += lineHeight;

        // Ürünler
        pdf.setFontSize(9);
        pdf.setFont("OpenSans", "normal");

        items.forEach(item => {
          // Ürün için yer kontrolü
          if (currentY > pageHeight - 25) {
            pdf.addPage();
            currentY = margin;
            currentColumn = 0;
          }

          const itemColumnX = getColumnX(currentColumn);
          const itemName = item.title || item.name || `Ürün ${item.id}`;
          const itemPrice = item.price && parseFloat(item.price) > 0 ? `${item.price} ₺` : "";

          // Ürün adı (sol)
          pdf.text(itemName, itemColumnX, currentY);

          // Fiyat (sağ - hizalı)
          if (itemPrice) {
            pdf.text(itemPrice, itemColumnX + columnWidth - 5, currentY, { align: "right" });
          }

          currentY += lineHeight;
        });

        // Sonraki kategoriye geç
        moveToNextColumn();
      });

      // Footer
      const totalPages = pdf.internal.getNumberOfPages();
      for (let i = 1; i <= totalPages; i++) {
        pdf.setPage(i);
        pdf.setFontSize(8);
        pdf.setFont("OpenSans", "normal");
        pdf.text(
          `Sayfa ${i} / ${totalPages}`,
          pageWidth / 2,
          pageHeight - 10,
          { align: "center" }
        );
        pdf.text(
          `SewPOS - ${new Date().toLocaleDateString("tr-TR")}`,
          pageWidth - margin,
          pageHeight - 10,
          { align: "right" }
        );
      }

      // PDF'i indir
      pdf.save("Menu_SewPOS.pdf");
      toast.success("PDF menü başarıyla indirildi!");
      
    } catch (error) {
      console.error("PDF oluşturma hatası:", error);
      toast.error("PDF oluşturulurken bir hata oluştu!");
    }
  };

  return (
    <button
      onClick={handleExportPDF}
      className="btn bg-red-500 text-white hover:bg-red-600 px-4 py-2 rounded-lg"
    >
      PDF Menü İndir
    </button>
  );
};

export default ExportMenuPDF;
