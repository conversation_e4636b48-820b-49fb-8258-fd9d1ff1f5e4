import React from "react";
import jsPDF from "jspdf";
import { toast } from "react-hot-toast";

const ExportMenuPDF = ({ menuItems, categories }) => {
  const handleExportPDF = () => {
    try {
      const pdf = new jsPDF();
      
      // PDF ayarları
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const lineHeight = 8;
      let currentY = margin;

      // Başlık
      pdf.setFontSize(20);
      pdf.setFont("helvetica", "bold");
      pdf.text("MENÜ", pageWidth / 2, currentY, { align: "center" });
      currentY += lineHeight * 2;

      // Kategorilere göre grupla
      const groupedItems = {};
      categories.forEach(category => {
        const categoryItems = menuItems.filter(item => item.category_id === category.id);
        if (categoryItems.length > 0) {
          groupedItems[category.id] = {
            category: category,
            items: categoryItems
          };
        }
      });

      // Her kategori için
      Object.values(groupedItems).forEach(({ category, items }) => {
        // Sayfa kontrolü
        if (currentY > pageHeight - 60) {
          pdf.addPage();
          currentY = margin;
        }

        // Kategori başlığı
        pdf.setFontSize(16);
        pdf.setFont("helvetica", "bold");
        pdf.text(category.name.toUpperCase(), margin, currentY);
        currentY += lineHeight;

        // Kategori altı çizgi
        pdf.setLineWidth(0.5);
        pdf.line(margin, currentY, pageWidth - margin, currentY);
        currentY += lineHeight;

        // Ürünler
        pdf.setFontSize(11);
        items.forEach(item => {
          // Sayfa kontrolü
          if (currentY > pageHeight - 40) {
            pdf.addPage();
            currentY = margin;
          }

          // Ürün adı ve fiyat
          pdf.setFont("helvetica", "normal");
          const itemName = item.title;
          const itemPrice = item.price && parseFloat(item.price) > 0 ? `${item.price} ₺` : "";
          
          // Ürün adı (sol)
          pdf.text(itemName, margin, currentY);
          
          // Fiyat (sağ)
          if (itemPrice) {
            pdf.text(itemPrice, pageWidth - margin, currentY, { align: "right" });
          }
          
          currentY += lineHeight;

          // Ürün açıklaması (varsa)
          if (item.description) {
            pdf.setFont("helvetica", "italic");
            pdf.setFontSize(9);
            
            // Açıklamayı satırlara böl
            const descriptionLines = pdf.splitTextToSize(
              item.description, 
              pageWidth - (margin * 2)
            );
            
            descriptionLines.forEach(line => {
              if (currentY > pageHeight - 30) {
                pdf.addPage();
                currentY = margin;
              }
              pdf.text(line, margin + 5, currentY);
              currentY += 6;
            });
            
            pdf.setFontSize(11);
          }

          currentY += 3; // Ürünler arası boşluk
        });

        currentY += lineHeight; // Kategoriler arası boşluk
      });

      // Footer
      const totalPages = pdf.internal.getNumberOfPages();
      for (let i = 1; i <= totalPages; i++) {
        pdf.setPage(i);
        pdf.setFontSize(8);
        pdf.setFont("helvetica", "normal");
        pdf.text(
          `Sayfa ${i} / ${totalPages}`,
          pageWidth / 2,
          pageHeight - 10,
          { align: "center" }
        );
        pdf.text(
          `SewPOS - ${new Date().toLocaleDateString("tr-TR")}`,
          pageWidth - margin,
          pageHeight - 10,
          { align: "right" }
        );
      }

      // PDF'i indir
      pdf.save("Menu_SewPOS.pdf");
      toast.success("PDF menü başarıyla indirildi!");
      
    } catch (error) {
      console.error("PDF oluşturma hatası:", error);
      toast.error("PDF oluşturulurken bir hata oluştu!");
    }
  };

  return (
    <button
      onClick={handleExportPDF}
      className="btn bg-red-500 text-white hover:bg-red-600 px-4 py-2 rounded-lg"
    >
      PDF Menü İndir
    </button>
  );
};

export default ExportMenuPDF;
