import axios from "axios";
import { API } from "../config/config";

const CART_KEY = 'RESTROPROSAAS__CART';


export async function sendFeedback(qrcode, feedbackType, message) {
  try {
    const response = await axios.post(`${API}/qrmenu/${qrcode}/feedback`, {
      feedback_type: feedbackType,
      message: message,
    });
    return response.data;
  } catch (error) {
    console.error("Geri bildirim gönderilirken hata:", error);
    throw error;
  }
}

export async function getQRMenuInit(qrcode, tableId) {
    axios.defaults.withCredentials = true;
    try {
        const response = await axios.get(`${API}/qrmenu/${qrcode}?tableId=${tableId}`);
        return response;
    } catch (error) {
        throw error;
    }
}

export function getCart() {
    const cartString = localStorage.getItem(CART_KEY);
    const cart = cartString ? JSON.parse(cartString) : [];
    return cart;
}

export function setCart(cart) {
    localStorage.setItem(CART_KEY, JSON.stringify(cart));
}

export async function createOrderFromQrMenu(deliveryType , cartItems, customerType, customer, tableId , qrcode, floorId = null) {
    try {
        const response = await axios.post(`${API}/qrmenu/${qrcode}/place-order` , {
           deliveryType, cartItems, customerType, customer, tableId, floorId
        });
        return response;
    } catch (error) {
        throw error;
    }
}


/**
 * Dinamik Sorular ve Öneriler Al
 * @param {string} qrcode - QR kodu
 * @returns {Promise}
 */
export async function getDynamicRecommendations(qrcode) {
    axios.defaults.withCredentials = true;
    try {
      const response = await axios.get(`${API}/qrmenu/${qrcode}/dynamic-recommendations`);
      return response.data; // Sadece `data` döndür
    } catch (error) {
      console.error("Dinamik öneriler alınırken hata:", error);
      return { questions: [], recommendations: [] }; // Varsayılan boş yapı döndür
    }
  }

  /**
   * Müşteri Yanıtlarına Göre Öneriler Al
   * @param {string} qrcode - QR kodu
   * @param {Object} answers - Müşteri yanıtları
   * @returns {Promise}
   */
  export async function getRecommendationsForUser(qrcode, answers) {
    axios.defaults.withCredentials = true;
    try {
      const response = await axios.post(`${API}/qrmenu/${qrcode}/recommendations`, { answers });
      return response.data; // Sadece `data` döndür
    } catch (error) {
      console.error("Müşteri yanıtlarına göre öneriler alınırken hata:", error);
      return { recommendations: [] }; // Varsayılan boş yapı döndür
    }
  }
