import ApiClient from "../helpers/ApiClient";

export async function saveCall(tenantId, callData) {
    try {
        const response = await ApiClient.post(`/${tenantId}/calls`, callData);
        return response.data;
    } catch (error) {
        throw error;
    }
}

export async function getCallHistory() {
    try {
        const response = await ApiClient.get(`/caller/calls`);
        return response.data;
    } catch (error) {
        throw error;
    }
}
