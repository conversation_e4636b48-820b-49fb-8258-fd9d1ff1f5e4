/**
 * QR Template Helper Functions
 * Template sistemini QR menü sayfalarında uygulamak için yardımcı fonksiyonlar
 */

// Template'e göre CSS sınıfları oluştur
export const getTemplateClasses = (templateConfig) => {
  if (!templateConfig) return {};

  const layoutType = templateConfig.layout_type || 'classic';
  const cardStyle = templateConfig.card_style || 'standard';
  const buttonStyle = templateConfig.button_style || 'rounded';
  const spacing = templateConfig.spacing || 'normal';
  const imageStyle = templateConfig.image_style || 'square';
  const headerStyle = templateConfig.header_style || 'simple';
  const categoryStyle = templateConfig.category_style || 'tabs';

  return {
    container: `qr-template-${layoutType}`,
    card: `menu-card card-${cardStyle} spacing-${spacing}`,
    button: `menu-button button-${buttonStyle}`,
    image: `menu-image image-${imageStyle}`,
    header: `menu-header header-${headerStyle}`,
    category: `menu-category category-${categoryStyle}`,
    price: `menu-price price-${templateConfig.price_position || 'right'}`
  };
};

// Template'e göre inline stiller oluştur
export const getTemplateStyles = (templateConfig) => {
  if (!templateConfig) return {};

  const layoutType = templateConfig.layout_type || 'classic';
  
  // Layout tipine göre renk paleti
  const colorPalettes = {
    classic: {
      primary: '#333',
      secondary: '#666',
      accent: '#047857',
      background: '#ffffff'
    },
    modern: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#3b82f6',
      background: '#f8fafc'
    },
    minimal: {
      primary: '#000',
      secondary: '#999',
      accent: '#000',
      background: '#ffffff'
    },
    luxury: {
      primary: '#8b5a2b',
      secondary: '#d4af37',
      accent: '#b8860b',
      background: '#fefdf8'
    }
  };

  const palette = colorPalettes[layoutType] || colorPalettes.classic;

  return {
    container: {
      backgroundColor: palette.background,
      fontFamily: templateConfig.font_family === 'system' ? 'system-ui' : templateConfig.font_family
    },
    header: {
      backgroundColor: palette.primary,
      color: '#ffffff'
    },
    card: {
      backgroundColor: '#ffffff',
      borderColor: palette.secondary + '30',
      borderRadius: getBorderRadius(templateConfig.button_style)
    },
    button: {
      backgroundColor: palette.accent,
      color: '#ffffff',
      borderRadius: getBorderRadius(templateConfig.button_style)
    },
    price: {
      color: palette.accent,
      fontWeight: 'bold'
    }
  };
};

// Button style'a göre border radius
const getBorderRadius = (buttonStyle) => {
  switch (buttonStyle) {
    case 'rounded': return '8px';
    case 'pill': return '50px';
    case 'square': return '0px';
    case 'elegant': return '4px';
    default: return '8px';
  }
};

// Spacing değerine göre padding/margin
export const getSpacingValue = (spacing) => {
  switch (spacing) {
    case 'tight': return '8px';
    case 'normal': return '16px';
    case 'wide': return '24px';
    case 'generous': return '32px';
    default: return '16px';
  }
};

// Template'i DOM'a uygula
export const applyTemplateToDOM = (templateConfig) => {
  if (!templateConfig) return;

  const root = document.documentElement;
  const styles = getTemplateStyles(templateConfig);
  const classes = getTemplateClasses(templateConfig);

  // CSS değişkenlerini ayarla
  root.style.setProperty('--template-primary', styles.container.backgroundColor);
  root.style.setProperty('--template-header-bg', styles.header.backgroundColor);
  root.style.setProperty('--template-button-bg', styles.button.backgroundColor);
  root.style.setProperty('--template-border-radius', styles.button.borderRadius);
  root.style.setProperty('--template-spacing', getSpacingValue(templateConfig.spacing));

  // Body'ye template sınıfı ekle
  document.body.className = document.body.className.replace(/qr-template-\w+/g, '');
  document.body.classList.add(classes.container);

  // Custom CSS'i uygula
  if (templateConfig.custom_css) {
    let customStyleElement = document.getElementById('qr-template-custom-css');
    if (!customStyleElement) {
      customStyleElement = document.createElement('style');
      customStyleElement.id = 'qr-template-custom-css';
      document.head.appendChild(customStyleElement);
    }
    customStyleElement.textContent = templateConfig.custom_css;
  }
};

// Template'i temizle
export const clearTemplateFromDOM = () => {
  const root = document.documentElement;
  
  // CSS değişkenlerini temizle
  root.style.removeProperty('--template-primary');
  root.style.removeProperty('--template-header-bg');
  root.style.removeProperty('--template-button-bg');
  root.style.removeProperty('--template-border-radius');
  root.style.removeProperty('--template-spacing');

  // Body'den template sınıflarını kaldır
  document.body.className = document.body.className.replace(/qr-template-\w+/g, '');

  // Custom CSS'i kaldır
  const customStyleElement = document.getElementById('qr-template-custom-css');
  if (customStyleElement) {
    customStyleElement.remove();
  }
};

// Template config'den description satır sayısını al
export const getDescriptionLines = (templateConfig) => {
  return templateConfig?.description_lines || 2;
};

// Template config'den image aspect ratio'yu al
export const getImageAspectRatio = (templateConfig) => {
  const imageStyle = templateConfig?.image_style || 'square';
  
  switch (imageStyle) {
    case 'square': return '1:1';
    case 'wide': return '16:9';
    case 'tall': return '3:4';
    default: return '1:1';
  }
};

// Template'e göre kategori görünümünü belirle
export const getCategoryDisplayType = (templateConfig) => {
  return templateConfig?.category_style || 'tabs';
};

// Template'e göre fiyat pozisyonunu belirle
export const getPricePosition = (templateConfig) => {
  return templateConfig?.price_position || 'right';
};
