/**
 * QR Template Helper Functions
 * Mevcut QR menü tasarımına dokunmadan template sistemi uygular
 */

// Template'i DOM'a uygula - sadece body'ye sınıf ekler
export const applyTemplateToDOM = (templateConfig) => {
  if (!templateConfig) return;

  const layoutType = templateConfig.layout_type || 'classic';

  // Mevcut template sınıflarını temizle
  document.body.className = document.body.className.replace(/qr-template-\w+/g, '');

  // Yeni template sınıfını ekle
  document.body.classList.add(`qr-template-${layoutType}`);

  // Custom CSS'i uygula
  if (templateConfig.custom_css) {
    let customStyleElement = document.getElementById('qr-template-custom-css');
    if (!customStyleElement) {
      customStyleElement = document.createElement('style');
      customStyleElement.id = 'qr-template-custom-css';
      document.head.appendChild(customStyleElement);
    }
    customStyleElement.textContent = templateConfig.custom_css;
  }
};



// Template'i temizle
export const clearTemplateFromDOM = () => {
  // Body'den template sınıflarını kaldır
  document.body.className = document.body.className.replace(/qr-template-\w+/g, '');

  // Custom CSS'i kaldır
  const customStyleElement = document.getElementById('qr-template-custom-css');
  if (customStyleElement) {
    customStyleElement.remove();
  }
};


