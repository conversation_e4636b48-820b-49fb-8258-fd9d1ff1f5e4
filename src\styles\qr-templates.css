/* QR Menu Template Styles - Mevcut tasarımı override eder */

/* ===== CLASSIC TEMPLATE (Varsayılan) ===== */
.qr-template-classic {
  /* Mevcut tasarımı korur, hiçbir değişiklik yapmaz */
}

/* ===== MODERN TEMPLATE ===== */
.qr-template-modern {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Modern - Header Override */
.qr-template-modern header {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* Modern - Butonlar */
.qr-template-modern button,
.qr-template-modern .btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.qr-template-modern button:hover,
.qr-template-modern .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Modern - Kartlar */
.qr-template-modern .rounded-3xl {
  border-radius: 16px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Modern - Resimler */
.qr-template-modern .rounded-2xl {
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ===== MINIMAL TEMPLATE ===== */
.qr-template-minimal {
  background: #ffffff;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* Minimal - Header Override */
.qr-template-minimal header {
  background: #000000 !important;
  border-bottom: 1px solid #e5e7eb;
}

/* Minimal - Butonlar */
.qr-template-minimal button,
.qr-template-minimal .btn {
  background: #000000 !important;
  border-radius: 0 !important;
  border: 2px solid #000000 !important;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.qr-template-minimal button:hover,
.qr-template-minimal .btn:hover {
  background: #ffffff !important;
  color: #000000 !important;
}

/* Minimal - Kartlar */
.qr-template-minimal .rounded-3xl {
  border-radius: 0 !important;
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: none !important;
}

/* Minimal - Resimler */
.qr-template-minimal .rounded-2xl {
  border-radius: 0 !important;
}

/* ===== LUXURY TEMPLATE ===== */
.qr-template-luxury {
  background: linear-gradient(135deg, #fefdf8 0%, #faf9f0 100%);
  font-family: 'Georgia', serif;
}

/* Luxury - Header Override */
.qr-template-luxury header {
  background: linear-gradient(135deg, #8b5a2b 0%, #a0522d 100%) !important;
  position: relative;
}

.qr-template-luxury header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* Luxury - Butonlar */
.qr-template-luxury button,
.qr-template-luxury .btn {
  background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%) !important;
  border-radius: 8px !important;
  border: 2px solid #d4af37 !important;
  color: #000000 !important;
  font-weight: 600;
  text-shadow: none;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.qr-template-luxury button:hover,
.qr-template-luxury .btn:hover {
  background: linear-gradient(135deg, #b8860b 0%, #d4af37 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Luxury - Kartlar */
.qr-template-luxury .rounded-3xl {
  border-radius: 12px !important;
  background: linear-gradient(135deg, #fefdf8 0%, #faf9f0 100%) !important;
  border: 2px solid #d4af37 !important;
  box-shadow: 0 8px 24px rgba(212, 175, 55, 0.2);
  position: relative;
}

.qr-template-luxury .rounded-3xl::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 10px;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);
  pointer-events: none;
}

/* Luxury - Resimler */
.qr-template-luxury .rounded-2xl {
  border-radius: 8px !important;
  border: 2px solid #d4af37;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Modern Template Mobile */
  .qr-template-modern .rounded-3xl {
    border-radius: 12px !important;
  }

  /* Luxury Template Mobile */
  .qr-template-luxury .rounded-3xl {
    border-radius: 8px !important;
  }
}

/* ===== ANIMASYONLAR ===== */
.qr-template-modern .rounded-3xl,
.qr-template-luxury .rounded-3xl {
  transition: all 0.3s ease;
}

.qr-template-modern .rounded-3xl:hover {
  transform: translateY(-2px);
}

.qr-template-luxury .rounded-3xl:hover {
  transform: translateY(-1px);
  box-shadow: 0 12px 32px rgba(212, 175, 55, 0.3);
}
