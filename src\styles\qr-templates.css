/* QR Menu Template Styles */

/* Base Template Variables */
:root {
  --template-primary: #333;
  --template-header-bg: #333;
  --template-button-bg: #047857;
  --template-border-radius: 8px;
  --template-spacing: 16px;
}

/* Layout Types */
.qr-template-classic {
  --template-primary: #333;
  --template-secondary: #666;
  --template-accent: #047857;
  --template-background: #ffffff;
}

.qr-template-modern {
  --template-primary: #2563eb;
  --template-secondary: #64748b;
  --template-accent: #3b82f6;
  --template-background: #f8fafc;
}

.qr-template-minimal {
  --template-primary: #000;
  --template-secondary: #999;
  --template-accent: #000;
  --template-background: #ffffff;
}

.qr-template-luxury {
  --template-primary: #8b5a2b;
  --template-secondary: #d4af37;
  --template-accent: #b8860b;
  --template-background: #fefdf8;
}

/* Card Styles */
.menu-card {
  transition: all 0.2s ease;
  overflow: hidden;
}

.card-standard {
  border: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  background: white;
}

.card-elevated {
  border: none;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  background: white;
}

.card-flat {
  border: none;
  box-shadow: none;
  background: transparent;
}

.card-premium {
  border: 1px solid var(--template-secondary);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  background: white;
}

/* Button Styles */
.menu-button {
  transition: all 0.2s ease;
  font-weight: 500;
  padding: 8px 16px;
  border: none;
  cursor: pointer;
  background: var(--template-button-bg);
  color: white;
}

.button-rounded {
  border-radius: 8px;
}

.button-pill {
  border-radius: 50px;
}

.button-square {
  border-radius: 0;
}

.button-elegant {
  border-radius: 4px;
  border: 1px solid var(--template-secondary);
}

.menu-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.menu-button:active {
  transform: translateY(0);
}

/* Image Styles */
.menu-image {
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-square {
  aspect-ratio: 1;
  border-radius: 8px;
}

.image-wide {
  aspect-ratio: 16/9;
  border-radius: 8px;
}

.image-tall {
  aspect-ratio: 3/4;
  border-radius: 8px;
}

.image-circle {
  aspect-ratio: 1;
  border-radius: 50%;
}

.menu-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Header Styles */
.menu-header {
  background: var(--template-header-bg);
  color: white;
  padding: 16px;
}

.header-simple {
  text-align: left;
}

.header-centered {
  text-align: center;
}

.header-minimal {
  padding: 8px 16px;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.header-bold {
  padding: 24px 16px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Category Styles */
.menu-category {
  margin-bottom: var(--template-spacing);
}

.category-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  overflow-x: auto;
}

.category-tabs .category-item {
  padding: 8px 16px;
  border-radius: var(--template-border-radius);
  background: rgba(0,0,0,0.05);
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-tabs .category-item.active {
  background: var(--template-accent);
  color: white;
}

.category-dropdown {
  position: relative;
  margin-bottom: 16px;
}

.category-list {
  display: grid;
  gap: 8px;
  margin-bottom: 16px;
}

.category-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

/* Price Styles */
.menu-price {
  color: var(--template-accent);
  font-weight: bold;
}

.price-right {
  text-align: right;
}

.price-left {
  text-align: left;
}

.price-center {
  text-align: center;
}

.price-badge {
  background: var(--template-accent);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* Spacing Utilities */
.spacing-tight {
  --template-spacing: 8px;
}

.spacing-normal {
  --template-spacing: 16px;
}

.spacing-wide {
  --template-spacing: 24px;
}

.spacing-generous {
  --template-spacing: 32px;
}

/* Font Families */
.font-system {
  font-family: system-ui, -apple-system, sans-serif;
}

.font-serif {
  font-family: Georgia, serif;
}

.font-mono {
  font-family: 'Courier New', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-header {
    padding: 12px 16px;
  }
  
  .category-tabs {
    padding: 0 16px;
    margin: 0 -16px 16px -16px;
  }
  
  .menu-card {
    margin: 0 16px;
  }
  
  .spacing-generous {
    --template-spacing: 24px;
  }
  
  .spacing-wide {
    --template-spacing: 20px;
  }
}

/* Animation Classes */
.template-fade-in {
  animation: templateFadeIn 0.3s ease-in-out;
}

@keyframes templateFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Template Specific Overrides */
.qr-template-luxury .menu-card {
  background: linear-gradient(135deg, #fefdf8 0%, #faf9f0 100%);
}

.qr-template-modern .menu-button {
  background: linear-gradient(135deg, var(--template-accent) 0%, #1d4ed8 100%);
}

.qr-template-minimal .menu-card {
  border: 1px solid #e5e7eb;
  box-shadow: none;
}

.qr-template-minimal .menu-button {
  background: #000;
  border-radius: 0;
}
