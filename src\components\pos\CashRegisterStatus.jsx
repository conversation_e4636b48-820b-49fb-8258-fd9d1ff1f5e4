import React from 'react';
import { IconCashRegister, IconAlertCircle, IconCheck, IconHistory } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { useUserActiveSession } from "../../controllers/cash-register.controller";
import { getUserDetailsInLocalStorage } from "../../helpers/UserDetails";

const CashRegisterStatus = ({ onOpenSessionModal, onOpenLastTransactionsModal, maliModeActive, cashRegisterEnabled = true }) => {
  const { data: activeSession, error, isLoading } = useUserActiveSession();
  const user = getUserDetailsInLocalStorage();
  const currency = user?.currency || "₺";

  // Kasa sistemi devre dışıysa hiçbir şey gösterme
  if (!cashRegisterEnabled) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center p-2 bg-base-200 rounded-lg animate-pulse">
        <div className="w-6 h-6 bg-gray-300 rounded-full mr-2"></div>
        <div className="h-4 bg-gray-300 rounded w-24"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center p-2 bg-red-100 text-red-700 rounded-lg">
        <IconAlertCircle size={18} stroke={iconStroke} className="mr-2" />
        <span className="text-sm">Kasa durumu yüklenemedi</span>
      </div>
    );
  }

  if (!activeSession) {
    return (
      <div
        className="flex items-center px-4 py-2 bg-yellow-100 text-yellow-700 rounded-lg cursor-pointer hover:bg-yellow-200 transition"
        onClick={onOpenSessionModal}
      >
        <IconCashRegister size={18} stroke={iconStroke} className="mr-2" />
        <span className="text-sm font-medium">Kasa oturumu açın</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <div
        className="flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-lg cursor-pointer hover:bg-green-200 transition"
        onClick={onOpenSessionModal}
      >
        <IconCheck size={18} stroke={iconStroke} className="mr-2" />
        <span className="text-sm font-medium">{activeSession.register_name}</span>
        <span className="text-xs ml-2 bg-green-200 px-2 py-0.5 rounded">
          {parseFloat(activeSession.opening_amount).toFixed(2)} {currency}
        </span>
      </div>

      {/* Son 3 İşlem Butonu - Mali mode aktifken gizle */}
      {!maliModeActive && (
        <button
          onClick={onOpenLastTransactionsModal}
          className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition"
          title="Son 3 İşlem"
        >
          <IconHistory size={16} stroke={iconStroke} className="mr-1" />
          <span className="text-xs font-medium">Son 3 Sipariş</span>
        </button>
      )}
    </div>
  );
};

export default CashRegisterStatus;
