import React, { useRef, useState } from "react";
import Page from "../components/Page";
import { IconCarrot, IconFilter, IconDownload, IconArrowUpRight, IconArrowDownLeft, IconSearch, IconBox, IconAlertTriangle, IconCircleArrowDownLeft, IconCircleArrowUpRight, IconCircleMinus, IconArrowsRandom, IconLibraryMinus } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { Link } from "react-router-dom";
import clsx from "clsx";
import { useInventoryLogs } from "../controllers/inventory.controller";
import moment from "moment";
import { toast } from "react-hot-toast";

export default function InventoryLogsPage() {
  const filters = [
    { key: "today", value: "Bugün" },
    { key: "yesterday", value: "Dün" },
    { key: "last_7days", value: "Son 7 Gün" },
    { key: "this_month", value: "Bu Ay" },
    { key: "last_month", value: "Geçen Ay" },
    { key: "custom", value: "Özel" },
  ];

  const [selectedMovementType, setSelectedMovementType] = useState("all");

  const fromDateRef = useRef();
  const toDateRef = useRef();
  const filterTypeRef = useRef();

  const now = new Date();
  const defaultDateFrom = `${now.getFullYear()}-${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;
  const defaultDateTo = `${now.getFullYear()}-${(now.getMonth() + 2)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;

  const [state, setState] = useState({
    filter: filters[0].key,
    fromDate: null,
    toDate: null,
    warehouseId: "",
    search: ""
  });

  const { APIURL, data, error, isLoading } = useInventoryLogs({
    type: state.filter,
    from: state.fromDate,
    to: state.toDate,
    movementType: selectedMovementType,
  });

  if (isLoading) {
    return <Page>Yükleniyor...</Page>;
  }

  if (error) {
    return <Page>Veri yüklenirken bir hata oluştu!</Page>;
  }

  const handleExportData = async () => {
    try {
      const { Parser } = await import("@json2csv/plainjs");
      const { saveAs } = await import("file-saver");

      if (data.length != 0){
        const formattedData = data.map((entry) => ({
          title: entry.title,
          movement: entry.type,
          quantity: entry.quantity,
          unit:entry.unit || '',
          remarks: entry.note,
          updated_by: entry.created_by,
          created_at: moment.utc(entry.created_at).local().format("YYYY-MM-DD HH:mm"),
        }));

        const opts = {
          fields: ["title", "movement", "quantity", "unit", "remarks", "updated_by", "created_at"],
        };

        const parser = new Parser(opts);
        const csv = parser.parse(formattedData);

        const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });

        const formattedDate = `${new Date().toISOString().substring(0, 10)}`;
        const fileName = `Envanter Hareketleri - ${formattedDate}.csv`;

        saveAs(blob, fileName);
        toast.success("Veriler başarıyla dışa aktarıldı");
      } else {
        toast.error("Dışa aktarılacak veri bulunamadı");
      }

    } catch (error) {
      console.error("CSV dışa aktarma hatası:", error);
      toast.dismiss();
      toast.error("Veri dışa aktarılırken bir hata oluştu");
    }
  };

  return (
    <Page>
      <div className="breadcrumbs text-sm mb-1">
        <ul>
          <li>
            <Link to="/dashboard/inventory">Envanter</Link>
          </li>
          <li>Hareketler</li>
        </ul>
      </div>

      <div className="flex flex-col md:flex-row md:items-center gap-2">
        <h3 className="text-2xl flex-1">Envanter Hareketleri</h3>
        <div className="relative md:w-80">
          <IconSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="search"
            className="input input-sm input-bordered focus:outline-none focus:ring-1 focus-within:ring-gray-200 transition pl-8 pr-2 w-full rounded-lg text-gray-500 py-4 h-8"
            placeholder="Ara..."
            value={state.search}
            onChange={(e) => {
              setState({
                ...state,
                search: e.target.value,
              });
            }}
          />
        </div>
        <button
          onClick={() => document.getElementById("filter-dialog").showModal()}
          className="btn btn-sm rounded-lg bg-restro-green-light w-fit"
        >
          <IconFilter stroke={iconStroke} /> Filtreler
        </button>
        <button
          onClick={handleExportData}
          className="btn btn-sm rounded-lg transition bg-restro-green-light w-fit"
        >
          <IconDownload stroke={iconStroke} size={20} />
        </button>
      </div>

      <h3 className="mt-1 text-gray-500 text-base">
        Gösterilen veriler: {filters.find((f) => f.key == state.filter).value}
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
        {[
          {
            label: "Tümü",
            icon: <IconArrowsRandom stroke={1.5} className="text-gray-600" />,
            type: "all",
            border: "border-l-gray-400",
            countColor: "text-gray-600",
          },
          {
            label: "Giriş",
            icon: <IconCircleArrowDownLeft stroke={1.5} className="text-green-600" />,
            type: "IN",
            border: "border-l-restro-green",
            countColor: "text-restro-green",
          },
          {
            label: "Çıkış",
            icon: <IconCircleArrowUpRight stroke={1.5} className="text-yellow-600" />,
            type: "OUT",
            border: "border-l-yellow-600",
            countColor: "text-yellow-600",
          },
          {
            label: "Fire",
            icon: <IconLibraryMinus stroke={1.5} className="text-red-600" />,
            type: "WASTAGE",
            border: "border-l-red-600",
            countColor: "text-red-600",
          },
        ].map(({ label, icon, type, border, countColor }) => {
          const isSelected = selectedMovementType === type;
          return (
            <div
              key={type}
              onClick={() => setSelectedMovementType(type)}
              className={clsx(
                "cursor-pointer flex items-center gap-4 px-4 py-4 rounded-xl border border-l-8 transition-all duration-200",
                border,
                isSelected ? "bg-gray-50" : ""
              )}
            >
              <div className="p-3 rounded-lg bg-gray-100 shadow-inner">{icon}</div>
              <div>
                <div className="text-sm font-medium text-gray-600">{label}</div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="w-full overflow-auto h-[calc(100vh-320px)] mt-4">
        {data
          .filter((item) => {
            if (!state.search) {
              return true;
            }
            return new String(item.title).trim().toLowerCase().includes(state.search.trim().toLowerCase());
          }).length === 0 ? (
          <div className="flex flex-col justify-center items-center text-center h-full">
            <img
              src="/assets/illustrations/orders-not-found.webp"
              alt="Stok hareketi bulunamadı"
              className="w-1/2 md:w-60"
            />
            <p className="text-md text-gray-600">Stok hareketi bulunamadı</p>
          </div>
        ) : (
          <table className="table table-sm">
            <thead className="bg-restro-green-light text-gray-500 sticky top-0 z-10">
              <tr>
                <th className="text-start p-2.5">#</th>
                <th className="text-start">Ürün</th>
                <th className="text-start">Miktar</th>
                <th className="text-start">Hareket</th>
                <th className="text-start">Tarih/Saat</th>
                <th className="text-start max-w-60">Açıklama</th>
                <th className="text-start">Güncelleyen</th>
              </tr>
            </thead>
            <tbody>
              {data
                .filter((item) => {
                  if (!state.search) {
                    return true;
                  }
                  return new String(item.title).trim().toLowerCase().includes(state.search.trim().toLowerCase());
                })
                .map((inventoryItem, i) => {
                  const { id, inventory_item_id, title, type, quantity, unit, note, created_by, created_at } = inventoryItem;
                  return (
                    <tr key={i}>
                      <td>{data.length - i}</td>
                      <td>
                        <div className="flex gap-2 items-center">
                          <p className="text-ellipsis line-clamp-2">{title}</p>
                        </div>
                      </td>
                      <td>
                        <p>
                          {quantity ? `${Number(quantity).toLocaleString()} ${unit || ''}` : "Yok"}
                        </p>
                      </td>
                      <td>
                        <div
                          className={clsx("flex items-center gap-1 w-fit px-2 py-1 rounded-lg text-xs", {
                            "text-red-600 bg-red-50": type == "WASTAGE",
                            "text-yellow-600 bg-yellow-50": type == "OUT",
                            "text-green-600 bg-green-50": type == "IN",
                          })}
                        >
                          {type == "WASTAGE" && "Fire"}
                          {type == "OUT" && "Çıkış"}
                          {type == "IN" && "Giriş"}
                        </div>
                      </td>
                      <td>{created_at ? moment.utc(created_at).local().format("DD/MM/YYYY, h:mm A") : "Tarih yok"}</td>
                      <td>{note || "Yok"}</td>
                      <td>{created_by || "Bilinmiyor"}</td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        )}
      </div>

      {/* filter dialog */}
      <dialog id="filter-dialog" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg flex items-center">
            <IconFilter stroke={iconStroke} /> Filtreler
          </h3>
          {/* filters */}
          <div className="my-4">
            <div>
              <label className="block text-gray-500 text-sm">Filtre</label>
              <select
                className="select select-sm select-bordered w-full"
                ref={filterTypeRef}
              >
                {filters.map((filter, index) => (
                  <option key={index} value={filter.key}>
                    {filter.value}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex gap-2 mt-4">
              <div className="flex-1">
                <label
                  htmlFor="fromDate"
                  className="block text-gray-500 text-sm"
                >
                  Başlangıç Tarihi
                </label>
                <input
                  defaultValue={defaultDateFrom}
                  type="date"
                  ref={fromDateRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
              <div className="flex-1">
                <label
                  htmlFor="toDate"
                  className="block text-gray-500 text-sm"
                >
                  Bitiş Tarihi
                </label>
                <input
                  defaultValue={defaultDateTo}
                  type="date"
                  ref={toDateRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
            </div>
          </div>
          {/* filters */}
          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn">Kapat</button>
              <button
                onClick={() => {
                  setState({
                    ...state,
                    filter: filterTypeRef.current.value,
                    fromDate: fromDateRef.current.value || null,
                    toDate: toDateRef.current.value || null,
                  });
                }}
                className="btn ml-2"
              >
                Uygula
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* filter dialog */}
    </Page>
  );
}