import React, { useState, useEffect, createContext, useContext, useCallback, useMemo } from 'react';
import { getUserDetailsInLocalStorage } from '../helpers/UserDetails';
import { saveLockStateInLocalStorage, getLockStateFromLocalStorage } from '../helpers/LockHelper';
import { useLocation } from 'react-router-dom';
import LockScreen from '../components/LockScreen';
import { getPinSettings } from '../controllers/pin.controller';
import { shouldDisableLockScreen, shouldDisableAutoLock, checkIdleTimeAndLock } from '../helpers/LockScreenHelper';

const LockContext = createContext({
  isLocked: false,
  lockTime: null,
  lock: () => {},
  unlock: () => {},
  disableScreenLock: false,
  shouldLockPath: false
});

// Sadece POS sayfasında kilitleme işlemi yapılacak
const LOCKED_PATHS = ['/dashboard/pos'];

export function LockProvider({ children }) {
  const location = useLocation();
  const user = getUserDetailsInLocalStorage();
  const tenantId = user?.tenant_id;

  // Ekran kilidini devre dışı bırakma ayarını helper fonksiyonu ile kontrol et
  const disableScreenLock = shouldDisableLockScreen(user);

  // Otomatik kilitlemeyi devre dışı bırakma ayarını helper fonksiyonu ile kontrol et
  const disableAutoLock = shouldDisableAutoLock(user);

  // Mevcut yolun kilitlenebilir olup olmadığını kontrol et
  const shouldLockPath = useMemo(() => {
    return LOCKED_PATHS.some(path => location.pathname === path);
  }, [location.pathname]);

  // Kilit durumunu başlat
  const [isLocked, setIsLocked] = useState(() => {
    // Sadece kilitlenebilir bir yoldaysak ve kilit devre dışı değilse kilit durumunu kontrol et
    if (shouldLockPath && !disableScreenLock && tenantId) {
      const lockState = getLockStateFromLocalStorage(tenantId);
      return lockState?.isLocked || false;
    }
    return false;
  });

  const [lockTime, setLockTime] = useState(() => {
    if (shouldLockPath && !disableScreenLock && tenantId) {
      const lockState = getLockStateFromLocalStorage(tenantId);
      return lockState?.lockTime || null;
    }
    return null;
  });

  const [lastActivityTime, setLastActivityTime] = useState(Date.now());
  const [pinSettings, setPinSettings] = useState(() => getPinSettings());

  // Kilitleme fonksiyonu
  const lock = useCallback(() => {
    // Sadece kilitlenebilir bir yoldaysak, kilit devre dışı değilse ve tenant mevcutsa kilitle
    if (shouldLockPath && !disableScreenLock && tenantId) {
      setIsLocked(true);
      const currentTime = new Date().toISOString();
      setLockTime(currentTime);
      saveLockStateInLocalStorage({ isLocked: true, lockTime: currentTime }, tenantId);
    }
  }, [shouldLockPath, disableScreenLock, tenantId]);

  // Kilit açma fonksiyonu
  const unlock = useCallback(() => {
    setIsLocked(false);
    setLockTime(null);
    saveLockStateInLocalStorage({ isLocked: false, lockTime: null }, tenantId);
    setLastActivityTime(Date.now());
  }, [tenantId]);

  // Boşta kalma süresi sonrası otomatik kilitleme
  useEffect(() => {
    // Sadece kilitlenebilir bir yoldaysak, otomatik kilitleme etkinse ve otomatik kilit devre dışı değilse
    if (shouldLockPath && pinSettings.autoLockEnabled && !disableAutoLock && tenantId) {
      const idleTimeLimit = pinSettings.autoLockTime * 60 * 1000; // Dakikayı milisaniyeye çevir

      const interval = setInterval(() => {
        // Helper fonksiyonu kullanarak boşta kalma süresini kontrol et
        checkIdleTimeAndLock(lastActivityTime, pinSettings, lock);
      }, 1000); // Her saniye kontrol et

      return () => clearInterval(interval);
    }
  }, [lastActivityTime, pinSettings, disableAutoLock, lock, shouldLockPath, tenantId]);

  // Kilitlenebilir olmayan yollara geçildiğinde kilit durumunu sıfırla
  useEffect(() => {
    if (!shouldLockPath && isLocked) {
      // Kilitlenebilir olmayan bir yola geçildiğinde otomatik olarak kilidi aç
      unlock();
    }
  }, [shouldLockPath, isLocked, unlock]);

  // PIN ayarlarını düzenli aralıklarla kontrol et - daha iyi performans için frekansı azalttık
  useEffect(() => {
    const checkPinSettings = setInterval(() => {
      const currentSettings = getPinSettings();
      setPinSettings(currentSettings);
    }, 1000); // Her saniye kontrol et (10 saniyelik süre için daha hassas kontrol)

    return () => clearInterval(checkPinSettings);
  }, []);

  // Kullanıcı aktivitesi takibi
  const handleUserActivity = useCallback(() => {
    if (shouldLockPath && pinSettings.autoLockEnabled && !disableAutoLock) {
      setLastActivityTime(Date.now());
    }
  }, [pinSettings.autoLockEnabled, disableAutoLock, shouldLockPath]);

  // Gerektiğinde aktivite dinleyicilerini ayarla
  useEffect(() => {
    if (shouldLockPath && pinSettings.autoLockEnabled && !disableAutoLock) {
      // Daha iyi performans için pasif olay dinleyicileri kullan
      const options = { passive: true };

      document.addEventListener('click', handleUserActivity, options);
      document.addEventListener('keypress', handleUserActivity, options);

      // Fare hareketini kısıtlayarak frekansını azalt
      let lastMove = 0;
      const handleMouseMove = (e) => {
        const now = Date.now();
        if (now - lastMove > 500) { // Fare hareketi için sadece her 500ms'de bir güncelle
          lastMove = now;
          handleUserActivity();
        }
      };

      document.addEventListener('mousemove', handleMouseMove, options);
      document.addEventListener('touchstart', handleUserActivity, options);

      return () => {
        document.removeEventListener('click', handleUserActivity);
        document.removeEventListener('keypress', handleUserActivity);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('touchstart', handleUserActivity);
      };
    }
  }, [pinSettings.autoLockEnabled, disableAutoLock, handleUserActivity, shouldLockPath]);

  // Context değerlerini hazırla
  const contextValue = {
    isLocked,
    lockTime,
    lock,
    unlock,
    disableScreenLock,
    shouldLockPath
  };

  // LockProvider her zaman context değerlerini sağlayacak, ancak kilit ekranı sadece
  // kilitlenebilir bir yoldaysak ve kilitlendiyse gösterilecek
  return (
    <LockContext.Provider value={contextValue}>
      {(shouldLockPath && isLocked) ? <LockScreen /> : children}
    </LockContext.Provider>
  );
}

export const useLock = () => {
  const context = useContext(LockContext);
  if (!context) {
    throw new Error('useLock must be used within a LockProvider');
  }
  return context;
};