import React, { useState, useRef } from "react";
import Page from "../components/Page";
import { 
  IconPlus, 
  IconPencil, 
  IconTrash, 
  IconSettings, 
  IconMapPin,
  IconBox,
  IconTransfer,
  IconHistory,
  IconChartBar,
  IconToggleLeft,
  IconToggleRight
} from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import {
  useStorageLocations,
  useStorageLocationSettings,
  createStorageLocation,
  updateStorageLocation,
  deleteStorageLocation,
  updateStorageLocationSettings,
  LOCATION_TYPES,
  getLocationTypeInfo,
  formatLocationCode,
  validateLocationData
} from "../controllers/storage-locations.controller";
import { useFloors } from "../controllers/floors.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";
import { Link } from "react-router-dom";

const StorageLocationsPage = () => {
  const { data: locationsData, isLoading: locationsLoading, APIURL: locationsAPI } = useStorageLocations();
  const { data: settingsData, isLoading: settingsLoading, APIURL: settingsAPI } = useStorageLocationSettings();
  const { data: floors, isLoading: floorsLoading } = useFloors();

  // Backend response'u kontrol et - bazen direkt array, bazen {data: array} formatında geliyor
  const locations = Array.isArray(locationsData) ? locationsData : (locationsData?.data || []);
  const settings = settingsData?.data || settingsData || null;

  // Debug için
  console.log("Raw locationsData:", locationsData);
  console.log("Parsed locations:", locations);
  console.log("Locations length:", locations?.length);
  console.log("Raw settingsData:", settingsData);
  console.log("Parsed settings:", settings);


  
  const [editingLocation, setEditingLocation] = useState(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  
  // Form refs
  const nameRef = useRef();
  const typeRef = useRef();
  const descriptionRef = useRef();
  const locationCodeRef = useRef();
  const capacityInfoRef = useRef();

  // Modül ayarları toggle
  const handleToggleModule = async () => {
    try {
      const newSettings = {
        ...settings,
        is_enabled: !settings?.is_enabled
      };

      const res = await updateStorageLocationSettings(newSettings);
      if (res.status === 200) {
        toast.success(settings?.is_enabled ? "Modül devre dışı bırakıldı" : "Modül aktifleştirildi");
        await mutate(settingsAPI);
      }
    } catch (error) {
      toast.error("Ayarlar güncellenirken hata oluştu");
      console.error(error);
    }
  };

  // Lokasyon ekleme
  const handleAddLocation = async () => {
    const name = nameRef.current?.value?.trim();
    const type = typeRef.current?.value;
    const description = descriptionRef.current?.value?.trim();
    const locationCode = locationCodeRef.current?.value?.trim();
    const capacityInfo = capacityInfoRef.current?.value?.trim();

    const locationData = {
      name,
      type,
      description,
      location_code: locationCode || formatLocationCode(name, type),
      capacity_info: capacityInfo ? JSON.parse(capacityInfo) : null,
      is_active: true
    };

    const validation = validateLocationData(locationData);
    if (!validation.isValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError);
      return;
    }

    try {
      const res = await createStorageLocation(locationData);
      if (res.status === 200) {
        toast.success("Lokasyon başarıyla eklendi");
        setIsAddModalOpen(false);
        clearForm();
        await mutate(locationsAPI);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Lokasyon eklenirken hata oluştu";
      toast.error(message);
      console.error(error);
    }
  };

  // Lokasyon güncelleme
  const handleUpdateLocation = async () => {
    if (!editingLocation) return;

    const name = nameRef.current?.value?.trim();
    const type = typeRef.current?.value;
    const description = descriptionRef.current?.value?.trim();
    const locationCode = locationCodeRef.current?.value?.trim();
    const capacityInfo = capacityInfoRef.current?.value?.trim();

    const locationData = {
      name,
      type,
      description,
      location_code: locationCode,
      capacity_info: capacityInfo ? JSON.parse(capacityInfo) : null
    };

    const validation = validateLocationData(locationData);
    if (!validation.isValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError);
      return;
    }

    try {
      const res = await updateStorageLocation(editingLocation.id, locationData);
      if (res.status === 200) {
        toast.success("Lokasyon başarıyla güncellendi");
        setEditingLocation(null);
        clearForm();
        await mutate(locationsAPI);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Lokasyon güncellenirken hata oluştu";
      toast.error(message);
      console.error(error);
    }
  };

  // Lokasyon silme
  const handleDeleteLocation = async (location) => {
    if (!confirm(`"${location.name}" lokasyonunu silmek istediğinize emin misiniz?`)) {
      return;
    }

    try {
      const res = await deleteStorageLocation(location.id);
      if (res.status === 200) {
        toast.success("Lokasyon başarıyla silindi");
        await mutate(locationsAPI);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Lokasyon silinirken hata oluştu";
      toast.error(message);
      console.error(error);
    }
  };

  // Form temizleme
  const clearForm = () => {
    if (nameRef.current) nameRef.current.value = "";
    if (typeRef.current) typeRef.current.value = "";
    if (descriptionRef.current) descriptionRef.current.value = "";
    if (locationCodeRef.current) locationCodeRef.current.value = "";
    if (capacityInfoRef.current) capacityInfoRef.current.value = "";
  };

  // Düzenleme modalını aç
  const openEditModal = (location) => {
    setEditingLocation(location);
    setTimeout(() => {
      if (nameRef.current) nameRef.current.value = location.name || "";
      if (typeRef.current) typeRef.current.value = location.type || "";
      if (descriptionRef.current) descriptionRef.current.value = location.description || "";
      if (locationCodeRef.current) locationCodeRef.current.value = location.location_code || "";
      if (capacityInfoRef.current) {
        capacityInfoRef.current.value = location.capacity_info ? JSON.stringify(location.capacity_info, null, 2) : "";
      }
    }, 100);
  };

  if (locationsLoading || settingsLoading) {
    return (
      <Page>
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </Page>
    );
  }

  return (
    <Page>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Depo/Bar Lokasyonları</h1>
            <p className="text-gray-600 mt-1">
              Farklı depo ve barlarınızı yönetin, floor bazlı stok düşümü yapın
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Modül Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Modül:</span>
              <button
                onClick={handleToggleModule}
                className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  settings?.is_enabled
                    ? "bg-green-100 text-green-700 hover:bg-green-200"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
              >
                {settings?.is_enabled ? (
                  <>
                    <IconToggleRight size={16} />
                    Aktif
                  </>
                ) : (
                  <>
                    <IconToggleLeft size={16} />
                    Pasif
                  </>
                )}
              </button>
            </div>

            {/* Action Buttons */}
            <Link
              to="/dashboard/storage-locations/settings"
              className="btn btn-ghost btn-sm"
            >
              <IconSettings size={18} stroke={iconStroke} />
              Ayarlar
            </Link>

            <Link
              to="/dashboard/storage-locations/floor-mappings"
              className="btn btn-ghost btn-sm"
              disabled={!settings?.is_enabled}
            >
              <IconMapPin size={18} stroke={iconStroke} />
              Floor Eşleştirme
            </Link>

            <button
              onClick={() => {
                setEditingLocation(null);
                clearForm();
                document.getElementById('modal-location-form').showModal();
              }}
              className="btn bg-blue-500 hover:bg-blue-600 text-white btn-sm"
              disabled={!settings?.is_enabled}
            >
              <IconPlus size={18} stroke={iconStroke} />
              Lokasyon Ekle
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        {settings?.is_enabled && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <IconMapPin size={20} className="text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Toplam Lokasyon</p>
                  <p className="text-xl font-bold">{locations?.length || 0}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <IconBox size={20} className="text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Aktif Lokasyon</p>
                  <p className="text-xl font-bold">
                    {locations?.filter(l => l.is_active)?.length || 0}
                  </p>
                </div>
              </div>
            </div>

            <Link
              to="/dashboard/storage-locations/transfers"
              className="bg-white p-4 rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <IconTransfer size={20} className="text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Transferler</p>
                  <p className="text-xl font-bold">→</p>
                </div>
              </div>
            </Link>

            <Link
              to="/dashboard/storage-locations/reports"
              className="bg-white p-4 rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <IconChartBar size={20} className="text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Raporlar</p>
                  <p className="text-xl font-bold">📊</p>
                </div>
              </div>
            </Link>
          </div>
        )}

        {/* Modül Pasifse Uyarı */}
        {!settings?.is_enabled && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <IconToggleLeft size={24} className="text-yellow-600" />
              <div>
                <h3 className="font-medium text-yellow-800">Depo/Bar Lokasyonları Modülü Pasif</h3>
                <p className="text-yellow-700 text-sm mt-1">
                  Bu modül şu anda devre dışı. Lokasyon bazlı stok yönetimi kullanmak için modülü aktifleştirin.
                  Modül pasifken normal stok sistemi çalışmaya devam eder.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Lokasyonlar Listesi */}
        {settings?.is_enabled && (
          <div className="bg-white rounded-lg border">
            <div className="p-6 border-b">
              <h2 className="text-lg font-semibold">Lokasyonlar</h2>
            </div>

            <div className="overflow-x-auto">
              {locations && locations.length > 0 ? (
                <table className="table w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th>Lokasyon</th>
                      <th>Tür</th>
                      <th>Kod</th>
                      <th>Durum</th>
                      <th>Varsayılan</th>
                      <th>İşlemler</th>
                    </tr>
                  </thead>
                  <tbody>
                    {locations.map((location) => {
                      const typeInfo = getLocationTypeInfo(location.type);
                      return (
                        <tr key={location.id} className="hover:bg-gray-50">
                          <td>
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg bg-${typeInfo.color}-100`}>
                                <span className="text-lg">{typeInfo.icon}</span>
                              </div>
                              <div>
                                <p className="font-medium">{location.name}</p>
                                {location.description && (
                                  <p className="text-sm text-gray-500">{location.description}</p>
                                )}
                              </div>
                            </div>
                          </td>
                          <td>
                            <span className={`badge badge-${typeInfo.color} badge-outline`}>
                              {typeInfo.label}
                            </span>
                          </td>
                          <td>
                            <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                              {location.location_code}
                            </code>
                          </td>
                          <td>
                            <span className={`badge ${
                              location.is_active
                                ? "badge-success"
                                : "badge-error"
                            }`}>
                              {location.is_active ? "Aktif" : "Pasif"}
                            </span>
                          </td>
                          <td>
                            {location.is_default && (
                              <span className="badge badge-primary">Varsayılan</span>
                            )}
                          </td>
                          <td>
                            <div className="flex items-center gap-2">
                              <Link
                                to={`/dashboard/storage-locations/${location.id}/inventory`}
                                className="btn btn-ghost btn-xs"
                                title="Stok Görüntüle"
                              >
                                <IconBox size={14} />
                              </Link>

                              <button
                                onClick={() => {
                                  openEditModal(location);
                                  document.getElementById('modal-location-form').showModal();
                                }}
                                className="btn btn-ghost btn-xs"
                                title="Düzenle"
                              >
                                <IconPencil size={14} />
                              </button>

                              <button
                                onClick={() => handleDeleteLocation(location)}
                                className="btn btn-ghost btn-xs text-red-600 hover:bg-red-50"
                                title="Sil"
                              >
                                <IconTrash size={14} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-12">
                  <IconMapPin size={48} className="mx-auto text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">Henüz lokasyon yok</h3>
                  <p className="text-gray-500 mb-4">
                    İlk lokasyonunuzu ekleyerek başlayın
                  </p>
                  <button
                    onClick={() => {
                      setEditingLocation(null);
                      clearForm();
                      document.getElementById('modal-location-form').showModal();
                    }}
                    className="btn bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    <IconPlus size={18} stroke={iconStroke} />
                    Lokasyon Ekle
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {settings?.is_enabled && locations && locations.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/dashboard/storage-locations/floor-mappings"
              className="bg-white p-6 rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <IconMapPin size={24} className="text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium">Floor Eşleştirme</h3>
                  <p className="text-sm text-gray-600">
                    Katları lokasyonlara bağlayın
                  </p>
                </div>
              </div>
            </Link>

            <Link
              to="/dashboard/storage-locations/movements"
              className="bg-white p-6 rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <IconHistory size={24} className="text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium">Stok Hareketleri</h3>
                  <p className="text-sm text-gray-600">
                    Tüm hareketleri görüntüleyin
                  </p>
                </div>
              </div>
            </Link>

            <Link
              to="/dashboard/storage-locations/bulk-transfer"
              className="bg-white p-6 rounded-lg border hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <IconTransfer size={24} className="text-purple-600" />
                </div>
                <div>
                  <h3 className="font-medium">Toplu Transfer</h3>
                  <p className="text-sm text-gray-600">
                    Birden fazla ürün transferi
                  </p>
                </div>
              </div>
            </Link>
          </div>
        )}

        {/* Lokasyon Ekleme/Düzenleme Modal */}
        <dialog id="modal-location-form" className="modal">
          <div className="modal-box w-11/12 max-w-2xl">
            <h3 className="font-bold text-lg mb-4">
              {editingLocation ? "Lokasyon Düzenle" : "Yeni Lokasyon Ekle"}
            </h3>

            <div className="space-y-4">
              {/* Lokasyon Adı */}
              <div>
                <label className="label">
                  <span className="label-text">Lokasyon Adı *</span>
                </label>
                <input
                  ref={nameRef}
                  type="text"
                  placeholder="Ana Depo, Cocktail Bar, vb."
                  className="input input-bordered w-full"
                />
              </div>

              {/* Lokasyon Türü */}
              <div>
                <label className="label">
                  <span className="label-text">Lokasyon Türü *</span>
                </label>
                <select ref={typeRef} className="select select-bordered w-full">
                  <option value="">Tür seçin</option>
                  {Object.entries(LOCATION_TYPES).map(([key, value]) => (
                    <option key={key} value={key}>
                      {value.icon} {value.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Lokasyon Kodu */}
              <div>
                <label className="label">
                  <span className="label-text">Lokasyon Kodu</span>
                </label>
                <input
                  ref={locationCodeRef}
                  type="text"
                  placeholder="WH01, BAR01, vb. (otomatik oluşturulur)"
                  className="input input-bordered w-full"
                />
                <div className="label">
                  <span className="label-text-alt text-gray-500">
                    Boş bırakılırsa otomatik oluşturulur
                  </span>
                </div>
              </div>

              {/* Açıklama */}
              <div>
                <label className="label">
                  <span className="label-text">Açıklama</span>
                </label>
                <textarea
                  ref={descriptionRef}
                  placeholder="Lokasyon hakkında açıklama..."
                  className="textarea textarea-bordered w-full"
                  rows="3"
                ></textarea>
              </div>

              {/* Kapasite Bilgileri (JSON) */}
              <div>
                <label className="label">
                  <span className="label-text">Kapasite Bilgileri (JSON)</span>
                </label>
                <textarea
                  ref={capacityInfoRef}
                  placeholder='{"max_items": 1000, "temperature": "cold", "special_storage": true}'
                  className="textarea textarea-bordered w-full font-mono text-sm"
                  rows="4"
                ></textarea>
                <div className="label">
                  <span className="label-text-alt text-gray-500">
                    Opsiyonel - JSON formatında kapasite ve özel bilgiler
                  </span>
                </div>
              </div>
            </div>

            <div className="modal-action">
              <button
                type="button"
                onClick={() => {
                  document.getElementById('modal-location-form').close();
                  setEditingLocation(null);
                  clearForm();
                }}
                className="btn btn-ghost"
              >
                İptal
              </button>
              <button
                type="button"
                onClick={editingLocation ? handleUpdateLocation : handleAddLocation}
                className="btn bg-blue-500 hover:bg-blue-600 text-white"
              >
                {editingLocation ? "Güncelle" : "Ekle"}
              </button>
            </div>
          </div>
          <form method="dialog" className="modal-backdrop">
            <button onClick={() => {
              setEditingLocation(null);
              clearForm();
            }}>close</button>
          </form>
        </dialog>
      </div>
    </Page>
  );
};

export default StorageLocationsPage;
