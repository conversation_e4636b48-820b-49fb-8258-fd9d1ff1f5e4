import React, { useState, useEffect } from "react";
import { 
  IconArrowLeft, 
  IconCash, 
  IconClock, 
  IconUser, 
  IconReceipt,
  IconCopyPlus
} from "@tabler/icons-react";
import { iconStroke } from "../../../config/config";
import { formatDate } from "../../../helpers/formatDate";
import { getUserDetailsInLocalStorage } from "../../../helpers/UserDetails";

const TableDetailPage = ({ table, onBack, onTableSelect }) => {
  const user = getUserDetailsInLocalStorage();
  const currency = user?.currency || "₺";

  // Masa toplam tutarını hesapla
  const calculateTableTotal = (orders) => {
    if (!orders || orders.length === 0) return 0;

    return orders
      .filter(order => order.payment_status !== "paid")
      .reduce((total, order) => {
        // 1️⃣ Ürünlerden gelen toplam
        const itemsTotal = (order.items || [])
          .filter(item => !['cancelled', 'complimentary', 'waste'].includes(item.status))
          .reduce((sum, item) => sum + parseFloat(item.price) * item.quantity, 0);

        // 2️⃣ İndirimler toplamı (calculated_discount kullan)
        const discountsTotal = (order.discounts || [])
          .reduce((sum, discount) => sum + parseFloat(discount.calculated_discount || 0), 0);

        // 3️⃣ Parçalı ödemeler toplamı
        const partialPaymentsTotal = (order.partialPayments || [])
          .reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);

        // 4️⃣ Ödenecek kalan miktarı hesapla (ürünler - indirimler - ödemeler)
        const remainingAmount = Math.max(0, itemsTotal - discountsTotal - partialPaymentsTotal);

        return total + remainingAmount;
      }, 0);
  };

  const tableTotal = calculateTableTotal(table.orders);

  // Sipariş durumu kontrolü
  const isOrderOlderThan10Minutes = (orderDate) => {
    if (!orderDate) return false;
    const orderTime = new Date(orderDate);
    const now = new Date();
    const diffInMinutes = (now - orderTime) / (1000 * 60);
    return diffInMinutes > 10;
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button
              onClick={onBack}
              className="btn btn-ghost btn-sm"
            >
              <IconArrowLeft size={20} stroke={iconStroke} />
            </button>
            <div>
              <h1 className="text-xl font-bold">{table.table_title}</h1>
              <p className="text-sm text-gray-500">
                {table.orders?.length > 0 && table.orders[0].user_name && (
                  <>Garson: {table.orders[0].user_name}</>
                )}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="text-right">
              <p className="text-sm text-gray-500">Toplam Tutar</p>
              <p className="text-xl font-bold text-green-600">
                {tableTotal.toFixed(2)} {currency}
              </p>
            </div>
            
            {/* Masa durumu */}
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              table.table_status === "locked" 
                ? "bg-gray-100 text-gray-700" 
                : table.table_status === "busy"
                ? "bg-yellow-100 text-yellow-700"
                : "bg-green-100 text-green-700"
            }`}>
              {table.table_status === "locked" ? "Kilitli" : 
               table.table_status === "busy" ? "Dolu" : "Boş"}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {table.orders && table.orders.length > 0 ? (
          <div className="space-y-4">
            {table.orders.map((order, orderIndex) => (
              <div key={orderIndex} className="bg-white rounded-lg shadow-sm border p-4">
                {/* Sipariş Başlığı */}
                <div className="flex items-center justify-between mb-4 pb-3 border-b">
                  <div>
                    <h3 className="font-semibold">Sipariş #{order.id}</h3>
                    <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
                      <span className="flex items-center gap-1">
                        <IconClock size={14} />
                        {formatDate(order.date)}
                      </span>
                      {order.user_name && (
                        <span className="flex items-center gap-1">
                          <IconUser size={14} />
                          {order.user_name}
                        </span>
                      )}
                      {order.customer_name && (
                        <span>Müşteri: {order.customer_name}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      order.payment_status === "paid" 
                        ? "bg-green-100 text-green-700"
                        : order.payment_status === "partial"
                        ? "bg-yellow-100 text-yellow-700"
                        : "bg-red-100 text-red-700"
                    }`}>
                      {order.payment_status === "paid" ? "Ödendi" :
                       order.payment_status === "partial" ? "Kısmi Ödeme" : "Ödenmedi"}
                    </div>
                    
                    {isOrderOlderThan10Minutes(order.date) && (
                      <div className="text-xs text-red-600 mt-1 animate-pulse">
                        ⚠️ 10 dakikayı aştı
                      </div>
                    )}
                  </div>
                </div>

                {/* Sipariş Ürünleri */}
                {order.items && order.items.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-700 mb-2">Ürünler:</h4>
                    {order.items.map((item, itemIndex) => (
                      <div key={itemIndex} className={`flex items-center justify-between p-2 rounded ${
                        item.status === 'cancelled' ? 'bg-red-50 border border-red-200' :
                        item.status === 'complimentary' ? 'bg-green-50 border border-green-200' :
                        item.status === 'waste' ? 'bg-gray-50 border border-gray-200' :
                        'bg-gray-50'
                      }`}>
                        <div className="flex-1">
                          <div className={`font-medium ${
                            ['cancelled', 'complimentary', 'waste'].includes(item.status) 
                              ? 'line-through text-gray-500' 
                              : ''
                          }`}>
                            {item.item_title}
                            {item.variant_title && (
                              <span className="text-sm text-gray-600 ml-1">
                                ({item.variant_title})
                              </span>
                            )}
                          </div>
                          
                          {item.addons && item.addons.length > 0 && (
                            <div className="text-xs text-gray-500 mt-1">
                              Ekstralar: {item.addons.map(addon => addon.title).join(', ')}
                            </div>
                          )}
                          
                          {item.notes && (
                            <div className="text-xs text-gray-500 mt-1">
                              Not: {item.notes}
                            </div>
                          )}
                        </div>
                        
                        <div className="text-right ml-4">
                          <div className={`font-medium ${
                            ['cancelled', 'complimentary', 'waste'].includes(item.status) 
                              ? 'line-through text-gray-500' 
                              : ''
                          }`}>
                            {item.quantity} x {parseFloat(item.price).toFixed(2)} {currency}
                          </div>
                          
                          <div className={`text-xs px-2 py-1 rounded mt-1 ${
                            item.status === 'preparing' ? 'bg-yellow-100 text-yellow-700' :
                            item.status === 'completed' ? 'bg-blue-100 text-blue-700' :
                            item.status === 'delivered' ? 'bg-green-100 text-green-700' :
                            item.status === 'cancelled' ? 'bg-red-100 text-red-700' :
                            item.status === 'complimentary' ? 'bg-green-100 text-green-700' :
                            item.status === 'waste' ? 'bg-gray-100 text-gray-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {item.status === 'preparing' ? 'Hazırlanıyor' :
                             item.status === 'completed' ? 'Hazır' :
                             item.status === 'delivered' ? 'Teslim Edildi' :
                             item.status === 'cancelled' ? 'İptal' :
                             item.status === 'complimentary' ? 'İkram' :
                             item.status === 'waste' ? 'Zayi' :
                             item.status}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Kısmi Ödemeler */}
                {order.partialPayments && order.partialPayments.length > 0 && (
                  <div className="mt-4 pt-3 border-t">
                    <h4 className="font-medium text-gray-700 mb-2">Kısmi Ödemeler:</h4>
                    <div className="space-y-1">
                      {order.partialPayments.map((payment, paymentIndex) => (
                        <div key={paymentIndex} className="flex justify-between text-sm">
                          <span>{payment.payment_type} - {formatDate(payment.created_at)}</span>
                          <span className="font-medium">{parseFloat(payment.amount).toFixed(2)} {currency}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <IconReceipt size={48} stroke={1} />
            </div>
            <h3 className="text-lg font-medium text-gray-600 mb-2">Henüz sipariş yok</h3>
            <p className="text-gray-500 mb-4">Bu masada henüz sipariş bulunmuyor.</p>
            <button
              onClick={() => onTableSelect(String(table.id))}
              className="btn bg-blue-500 hover:bg-blue-600 text-white"
            >
              <IconCopyPlus size={18} stroke={1.5} />
              Ürün Ekle
            </button>
          </div>
        )}
      </div>

      {/* Footer Actions */}
      {table.orders && table.orders.length > 0 && (
        <div className="bg-white border-t p-4">
          <div className="flex gap-2 justify-end">
            <button
              onClick={() => onTableSelect(String(table.id))}
              className="btn bg-blue-500 hover:bg-blue-600 text-white"
            >
              <IconCopyPlus size={18} stroke={1.5} />
              Ürün Ekle
            </button>
            
            <button className="btn bg-green-500 hover:bg-green-600 text-white">
              <IconCash size={18} stroke={1.5} />
              Ödeme Al
            </button>
            
            <button className="btn bg-gray-500 hover:bg-gray-600 text-white">
              <IconReceipt size={18} stroke={1.5} />
              Hesap Yazdır
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TableDetailPage;
