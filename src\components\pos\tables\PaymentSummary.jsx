import React from 'react';

export const PaymentSummary = ({ netTotal, taxTotal, total, currency, discountTotal }) => (
  <div className="flex w-full items-center divide-x gap-x-4">
    <div className="flex-1 text-center">
      <p><PERSON></p>
      <p className="text-2xl">
        {Number(netTotal).toFixed(2)}
        {currency}
      </p>
    </div>
    <div className="flex-1 text-center">
      <p>KDV</p>
      <p className="text-2xl">
        {Number(taxTotal).toFixed(2)}
        {currency}
      </p>
    </div>
    <div className="flex-1 text-center">
      <p><PERSON>lam</p>
      <p className="text-2xl text-restro-green font-bold">
        {Number(total).toFixed(2)}
        {currency}
      </p>
    </div>
  </div>
);

export default PaymentSummary;