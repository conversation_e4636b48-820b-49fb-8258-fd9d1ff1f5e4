import React, { useEffect, useState } from "react";
import { IconChevronLeft, IconCategory, IconStar, IconCrown } from "@tabler/icons-react";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";

export default function CategoryPage() {
  const { t, i18n } = useTranslation(['category']);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("category");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        const currency = CURRENCIES.find(
          (c) => c.cc === data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          currency: currency?.symbol || "",
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, categories } = state;
  const storeName = storeSettings?.store_name || "";

  if (isLoading) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-amber-200 border-t-amber-600 rounded-full animate-spin mx-auto"></div>
            <IconCrown size={24} className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-amber-600" />
          </div>
          <p className="mt-6 text-amber-800 font-serif text-lg">Loading Luxury Experience...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 font-serif">
      {/* LUXURY HEADER - Altın gradient */}
      <header className="w-full bg-gradient-to-r from-amber-600 via-yellow-600 to-amber-700 text-white sticky top-0 z-50 shadow-2xl">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>
        <div className="container mx-auto px-4 py-6 flex items-center justify-between relative">
          <IconChevronLeft
            size={28}
            stroke={2}
            className="cursor-pointer hover:bg-white/20 rounded-full p-1 transition-all duration-300"
            onClick={() => navigate(-1)}
          />
          <div className="flex items-center space-x-3">
            <IconCrown size={24} className="text-yellow-200" />
            {storeSettings?.store_image ? (
              <img
                src={getImageURL(storeSettings.store_image)}
                alt={storeName}
                className="h-12 object-contain"
              />
            ) : (
              <h1 className="text-2xl font-bold text-white drop-shadow-lg">{storeName}</h1>
            )}
            <IconCrown size={24} className="text-yellow-200" />
          </div>
          <div className="w-7"></div>
        </div>
      </header>

      <div className="container mx-auto px-4">
        {/* LUXURY TITLE */}
        <div className="py-16 text-center">
          <div className="flex items-center justify-center space-x-4 mb-6">
            <IconStar size={32} className="text-amber-600" />
            <h2 className="text-5xl font-bold text-amber-800 tracking-wide">
              Premium Categories
            </h2>
            <IconStar size={32} className="text-amber-600" />
          </div>
          <p className="text-amber-700 text-lg italic">Discover our exquisite collection</p>
          <div className="w-32 h-1 bg-gradient-to-r from-transparent via-amber-600 to-transparent mx-auto mt-4"></div>
        </div>

        {/* LUXURY CATEGORIES - Kart formatında */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 pb-16">
          {categories.map((category, index) => {
            const { id, name, image } = category;
            const imageURL = getImageURL(image);

            return (
              <div
                key={id}
                className="group relative bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer border-2 border-amber-200 hover:border-amber-400 transform hover:-translate-y-2"
                onClick={() => {
                  navigate(
                    encryptedTableId
                      ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                      : `/m/${qrcode}/menu`,
                    {
                      state: { selectedCategory: id },
                    }
                  );
                }}
              >
                {/* Luxury border effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 via-yellow-400/20 to-amber-400/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10 flex flex-col items-center text-center space-y-6">
                  {/* Luxury image container */}
                  <div className="relative">
                    <div className="w-24 h-24 bg-gradient-to-br from-amber-100 to-yellow-100 rounded-full flex items-center justify-center shadow-lg border-4 border-amber-300 group-hover:border-amber-500 transition-all duration-300">
                      {image ? (
                        <img
                          src={imageURL}
                          alt={name}
                          className="w-full h-full object-cover rounded-full"
                        />
                      ) : (
                        <IconCategory size={40} className="text-amber-600" />
                      )}
                    </div>
                    {/* Luxury sparkle effect */}
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full flex items-center justify-center">
                      <IconStar size={12} className="text-white" />
                    </div>
                  </div>

                  {/* Luxury category name */}
                  <h3 className="text-2xl font-bold text-amber-800 group-hover:text-amber-900 transition-colors">
                    {t(`category:${id}`, { defaultValue: name })}
                  </h3>

                  {/* Luxury decoration */}
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-amber-400"></div>
                    <IconStar size={16} className="text-amber-500" />
                    <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-amber-400"></div>
                  </div>

                  {/* Luxury number */}
                  <div className="text-sm text-amber-600 font-medium tracking-widest">
                    COLLECTION {String(index + 1).padStart(2, '0')}
                  </div>
                </div>
              </div>
            );
          })}

          {/* LUXURY "ALL CATEGORIES" */}
          <div
            className="group relative bg-gradient-to-br from-amber-600 via-yellow-600 to-amber-700 rounded-2xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-500 cursor-pointer transform hover:-translate-y-2 border-4 border-amber-400"
            onClick={() => {
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                  : `/m/${qrcode}/menu`,
                {
                  state: { selectedCategory: "all" },
                }
              );
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/10 rounded-2xl"></div>

            <div className="relative z-10 flex flex-col items-center text-center space-y-6">
              <div className="relative">
                <div className="w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg border-4 border-white/30">
                  <IconCrown size={40} className="text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center">
                  <IconStar size={12} className="text-amber-600" />
                </div>
              </div>

              <h3 className="text-2xl font-bold text-white">
                Premium Collection
              </h3>

              <div className="flex items-center space-x-2">
                <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-white/50"></div>
                <IconStar size={16} className="text-white" />
                <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-white/50"></div>
              </div>

              <div className="text-sm text-white/80 font-medium tracking-widest">
                ALL COLLECTIONS
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
