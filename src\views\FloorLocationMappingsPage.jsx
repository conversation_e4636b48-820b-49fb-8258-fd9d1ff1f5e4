import React, { useState } from "react";
import Page from "../components/Page";
import { 
  IconPlus, 
  IconTrash, 
  IconArrowLeft,
  IconMapPin,
  IconBox,
  IconChevronUp,
  IconChevronDown
} from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { 
  useFloorLocationMappings,
  createFloorLocationMapping,
  updateFloorLocationMapping,
  deleteFloorLocationMapping,
  useStorageLocations,
  getLocationTypeInfo
} from "../controllers/storage-locations.controller";
import { useFloors } from "../controllers/floors.controller";
import { toast } from "react-hot-toast";
import { Link } from "react-router-dom";

const FloorLocationMappingsPage = () => {
  const { data: mappingsData, isLoading: mappingsLoading, mutate: mutateMappings } = useFloorLocationMappings();
  const { data: locationsData, isLoading: locationsLoading } = useStorageLocations();
  const { data: floors, isLoading: floorsLoading } = useFloors();
  
  const mappings = Array.isArray(mappingsData) ? mappingsData : (mappingsData?.data || []);
  const locations = Array.isArray(locationsData) ? locationsData : (locationsData?.data || []);
  
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedFloor, setSelectedFloor] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("");
  const [priority, setPriority] = useState(1);

  // Loading durumu
  if (mappingsLoading || locationsLoading || floorsLoading) {
    return (
      <Page>
        <div className="flex justify-center items-center h-64">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      </Page>
    );
  }

  // Yeni mapping ekleme
  const handleAddMapping = async (e) => {
    e.preventDefault();
    
    if (!selectedFloor || !selectedLocation) {
      toast.error("Floor ve lokasyon seçimi zorunludur!");
      return;
    }

    // Aynı floor-location kombinasyonu var mı kontrol et
    const existingMapping = mappings.find(m => 
      m.floor_id === parseInt(selectedFloor) && m.storage_location_id === parseInt(selectedLocation)
    );
    
    if (existingMapping) {
      toast.error("Bu floor-lokasyon eşleştirmesi zaten mevcut!");
      return;
    }

    try {
      const mappingData = {
        floor_id: parseInt(selectedFloor),
        storage_location_id: parseInt(selectedLocation),
        priority: parseInt(priority),
        is_active: true
      };

      const response = await createFloorLocationMapping(mappingData);
      
      if (response.status === 200 || response.status === 201) {
        toast.success("Floor-lokasyon eşleştirmesi başarıyla eklendi!");
        mutateMappings();
        setIsAddModalOpen(false);
        setSelectedFloor("");
        setSelectedLocation("");
        setPriority(1);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Eşleştirme eklenirken hata oluştu!";
      toast.error(message);
      console.error(error);
    }
  };

  // Mapping silme
  const handleDeleteMapping = async (mappingId) => {
    if (!confirm("Bu eşleştirmeyi silmek istediğinize emin misiniz?")) {
      return;
    }

    try {
      const response = await deleteFloorLocationMapping(mappingId);
      
      if (response.status === 200) {
        toast.success("Eşleştirme başarıyla silindi!");
        mutateMappings();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Eşleştirme silinirken hata oluştu!";
      toast.error(message);
      console.error(error);
    }
  };

  // Öncelik güncelleme
  const handleUpdatePriority = async (mappingId, newPriority) => {
    try {
      const response = await updateFloorLocationMapping(mappingId, { priority: newPriority });
      
      if (response.status === 200) {
        toast.success("Öncelik güncellendi!");
        mutateMappings();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Öncelik güncellenirken hata oluştu!";
      toast.error(message);
      console.error(error);
    }
  };

  // Floor'a göre gruplandırılmış mappings
  const groupedMappings = mappings.reduce((acc, mapping) => {
    const floorId = mapping.floor_id;
    if (!acc[floorId]) {
      acc[floorId] = [];
    }
    acc[floorId].push(mapping);
    return acc;
  }, {});

  // Her floor için mappings'leri önceliğe göre sırala
  Object.keys(groupedMappings).forEach(floorId => {
    groupedMappings[floorId].sort((a, b) => a.priority - b.priority);
  });

  return (
    <Page>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link
              to="/dashboard/settings/storage-locations"
              className="btn btn-sm btn-circle btn-ghost"
            >
              <IconArrowLeft size={20} stroke={iconStroke} />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Floor-Lokasyon Eşleştirme</h1>
              <p className="text-gray-600 mt-1">
                Her floor'u hangi lokasyonlara bağlayacağınızı ve öncelik sırasını belirleyin
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="btn bg-blue-500 hover:bg-blue-600 text-white"
          >
            <IconPlus size={18} stroke={iconStroke} />
            Yeni Eşleştirme
          </button>
        </div>

        {/* Mappings List */}
        <div className="bg-white rounded-lg shadow-sm border">
          {Object.keys(groupedMappings).length > 0 ? (
            <div className="divide-y">
              {Object.entries(groupedMappings).map(([floorId, floorMappings]) => {
                const floor = floors.find(f => f.id === parseInt(floorId));
                return (
                  <div key={floorId} className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <IconMapPin size={20} stroke={iconStroke} className="text-blue-500" />
                      <h3 className="text-lg font-semibold">{floor?.title || `Floor ${floorId}`}</h3>
                      <span className="badge badge-outline">{floorMappings.length} lokasyon</span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {floorMappings.map((mapping) => {
                        const location = locations.find(l => l.id === mapping.storage_location_id);
                        const typeInfo = getLocationTypeInfo(location?.type);
                        
                        return (
                          <div key={mapping.id} className="border rounded-lg p-4 hover:bg-gray-50">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center gap-2">
                                <span className="text-lg">{typeInfo.icon}</span>
                                <div>
                                  <h4 className="font-medium">{location?.name}</h4>
                                  <p className="text-sm text-gray-500">{location?.location_code}</p>
                                </div>
                              </div>
                              
                              <button
                                onClick={() => handleDeleteMapping(mapping.id)}
                                className="btn btn-sm btn-circle btn-ghost text-red-500 hover:bg-red-50"
                              >
                                <IconTrash size={16} />
                              </button>
                            </div>
                            
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Öncelik:</span>
                              <div className="flex items-center gap-1">
                                <button
                                  onClick={() => handleUpdatePriority(mapping.id, mapping.priority - 1)}
                                  disabled={mapping.priority <= 1}
                                  className="btn btn-xs btn-circle btn-ghost"
                                >
                                  <IconChevronUp size={14} />
                                </button>
                                <span className="font-medium px-2">{mapping.priority}</span>
                                <button
                                  onClick={() => handleUpdatePriority(mapping.id, mapping.priority + 1)}
                                  className="btn btn-xs btn-circle btn-ghost"
                                >
                                  <IconChevronDown size={14} />
                                </button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <IconMapPin size={48} stroke={iconStroke} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">Henüz eşleştirme yok</h3>
              <p className="text-gray-500 mb-4">
                Floor'ları lokasyonlara bağlayarak otomatik stok düşümü sağlayın
              </p>
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="btn bg-blue-500 hover:bg-blue-600 text-white"
              >
                <IconPlus size={18} stroke={iconStroke} />
                İlk Eşleştirmeyi Ekle
              </button>
            </div>
          )}
        </div>

        {/* Add Mapping Modal */}
        {isAddModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="font-bold text-lg mb-4">Yeni Floor-Lokasyon Eşleştirmesi</h3>
              
              <form onSubmit={handleAddMapping}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Floor *
                    </label>
                    <select
                      value={selectedFloor}
                      onChange={(e) => setSelectedFloor(e.target.value)}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Floor seçin</option>
                      {floors?.map((floor) => (
                        <option key={floor.id} value={floor.id}>
                          {floor.title}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Lokasyon *
                    </label>
                    <select
                      value={selectedLocation}
                      onChange={(e) => setSelectedLocation(e.target.value)}
                      className="select select-bordered w-full"
                      required
                    >
                      <option value="">Lokasyon seçin</option>
                      {locations?.filter(loc => loc.is_active).map((location) => {
                        const typeInfo = getLocationTypeInfo(location.type);
                        return (
                          <option key={location.id} value={location.id}>
                            {typeInfo.icon} {location.name} ({location.location_code})
                          </option>
                        );
                      })}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Öncelik
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={priority}
                      onChange={(e) => setPriority(e.target.value)}
                      className="input input-bordered w-full"
                      placeholder="1"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Düşük sayı = yüksek öncelik (önce bu lokasyondan stok düşer)
                    </p>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2 mt-6">
                  <button
                    type="button"
                    onClick={() => setIsAddModalOpen(false)}
                    className="btn"
                  >
                    İptal
                  </button>
                  <button
                    type="submit"
                    className="btn bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Ekle
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </Page>
  );
};

export default FloorLocationMappingsPage;
