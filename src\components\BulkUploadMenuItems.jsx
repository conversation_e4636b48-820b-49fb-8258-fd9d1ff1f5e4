import React, { useRef, useState } from "react";
import * as XLSX from "xlsx"; // Excel dosyasını okumak için
import { toast } from "react-hot-toast";
import { BulkUploadMenuItem } from "../controllers/menu_item.controller";

const BulkUploadMenuItems = () => {
  const fileInputRef = useRef();
  const [parsedData, setParsedData] = useState([]);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(sheet);
        setParsedData(jsonData);
        toast.success("Excel dosyası başarıyla yüklendi!");
      };
      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error(error);
      toast.error("Dosya okunurken bir hata oluştu!");
    }
  };

  const handleBulkUpload = async () => {
    if (parsedData.length === 0) {
      toast.error("Yüklemek için veri bulunamadı!");
      return;
    }

    try {
      toast.loading("Ürünler ekleniyor...");

      // Tüm ürünleri tek bir POST isteği ile gönder
      await BulkUploadMenuItem(parsedData);

      toast.dismiss();
      toast.success("Ürünler başarıyla yüklendi!");
    } catch (error) {
      console.error(error);
      toast.dismiss();
      toast.error("Ürünler eklenirken bir hata oluştu!");
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-lg font-bold mb-4">Excel ile Toplu Ürün Yükleme</h2>

      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx, .xls"
        onChange={handleFileUpload}
        className="mb-4 block"
      />

      <button
        onClick={handleBulkUpload}
        className="btn bg-blue-500 text-white hover:bg-blue-600"
      >
        Ürünleri Yükle
      </button>

      {parsedData.length > 0 && (
        <div className="mt-4">
          <h3 className="font-bold mb-2">Önizleme</h3>
          <table className="table-auto w-full border">
            <thead>
              <tr>
                <th className="border px-2 py-1">Başlık</th>
                <th className="border px-2 py-1">Açıklama</th>
                <th className="border px-2 py-1">Fiyat</th>
                <th className="border px-2 py-1">Net Fiyat</th>
                <th className="border px-2 py-1">Kategori Başlığı</th>
                <th className="border px-2 py-1">Vergi ID</th>
              </tr>
            </thead>
            <tbody>
              {parsedData.map((item, index) => (
                <tr key={index}>
                  <td className="border px-2 py-1">{item.title}</td>
                  <td className="border px-2 py-1">{item.description}</td>
                  <td className="border px-2 py-1">{item.price}</td>
                  <td className="border px-2 py-1">{item.netPrice}</td>
                  <td className="border px-2 py-1">{item.categoryTitle}</td>
                  <td className="border px-2 py-1">{item.taxId}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default BulkUploadMenuItems;
