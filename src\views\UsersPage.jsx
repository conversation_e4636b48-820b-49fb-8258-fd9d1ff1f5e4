import React, { useRef, useState, useEffect } from "react";
import Page from "../components/Page";
import {
  IconInfoCircle,
  IconInfoCircleFilled,
  IconMail,
  IconPhone,
  IconPlus,
  IconUser,
  IconX,
  IconLock,
  IconCheck,
  IconPencil,
  IconTrash,
  IconBuildingSkyscraper,
  IconSearch,
  IconNfc,
  IconCreditCard,
  IconLoader,
} from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { SCOPES } from "../config/scopes";
import { toast } from "react-hot-toast";
import { mutate } from "swr";
import { addNewUser, deleteUser, resetUserPassword, updateUser, useUsers } from "../controllers/users.controller";
import { validateEmail } from "../utils/emailValidator";
import FloorRestrictionsModal from "../components/FloorRestrictionsModal";
// NFC Kart durumları
const CARD_STATUS = {
  IDLE: "idle",
  WAITING_FOR_CARD: "waiting_for_card",
  READING: "reading",
  WRITING: "writing",
  SUCCESS: "success",
  ERROR: "error"
};
import { useFloors } from "../controllers/floors.controller";


export default function UsersPage() {
  const [state, setState] = useState({
    selectedScopes: [],
    selectedFloorIds: [],
    isActive: true,
    selectedUsername: null,
    searchTerm: "",
  });


  const nameRef = useRef();
  const usernameRef = useRef();
  const pinRef = useRef();
  const passwordRef = useRef();
  const phoneRef = useRef();
  const designationRef = useRef();
  const isActiveRef = useRef();

  const changePasswordModalUsernameRef = useRef();
  const changePasswordModalNewPasswordRef = useRef();

  const nameUpdateRef = useRef();
  const usernameUpdateRef = useRef();
  const pinUpdateRef = useRef();
  const phoneUpdateRef = useRef();
  const emailUpdateRef = useRef();
  const designationUpdateRef = useRef();
  const isActiveUpdateRef = useRef();

  const { APIURL, data: users, error, isLoading } = useUsers();
  const { data: floors, error: floorsError, isLoading: floorsLoading } = useFloors();

  // NFC Kart yazma state'leri
  const [nfcState, setNfcState] = useState({
    isModalOpen: false,
    selectedUser: null,
    cardStatus: CARD_STATUS.IDLE,
    isWriting: false
  });

  // NFC Kart okuma state'leri
  const [cardReadState, setCardReadState] = useState({
    isModalOpen: false,
    cardStatus: CARD_STATUS.IDLE,
    cardData: null,
    isReading: false
  });

  // NFC form ref'leri
  const nfcEmailRef = useRef();
  const nfcPasswordRef = useRef();
  const nfcPinRef = useRef();

  if (isLoading || floorsLoading) {
    return <Page>Loading...</Page>;
  }

  if (error || floorsError) {
    console.error(error || floorsError);
    return <Page>Error Loading details! Please try later!</Page>;
  }

  const { selectedScopes, selectedFloorIds, isActive, selectedUsername, searchTerm } = state;

  // ============================================================================
  // NFC YARDIMCI FONKSİYONLAR
  // ============================================================================

  // Kart durumu mesajları
  const getCardStatusMessage = (status) => {
    const messages = {
      [CARD_STATUS.IDLE]: "Hazır",
      [CARD_STATUS.WAITING_FOR_CARD]: "Kartı okuyucuya okutun...",
      [CARD_STATUS.READING]: "Karttan okunuyor...",
      [CARD_STATUS.WRITING]: "Karta yazılıyor...",
      [CARD_STATUS.SUCCESS]: "İşlem başarılı",
      [CARD_STATUS.ERROR]: "Hata oluştu"
    };
    return messages[status] || "Bilinmeyen durum";
  };

  // Kart durumu renkleri
  const getCardStatusColor = (status) => {
    const colors = {
      [CARD_STATUS.IDLE]: "text-gray-600",
      [CARD_STATUS.WAITING_FOR_CARD]: "text-blue-600",
      [CARD_STATUS.READING]: "text-purple-600",
      [CARD_STATUS.WRITING]: "text-orange-600",
      [CARD_STATUS.SUCCESS]: "text-green-600",
      [CARD_STATUS.ERROR]: "text-red-600"
    };
    return colors[status] || "text-gray-600";
  };

  // ============================================================================
  // GERÇEK NFC/RFID OKUYUCU FONKSİYONLARI
  // ============================================================================

  // Serial port bağlantısı
  let serialPort = null;
  let reader = null;

  // NFC okuyucuya bağlan
  const connectNFCReader = async () => {
    try {
      if (!('serial' in navigator)) {
        throw new Error('Web Serial API desteklenmiyor. Chrome/Edge kullanın.');
      }

      // Serial port seç
      serialPort = await navigator.serial.requestPort();
      await serialPort.open({
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none'
      });

      console.log('NFC okuyucu bağlandı');
      return true;
    } catch (error) {
      console.error('NFC okuyucu bağlantı hatası:', error);
      throw error;
    }
  };

  // Karttan veri oku
  const readNFCCard = async () => {
    try {
      if (!serialPort) {
        await connectNFCReader();
      }

      reader = serialPort.readable.getReader();

      // Okuma komutu gönder (ACR122U için)
      const writer = serialPort.writable.getWriter();
      const command = new Uint8Array([0xFF, 0xCA, 0x00, 0x00, 0x00]); // Get UID command
      await writer.write(command);
      writer.releaseLock();

      // Yanıt bekle
      const { value, done } = await reader.read();
      reader.releaseLock();

      if (done) {
        throw new Error('Okuma işlemi tamamlanamadı');
      }

      // Veriyi parse et
      const data = new TextDecoder().decode(value);
      console.log('Ham veri:', data);

      // JSON formatında veri varsa parse et
      try {
        const userData = JSON.parse(data);
        return userData;
      } catch {
        // Eğer JSON değilse, kart ID'si olarak kullan
        return {
          cardId: Array.from(value).map(b => b.toString(16).padStart(2, '0')).join(''),
          email: '',
          password: '',
          pin: '',
          username: 'Bilinmeyen Kart'
        };
      }
    } catch (error) {
      console.error('Kart okuma hatası:', error);
      throw error;
    }
  };

  // Karta veri yaz
  const writeNFCCard = async (userData) => {
    try {
      if (!serialPort) {
        await connectNFCReader();
      }

      const writer = serialPort.writable.getWriter();

      // Veriyi JSON string'e çevir
      const jsonData = JSON.stringify(userData);
      const dataBytes = new TextEncoder().encode(jsonData);

      // Yazma komutu (MIFARE Classic için)
      const writeCommand = new Uint8Array([
        0xFF, 0xD6, 0x00, 0x04, // Write Binary command
        dataBytes.length,
        ...dataBytes
      ]);

      await writer.write(writeCommand);
      writer.releaseLock();

      console.log('Veri karta yazıldı:', jsonData);
      return { success: true };
    } catch (error) {
      console.error('Kart yazma hatası:', error);
      throw error;
    }
  };

  // Web NFC API (Android Chrome için)
  const readWebNFC = async () => {
    try {
      if (!('NDEFReader' in window)) {
        throw new Error('Web NFC API desteklenmiyor');
      }

      const ndef = new NDEFReader();
      await ndef.scan();

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Kart okuma zaman aşımı'));
        }, 10000);

        ndef.addEventListener("reading", ({ message }) => {
          clearTimeout(timeout);

          for (const record of message.records) {
            if (record.recordType === "text") {
              const textDecoder = new TextDecoder(record.encoding);
              const data = textDecoder.decode(record.data);

              try {
                const userData = JSON.parse(data);
                resolve(userData);
              } catch {
                resolve({
                  cardId: data,
                  email: '',
                  password: '',
                  pin: '',
                  username: 'Web NFC Kart'
                });
              }
              return;
            }
          }

          reject(new Error('Kart verisi okunamadı'));
        });

        ndef.addEventListener("readingerror", () => {
          clearTimeout(timeout);
          reject(new Error('Kart okuma hatası'));
        });
      });
    } catch (error) {
      console.error('Web NFC okuma hatası:', error);
      throw error;
    }
  };

  // Web NFC ile yazma
  const writeWebNFC = async (userData) => {
    try {
      if (!('NDEFReader' in window)) {
        throw new Error('Web NFC API desteklenmiyor');
      }

      const ndef = new NDEFReader();
      await ndef.write({
        records: [{
          recordType: "text",
          data: JSON.stringify(userData)
        }]
      });

      return { success: true };
    } catch (error) {
      console.error('Web NFC yazma hatası:', error);
      throw error;
    }
  };

  // Ana kart okuma fonksiyonu (her iki yöntemi dener)
  const readCard = async () => {
    try {
      // Önce Web NFC dene (mobil için)
      if ('NDEFReader' in window) {
        return await readWebNFC();
      }
      // Sonra Serial API dene (PC için)
      else if ('serial' in navigator) {
        return await readNFCCard();
      }
      else {
        throw new Error('NFC/RFID okuyucu desteklenmiyor');
      }
    } catch (error) {
      console.error('Kart okuma hatası:', error);
      throw error;
    }
  };

  // Ana kart yazma fonksiyonu
  const writeCard = async (userData) => {
    try {
      // Önce Web NFC dene (mobil için)
      if ('NDEFReader' in window) {
        return await writeWebNFC(userData);
      }
      // Sonra Serial API dene (PC için)
      else if ('serial' in navigator) {
        return await writeNFCCard(userData);
      }
      else {
        throw new Error('NFC/RFID okuyucu desteklenmiyor');
      }
    } catch (error) {
      console.error('Kart yazma hatası:', error);
      throw error;
    }
  };

  // ============================================================================
  // NFC KART OKUMA FONKSİYONLARI
  // ============================================================================

  // Kart okuma modal açma
  const btnShowCardReadModal = () => {
    setCardReadState({
      isModalOpen: true,
      cardStatus: CARD_STATUS.IDLE,
      cardData: null,
      isReading: false
    });
    document.getElementById("modal-card-read").showModal();
  };

  // Kart okuma modal kapatma
  const btnCloseCardReadModal = () => {
    setCardReadState({
      isModalOpen: false,
      cardStatus: CARD_STATUS.IDLE,
      cardData: null,
      isReading: false
    });
    document.getElementById("modal-card-read").close();
  };

  // Kart okuma işlemi (GERÇEK)
  const btnReadCard = async () => {
    try {
      setCardReadState({
        ...cardReadState,
        isReading: true,
        cardStatus: CARD_STATUS.WAITING_FOR_CARD
      });

      toast.loading("Kartı okuyucuya okutun...");

      // GERÇEK kart okuma
      const cardData = await readCard();

      setCardReadState({
        ...cardReadState,
        cardStatus: CARD_STATUS.SUCCESS,
        cardData: cardData,
        isReading: false
      });

      toast.dismiss();
      toast.success("Kart başarıyla okundu!");

    } catch (error) {
      console.error("Kart okuma hatası:", error);

      setCardReadState({
        ...cardReadState,
        cardStatus: CARD_STATUS.ERROR,
        cardData: null,
        isReading: false
      });

      toast.dismiss();
      toast.error(`Kart okuma hatası: ${error.message}`);
    }
  };

  // ============================================================================
  // NFC KART YAZMA FONKSİYONLARI
  // ============================================================================

  // NFC modal açma
  const btnShowNfcModal = (user) => {
    setNfcState({
      ...nfcState,
      isModalOpen: true,
      selectedUser: user,
      cardStatus: CARD_STATUS.IDLE
    });

    // Form alanlarını kullanıcı bilgileriyle doldur
    setTimeout(() => {
      if (nfcEmailRef.current) nfcEmailRef.current.value = user.username || user.email || "";
      if (nfcPasswordRef.current) nfcPasswordRef.current.value = ""; // Güvenlik için boş
      if (nfcPinRef.current) nfcPinRef.current.value = user.pin || "";
    }, 100);

    document.getElementById("modal-nfc-write").showModal();
  };

  // NFC modal kapatma
  const btnCloseNfcModal = () => {
    setNfcState({
      ...nfcState,
      isModalOpen: false,
      selectedUser: null,
      cardStatus: CARD_STATUS.IDLE,
      isWriting: false
    });

    // Form alanlarını temizle
    if (nfcEmailRef.current) nfcEmailRef.current.value = "";
    if (nfcPasswordRef.current) nfcPasswordRef.current.value = "";
    if (nfcPinRef.current) nfcPinRef.current.value = "";

    document.getElementById("modal-nfc-write").close();
  };

  // Karta yazma işlemi - Kart okutulmasını bekle
  const btnWriteToCard = async () => {
    const email = nfcEmailRef.current?.value?.trim();
    const password = nfcPasswordRef.current?.value?.trim();
    const pin = nfcPinRef.current?.value?.trim();

    // Basit doğrulama
    if (!email) {
      toast.error("Email gereklidir");
      return;
    }
    if (!password) {
      toast.error("Şifre gereklidir");
      return;
    }
    if (!pin) {
      toast.error("PIN gereklidir");
      return;
    }

    try {
      // Kart okutulmasını bekle
      setNfcState({
        ...nfcState,
        isWriting: true,
        cardStatus: CARD_STATUS.CONNECTING
      });

      toast.loading("Kartı okuyucuya okutun...");

      // GERÇEK kart yazma işlemi
      setNfcState({
        ...nfcState,
        cardStatus: CARD_STATUS.WRITING
      });

      toast.dismiss();
      toast.loading("Karta yazılıyor...");

      // GERÇEK karta yazma
      await writeCard({
        email,
        password,
        pin,
        username: nfcState.selectedUser?.name,
        timestamp: new Date().toISOString()
      });

      setNfcState({
        ...nfcState,
        cardStatus: CARD_STATUS.SUCCESS,
        isWriting: false
      });

      toast.dismiss();
      toast.success("Kullanıcı bilgileri başarıyla karta yazıldı!");

      // 2 saniye sonra modalı kapat
      setTimeout(() => {
        btnCloseNfcModal();
      }, 2000);

    } catch (error) {
      console.error("NFC yazma hatası:", error);

      setNfcState({
        ...nfcState,
        cardStatus: CARD_STATUS.ERROR,
        isWriting: false
      });

      toast.dismiss();
      toast.error("Kart okuma/yazma işlemi başarısız");
    }
  };

  // Kart okutulmasını bekleyen fonksiyon
  const waitForCardRead = () => {
    return new Promise((resolve, reject) => {
      // Gerçek uygulamada burada NFC okuyucu kart bekleme eventi dinlenecek
      // Şimdilik 3-5 saniye arası rastgele bekleme simülasyonu
      const waitTime = Math.random() * 2000 + 3000; // 3-5 saniye

      setTimeout(() => {
        if (Math.random() > 0.1) { // %90 başarı oranı
          resolve();
        } else {
          reject(new Error("Kart okunamadı"));
        }
      }, waitTime);
    });
  };



  const btnAdd = async () => {
    const name = nameRef.current.value;
    const username = usernameRef.current.value;
    const password = passwordRef.current.value;
    const phone = phoneRef.current.value || null;
    const designation = designationRef.current.value || null;
    const isActive = isActiveRef.current.checked;

    const userScopes = selectedScopes;

    if (!name) {
      toast.error("Please provide name!");
      return;
    }
    if (!username) {
      toast.error("Please provide username!");
      return;
    }
    if (!password) {
      toast.error("Please provide password!");
      return;
    }
    if(!validateEmail(username)) {
      toast.error("Please provide valid email!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await addNewUser(
        username,
        password,
        name,
        designation,
        phone,
        null,
        userScopes,
        isActive,
        selectedFloorIds
      );

      if (res.status == 200) {
        nameRef.current.value = null;
        usernameRef.current.value = null;
        passwordRef.current.value = null;
        phoneRef.current.value = null;
        designationRef.current.value = null;
        isActiveRef.current.checked = true;

        await mutate(APIURL);

        setState({
          ...state,
          selectedScopes: [],
          selectedFloorIds: [],
          isActive: true
        });

        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  const btnDelete = async (username) => {
    const isConfirm = window.confirm("Emin misin! Bu süreç geri döndürülemez!");

    if(!isConfirm) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteUser(username);

      if(res.status == 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  const btnShowChangePassword = (username) => {
    changePasswordModalUsernameRef.current.value = username;
    document.getElementById("modal-reset-password").showModal()
  };

  const btnChangePassword = async () => {
    const username = changePasswordModalUsernameRef.current.value;
    const password = changePasswordModalNewPasswordRef.current.value;

    if(!(username)) {
      toast.error("Invalid Request!");
      return;
    }
    if(!(password)) {
      toast.error("Provide new password!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await resetUserPassword(
        username,
        password,
      );

      if (res.status == 200) {

        changePasswordModalUsernameRef.current.value = null;
        changePasswordModalNewPasswordRef.current.value = null;

        await mutate(APIURL);

        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  const btnShowUpdate = (username, name, pin, phone, scope, isUserActive = true) => {
    usernameUpdateRef.current.value = username;
    pinUpdateRef.current.value = pin;
    nameUpdateRef.current.value = name;
    phoneUpdateRef.current.value = phone;
    isActiveUpdateRef.current.checked = isUserActive;

    setState({
      ...state,
      selectedScopes: scope ? new String(scope).split(",") : [],
      selectedUsername: username
    });

    document.getElementById("modal-update").showModal();
  };

  const handleFloorRestrictionsClick = (username) => {
    setState({
      ...state,
      selectedUsername: username
    });
    document.getElementById("modal-floor-restrictions").showModal();
  };

  const handleFloorRestrictionsSuccess = (restrictedFloorIds) => {
    setState({
      ...state,
      selectedFloorIds: restrictedFloorIds
    });
  };

  const btnUpdate = async () => {
    const username = usernameUpdateRef.current.value;
    const name = nameUpdateRef.current.value;
    const pin = pinUpdateRef.current.value;
    const phone = phoneUpdateRef.current.value;
    const isActive = isActiveUpdateRef.current.checked;

    const userScopes = selectedScopes;

    if (!name) {
      toast.error("Please provide name!");
      return;
    }
    if (!username) {
      toast.error("Please provide username!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateUser(
        username,
        name,
        pin,
        phone,
        null,
        userScopes,
        isActive,
        selectedFloorIds
      );

      if (res.status == 200) {
        usernameUpdateRef.current.value = null;
        nameUpdateRef.current.value = null;
        pinUpdateRef.current.value = null;
        phoneUpdateRef.current.value = null;
        designationUpdateRef.current.value = null;
        isActiveUpdateRef.current.checked = true;

        await mutate(APIURL);

        setState({
          ...state,
          selectedScopes: [],
          selectedFloorIds: [],
          selectedUsername: null
        });

        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);

      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <Page>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-6">
          <h3 className="text-3xl font-light">Kullanıcılar</h3>
          <button
            onClick={btnShowCardReadModal}
            className="rounded-lg border bg-blue-50 hover:bg-blue-100 transition active:scale-95 hover:shadow-lg text-blue-600 px-2 py-1 flex items-center gap-1"
          >
            <IconCreditCard size={22} stroke={iconStroke} /> Kart Okuma
          </button>
          <button
            onClick={() => document.getElementById("modal-add").showModal()}
            className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
          >
            <IconPlus size={22} stroke={iconStroke} /> Yeni
          </button>
        </div>

        <div className="relative w-full md:w-64">
          <input
            type="text"
            placeholder="Kullanıcı ara..."
            value={searchTerm}
            onChange={(e) => setState({...state, searchTerm: e.target.value})}
            className="w-full border rounded-lg pl-10 pr-4 py-2 bg-gray-50 outline-restro-border-green-light text-sm"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <IconSearch size={18} stroke={iconStroke} />
          </div>
        </div>
      </div>

      <div className="mt-6 overflow-x-auto">
        {/* Kullanıcı sayısı bilgisi */}
        <div className="mb-2 text-sm text-gray-500">
          {searchTerm ? (
            <>
              <span className="font-medium">
                {users.filter(user => {
                  const searchTermLower = searchTerm.toLowerCase();
                  return (
                    (user.name && user.name.toLowerCase().includes(searchTermLower)) ||
                    (user.username && user.username.toLowerCase().includes(searchTermLower)) ||
                    (user.email && user.email.toLowerCase().includes(searchTermLower)) ||
                    (user.phone && user.phone.toLowerCase().includes(searchTermLower)) ||
                    (user.scope && user.scope.toLowerCase().includes(searchTermLower))
                  );
                }).length}
              </span> sonuç bulundu (toplam {users.length} kullanıcı)
            </>
          ) : (
            <>Toplam <span className="font-medium">{users.length}</span> kullanıcı</>
          )}
        </div>

        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 border-b">
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Kullanıcı</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">İletişim</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Yetkiler</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Durum</th>
              <th className="px-4 py-3 text-right text-sm font-medium text-gray-500">İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {users
              .filter(user => {
                if (!searchTerm) return true;
                const searchTermLower = searchTerm.toLowerCase();
                return (
                  (user.name && user.name.toLowerCase().includes(searchTermLower)) ||
                  (user.username && user.username.toLowerCase().includes(searchTermLower)) ||
                  (user.email && user.email.toLowerCase().includes(searchTermLower)) ||
                  (user.phone && user.phone.toLowerCase().includes(searchTermLower)) ||
                  (user.scope && user.scope.toLowerCase().includes(searchTermLower))
                );
              })
              .map((user, index) => {
              const {
                username,
                name,
                pin,
                role,
                designation,
                phone,
                email,
                scope,
                is_active,
              } = user;

              const userScopes = scope ? new String(scope).split(",") : [];
              const isUserActive = is_active !== undefined ? is_active : true;

              return (
                <tr
                  key={index}
                  className={`border-b hover:bg-gray-50 ${!isUserActive ? 'bg-red-50' : ''}`}
                >
                  {/* Kullanıcı Bilgileri */}
                  <td className="px-4 py-4">
                    <div className="flex items-center gap-3">
                      <div className="relative flex-shrink-0">
                        <div className="w-10 h-10 rounded-full flex items-center justify-center bg-gray-100 text-gray-400">
                          <IconUser size={20} stroke={iconStroke} />
                        </div>
                        {!isUserActive && (
                          <div className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-4 h-4 flex items-center justify-center">
                            <IconX size={10} stroke={2.5} />
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{name}</p>
                        <p className="text-xs text-gray-500">{username}</p>
                        <div className="flex items-center gap-1 mt-1">
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                            {role.toUpperCase()}
                          </span>
                          {designation && (
                            <span className="text-xs text-white bg-restro-green px-2 py-0.5 rounded-full">
                              {designation}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>

                  {/* İletişim Bilgileri */}
                  <td className="px-4 py-4">
                    <div className="text-sm">
                      {email && (
                        <p className="flex items-center gap-1 text-gray-500 text-xs">
                          <IconMail size={14} stroke={iconStroke} /> {email}
                        </p>
                      )}
                      {phone && (
                        <p className="flex items-center gap-1 text-gray-500 text-xs mt-1">
                          <IconPhone size={14} stroke={iconStroke} /> {phone}
                        </p>
                      )}
                    </div>
                  </td>

                  {/* Yetkiler */}
                  <td className="px-4 py-4">
                    <div className="flex flex-wrap gap-1">
                      {userScopes.length > 0 ? (
                        userScopes.map((s, i) => (
                          <div
                            key={i}
                            className="bg-gray-100 text-xs px-2 py-0.5 text-gray-500 rounded-full"
                          >
                            {s}
                          </div>
                        ))
                      ) : (
                        <span className="text-xs text-gray-400">Yetki yok</span>
                      )}
                    </div>
                  </td>

                  {/* Durum */}
                  <td className="px-4 py-4">
                    <div className="flex items-center">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          isUserActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {isUserActive ? 'Aktif' : 'Pasif'}
                      </span>
                    </div>
                  </td>

                  {/* İşlemler */}
                  <td className="px-4 py-4 text-right">
                    {role != "admin" && (
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={() => {
                            btnShowUpdate(username, name, pin, phone, scope, isUserActive);
                          }}
                          className="btn btn-xs btn-ghost text-gray-500"
                          title="Düzenle"
                        >
                          <IconPencil size={16} stroke={iconStroke} />
                        </button>
                        <button
                          onClick={() => {
                            btnShowChangePassword(username);
                          }}
                          className="btn btn-xs btn-ghost text-gray-500"
                          title="Şifre Sıfırla"
                        >
                          <IconLock size={16} stroke={iconStroke} />
                        </button>
                        <button
                          onClick={() => {
                            handleFloorRestrictionsClick(username);
                          }}
                          className="btn btn-xs btn-ghost text-gray-500"
                          title="Kat Kısıtlamaları"
                        >
                          <IconBuildingSkyscraper size={16} stroke={iconStroke} />
                        </button>
                        <button
                          onClick={() => {
                            btnShowNfcModal(user);
                          }}
                          className="btn btn-xs btn-ghost text-blue-500"
                          title="Karta Yükle"
                        >
                          <IconNfc size={16} stroke={iconStroke} />
                        </button>
                        <button
                          onClick={() => {
                            btnDelete(username);
                          }}
                          className="btn btn-xs btn-ghost text-red-500"
                          title="Kullanıcıyı Sil"
                        >
                          <IconTrash size={16} stroke={iconStroke} />
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* modal add */}
      <dialog id="modal-add" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Kullanıcı Ekle</h3>

          <div className="mt-4">
            <label htmlFor="name" className="mb-1 block text-gray-500 text-sm">
              Adı Soyadı <span className="text-xs text-gray-400">- (Zorunlu)</span>
            </label>
            <input
              ref={nameRef}
              type="text"
              name="name"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Ad ve soyadı giriniz..."
            />
          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label
                htmlFor="username"
                className="mb-1 block text-gray-500 text-sm"
              >
                E-posta{" "}
                <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={usernameRef}
                type="email"
                name="username"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Mail adresini giriniz..."
              />
            </div>
            <div className="flex-1">
              <label
                htmlFor="password"
                className="mb-1 block text-gray-500 text-sm"
              >
                Şifre{" "}
                <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={passwordRef}
                type="password"
                name="password"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Şifrenizi giriniz..."
              />
            </div>
          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label
                htmlFor="phone"
                className="mb-1 block text-gray-500 text-sm"
              >
                Telefon
              </label>
              <input
                ref={phoneRef}
                type="tel"
                name="phone"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Telefon numarasını giriniz..."
              />
            </div>
            <div className="flex-1">
              <label
                htmlFor="designation"
                className="mb-1 block text-gray-500 text-sm"
              >
                Detay
              </label>
              <input
                ref={designationRef}
                type="text"
                name="designation"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Detay griniz..."
              />
            </div>
          </div>

          <div className="flex gap-4 w-full mt-4">
            <div className="flex-1">
              <label
                htmlFor="scope"
                className="mb-1 text-gray-500 text-sm flex items-center gap-1"
              >
                Yetkiler
                <div
                  className="tooltip tooltip-right"
                  data-tip="Kullanıcıya vermek istediğiniz yetkileri seçiniz"
                >
                  <IconInfoCircleFilled
                    className="text-gray-400"
                    stroke={iconStroke}
                    size={14}
                  />
                </div>
              </label>
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    setState({
                      ...state,
                      selectedScopes: [
                        ...new Set([...selectedScopes, e.target.value]),
                      ],
                    });
                  }
                }}
                name="scope"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Select Scope"
              >
                <option value="">Yetkileri seçin ve ekleyin</option>
                {Object.values(SCOPES).map((s) => (
                  <option key={s} value={s}>
                    {s}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="my-4 flex items-center justify-between">
            <label htmlFor="isActive" className="text-gray-500 text-sm flex items-center gap-1">
              Kullanıcı Aktif Mi?
              <div
                className="tooltip tooltip-right"
                data-tip="Kullanıcının sisteme giriş yapabilmesi için aktif olması gerekir"
              >
                <IconInfoCircleFilled
                  className="text-gray-400"
                  stroke={iconStroke}
                  size={14}
                />
              </div>
            </label>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                ref={isActiveRef}
                type="checkbox"
                defaultChecked={true}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-restro-green peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
            </label>
          </div>

          <div className="flex flex-wrap gap-2 w-full mt-4">
            {selectedScopes.map((s, i) => (
              <div
                key={i}
                className="bg-gray-100 text-sm p-2 text-gray-400 rounded-full flex items-center gap-1"
              >
                {s}
                <button
                  onClick={() => {
                    setState({
                      ...state,
                      selectedScopes: selectedScopes.filter(
                        (scope) => scope != s
                      ),
                    });
                  }}
                  className="text-red-400"
                >
                  <IconX stroke={iconStroke} size={18} />
                </button>
              </div>
            ))}
          </div>

          <div className="modal-action mt-4">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                onClick={() => {
                  btnAdd();
                }}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* modal add */}

      {/* modal update */}
      <dialog id="modal-update" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Kullanıcı Güncelleme</h3>


          <div className="mt-4">
              <label
                htmlFor="username"
                className="mb-1 block text-gray-500 text-sm"
              >
                Kullanıcı Adı{" "}
                <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={usernameUpdateRef}
                disabled
                type="email"
                name="username"
                className=" cursor-not-allowed disabled:bg-gray-200 text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Kulalnıcı adını giriniz.."
              />
            </div>

          <div className="flex gap-4 w-full my-4">
          <div className="flex-1">
            <label htmlFor="name" className="mb-1 block text-gray-500 text-sm">
              Adı <span className="text-xs text-gray-400">- (Zorunlu)</span>
            </label>
            <input
              ref={nameUpdateRef}
              type="text"
              name="name"
              className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
              placeholder="Ad Soyad giriniz.."
            />
          </div>

          </div>

          <div className="flex gap-4 w-full my-4">
            <div className="flex-1">
              <label
                htmlFor="phone"
                className="mb-1 block text-gray-500 text-sm"
              >
                Telefon
              </label>
              <input
                ref={phoneUpdateRef}
                type="tel"
                name="phone"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Telefon numarasını giriniz.."
              />
            </div>
            <div className="flex-1">
              <label
                htmlFor="pin"
                className="mb-1 block text-gray-500 text-sm"
              >
                Pin
              </label>
              <input
                ref={pinUpdateRef}
                type="text"
                name="pin"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Detay grinizs..."
              />
            </div>
          </div>

          <div className="flex gap-4 w-full mt-4">
            <div className="flex-1">
              <label
                htmlFor="scope"
                className="mb-1 text-gray-500 text-sm flex items-center gap-1"
              >
                Yetki
                <div
                  className="tooltip tooltip-right"
                  data-tip="Kullanıcıya vermek istediğiniz yetkiler"
                >
                  <IconInfoCircleFilled
                    className="text-gray-400"
                    stroke={iconStroke}
                    size={14}
                  />
                </div>
              </label>
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    setState({
                      ...state,
                      selectedScopes: [
                        ...new Set([...selectedScopes, e.target.value]),
                      ],
                    });
                  }
                }}
                name="scope"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Select Scope"
              >
                <option value="">Vermek istediğiniz yetkileri seçin</option>
                {Object.values(SCOPES).map((s) => (
                  <option key={s} value={s}>
                    {s}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="my-4 flex items-center justify-between">
            <label htmlFor="isActiveUpdate" className="text-gray-500 text-sm flex items-center gap-1">
              Kullanıcı Aktif Mi?
              <div
                className="tooltip tooltip-right"
                data-tip="Kullanıcının sisteme giriş yapabilmesi için aktif olması gerekir"
              >
                <IconInfoCircleFilled
                  className="text-gray-400"
                  stroke={iconStroke}
                  size={14}
                />
              </div>
            </label>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                ref={isActiveUpdateRef}
                id="isActiveUpdate"
                type="checkbox"
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:bg-restro-green peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
            </label>
          </div>

          <div className="flex flex-wrap gap-2 w-full mt-4">
            {selectedScopes.map((s, i) => (
              <div
                key={i}
                className="bg-gray-100 text-sm p-2 text-gray-400 rounded-full flex items-center gap-1"
              >
                {s}
                <button
                  onClick={() => {
                    setState({
                      ...state,
                      selectedScopes: selectedScopes.filter(
                        (scope) => scope != s
                      ),
                    });
                  }}
                  className="text-red-400"
                >
                  <IconX stroke={iconStroke} size={18} />
                </button>
              </div>
            ))}
          </div>

          <div className="modal-action mt-4">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                onClick={() => {
                  btnUpdate();
                }}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* modal update */}

      {/* modal reset password */}
      <dialog id="modal-reset-password" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Şifre Sıfırla</h3>

          <div className="flex flex-col gap-4 w-full my-4">
            <div className="flex-1">
              <label
                htmlFor="username"
                className="mb-1 block text-gray-500 text-sm"
              >
                Kullanıcı Adı
              </label>
              <input
                ref={changePasswordModalUsernameRef}
                disabled
                type="text"
                name="username"
                className="cursor-not-allowed disabled:bg-gray-200 text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Enter Username here..."
              />
            </div>
            <div className="flex-1">
              <label
                htmlFor="password"
                className="mb-1 block text-gray-500 text-sm"
              >
                Şifre{" "}
                <span className="text-xs text-gray-400">- (Zorunlu)</span>
              </label>
              <input
                ref={changePasswordModalNewPasswordRef}
                type="password"
                name="password"
                className="text-sm w-full border rounded-lg px-4 py-2 bg-gray-50 outline-restro-border-green-light"
                placeholder="Şifreyi girniz..."
              />
            </div>
          </div>


          <div className="modal-action mt-4">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="rounded-lg hover:bg-gray-200 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-gray-200 text-gray-500">
                Kapat
              </button>
              <button
                onClick={() => {
                  btnChangePassword();
                }}
                className="rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white ml-3"
              >
                Kaydet
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* modal change password */}

      {/* Floor Restrictions Modal */}
      <FloorRestrictionsModal
        username={selectedUsername}
        floors={floors || []}
        onSuccess={handleFloorRestrictionsSuccess}
        APIURL={APIURL}
      />
      {/* Floor Restrictions Modal */}

      {/* NFC Kart Yazma Modal */}
      <dialog id="modal-nfc-write" className="modal">
        <div className="modal-box w-11/12 max-w-md">
          <h3 className="font-bold text-lg mb-4 flex items-center gap-2">
            <IconNfc size={24} className="text-blue-500" />
            Karta Yükle
          </h3>

          {nfcState.selectedUser && (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Kullanıcı:</p>
              <p className="font-medium">{nfcState.selectedUser.name}</p>
              <p className="text-sm text-gray-500">{nfcState.selectedUser.username}</p>
            </div>
          )}

          {/* Kart Durumu */}
          <div className="mb-4 p-3 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <IconCreditCard size={20} className={getCardStatusColor(nfcState.cardStatus)} />
              <span className="font-medium">Kart Durumu</span>
            </div>
            <p className={`text-sm ${getCardStatusColor(nfcState.cardStatus)}`}>
              {getCardStatusMessage(nfcState.cardStatus)}
            </p>
            {nfcState.cardStatus === CARD_STATUS.CONNECTING && (
              <div className="mt-2">
                <div className="flex items-center gap-2">
                  <IconLoader size={16} className="animate-spin text-blue-500" />
                  <span className="text-sm text-blue-600">Kartı okuyucuya okutun...</span>
                </div>
              </div>
            )}
            {nfcState.cardStatus === CARD_STATUS.WRITING && (
              <div className="mt-2">
                <div className="flex items-center gap-2">
                  <IconLoader size={16} className="animate-spin text-orange-500" />
                  <span className="text-sm text-orange-600">Karta yazılıyor, kartı çıkarmayın...</span>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            {/* Email */}
            <div>
              <label className="label">
                <span className="label-text">Email *</span>
              </label>
              <input
                ref={nfcEmailRef}
                type="email"
                placeholder="<EMAIL>"
                className="input input-bordered w-full"
                disabled={nfcState.isWriting}
              />
            </div>

            {/* Şifre */}
            <div>
              <label className="label">
                <span className="label-text">Şifre *</span>
              </label>
              <input
                ref={nfcPasswordRef}
                type="password"
                placeholder="Kullanıcı şifresi"
                className="input input-bordered w-full"
                disabled={nfcState.isWriting}
              />
              <div className="label">
                <span className="label-text-alt text-yellow-600">
                  ⚠️ Güvenlik için şifreyi manuel girin
                </span>
              </div>
            </div>

            {/* PIN */}
            <div>
              <label className="label">
                <span className="label-text">PIN *</span>
              </label>
              <input
                ref={nfcPinRef}
                type="text"
                placeholder="4-6 haneli PIN"
                maxLength="6"
                className="input input-bordered w-full"
                disabled={nfcState.isWriting}
              />
            </div>

            {/* Uyarı */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <IconInfoCircle size={16} className="text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Nasıl Kullanılır:</p>
                  <ul className="text-xs space-y-1">
                    <li>• Bilgileri kontrol edin</li>
                    <li>• "Karta Yükle" butonuna tıklayın</li>
                    <li>• Kartı okuyucuya okutun</li>
                    <li>• Sistem otomatik olarak karta yazacak</li>
                    <li>• Yazma sırasında kartı çıkarmayın</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="modal-action mt-6">
            <button
              onClick={btnCloseNfcModal}
              className="btn btn-ghost"
              disabled={nfcState.isWriting}
            >
              İptal
            </button>
            <button
              onClick={btnWriteToCard}
              className={`btn ${
                nfcState.cardStatus === CARD_STATUS.SUCCESS
                  ? "btn-success"
                  : "bg-blue-500 hover:bg-blue-600 text-white"
              }`}
              disabled={nfcState.isWriting || nfcState.cardStatus === CARD_STATUS.SUCCESS}
            >
              {nfcState.isWriting ? (
                <>
                  <IconLoader size={16} className="animate-spin" />
                  Yazılıyor...
                </>
              ) : nfcState.cardStatus === CARD_STATUS.SUCCESS ? (
                <>
                  <IconCheck size={16} />
                  Tamamlandı
                </>
              ) : (
                <>
                  <IconNfc size={16} />
                  Karta Yükle
                </>
              )}
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button onClick={btnCloseNfcModal}>close</button>
        </form>
      </dialog>
      {/* NFC Kart Yazma Modal */}

      {/* Kart Okuma Modal */}
      <dialog id="modal-card-read" className="modal">
        <div className="modal-box w-11/12 max-w-md">
          <h3 className="font-bold text-lg mb-4 flex items-center gap-2">
            <IconCreditCard size={24} className="text-blue-500" />
            Kart Okuma
          </h3>

          {/* Kart Durumu */}
          <div className="mb-4 p-3 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <IconCreditCard size={20} className={getCardStatusColor(cardReadState.cardStatus)} />
              <span className="font-medium">Durum</span>
            </div>
            <p className={`text-sm ${getCardStatusColor(cardReadState.cardStatus)}`}>
              {getCardStatusMessage(cardReadState.cardStatus)}
            </p>
            {cardReadState.cardStatus === CARD_STATUS.WAITING_FOR_CARD && (
              <div className="mt-2">
                <div className="flex items-center gap-2">
                  <IconLoader size={16} className="animate-spin text-blue-500" />
                  <span className="text-sm text-blue-600">Kartı okuyucuya okutun...</span>
                </div>
              </div>
            )}
          </div>

          {/* Okunan Kart Verileri */}
          {cardReadState.cardData && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">Okunan Veriler:</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-green-600 font-medium">Email:</span>
                  <span className="ml-2 text-green-800">{cardReadState.cardData.email}</span>
                </div>
                <div>
                  <span className="text-green-600 font-medium">Kullanıcı:</span>
                  <span className="ml-2 text-green-800">{cardReadState.cardData.username}</span>
                </div>
                <div>
                  <span className="text-green-600 font-medium">PIN:</span>
                  <span className="ml-2 text-green-800">{cardReadState.cardData.pin}</span>
                </div>
                <div>
                  <span className="text-green-600 font-medium">Şifre:</span>
                  <span className="ml-2 text-green-800">****** (gizli)</span>
                </div>
              </div>
            </div>
          )}

          {/* NFC Desteği ve Uyarı */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div className="flex items-start gap-2">
              <IconInfoCircle size={16} className="text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">NFC/RFID Desteği:</p>
                <ul className="text-xs space-y-1">
                  {('NDEFReader' in window) && <li>✅ Web NFC API (Android Chrome)</li>}
                  {('serial' in navigator) && <li>✅ Serial API (USB okuyucular)</li>}
                  {!('NDEFReader' in window) && !('serial' in navigator) && (
                    <li>❌ NFC desteği bulunamadı</li>
                  )}
                  <li>• "Kart Oku" butonuna tıklayın</li>
                  <li>• Kartı okuyucuya okutun</li>
                  <li>• Sistem GERÇEK okuyucu ile okuyacak</li>
                  <li>• Okunan veriler ekranda görünecek</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="modal-action">
            <button
              onClick={btnCloseCardReadModal}
              className="btn btn-ghost"
              disabled={cardReadState.isReading}
            >
              Kapat
            </button>
            <button
              onClick={btnReadCard}
              className={`btn ${
                cardReadState.cardStatus === CARD_STATUS.SUCCESS
                  ? "btn-success"
                  : "bg-blue-500 hover:bg-blue-600 text-white"
              }`}
              disabled={cardReadState.isReading || cardReadState.cardStatus === CARD_STATUS.SUCCESS}
            >
              {cardReadState.isReading ? (
                <>
                  <IconLoader size={16} className="animate-spin" />
                  Okunuyor...
                </>
              ) : cardReadState.cardStatus === CARD_STATUS.SUCCESS ? (
                <>
                  <IconCheck size={16} />
                  Okundu
                </>
              ) : (
                <>
                  <IconCreditCard size={16} />
                  Kart Oku
                </>
              )}
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button onClick={btnCloseCardReadModal}>close</button>
        </form>
      </dialog>
      {/* Kart Okuma Modal */}
    </Page>
  );
}
