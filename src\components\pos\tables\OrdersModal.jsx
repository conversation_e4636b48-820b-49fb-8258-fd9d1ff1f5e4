import React from 'react';
import { IconX, IconPlus } from "@tabler/icons-react";
import { iconStroke } from "../../../config/config";

export const OrdersModal = ({ 
  table, 
  tableOrders, 
  timers,
  onClose,
  onAddProduct 
}) => {
  // tableOrders kontrol ediliyor
  if (!tableOrders) return null;

  return (
    <dialog id={`orders-modal-${table.id}`} className="modal">
      <div className="modal-box max-w-2xl bg-white">
        <div className="flex items-center justify-between mb-6 pb-4 border-b">
          <div>
            <h3 className="text-xl font-bold text-gray-800">{table.table_title}</h3>
          </div>
          <div className="flex gap-2">
            <button 
              onClick={onAddProduct}
              className="px-3 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg flex items-center gap-2 transition-colors">
                <IconPlus size={18} /> <PERSON><PERSON><PERSON><PERSON>
            </button>
            <form method="dialog">
              <button 
                onClick={onClose}
                className="w-8 h-8 rounded-lg flex items-center justify-center hover:bg-gray-100 transition-colors"
              >
                <IconX size={18} className="text-gray-500" />
              </button>
            </form>
          </div>
        </div>

        <div className="space-y-4">
          {tableOrders.orders?.map((order, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-xl border border-gray-100">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center gap-3">
                  <span className="font-medium text-gray-700">Token: #{order.token_no}</span>
                  {timers[table.id] && (
                    <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
                      {timers[table.id]}
                    </span>
                  )}
                </div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium 
                  ${order.payment_status === 'paid' 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-amber-100 text-amber-700'}`}>
                  {order.payment_status === 'paid' ? 'Ödendi' : 'Ödenmedi'}
                </span>
              </div>

              <div className="space-y-2">
                {order.items?.map((item, idx) => (
                  <div key={idx} className="py-2 border-t border-gray-200">
                    <div className="flex justify-between text-gray-700">
                      <span>{item.item_title}</span>
                      <span className="font-medium">x{item.quantity}</span>
                    </div>
                    {item.notes && (
                      <p className="text-sm text-gray-500 mt-1">Not: {item.notes}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button>Kapat</button>
      </form>
    </dialog>
  );
};

export default OrdersModal;