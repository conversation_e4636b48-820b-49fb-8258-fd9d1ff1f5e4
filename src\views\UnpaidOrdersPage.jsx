import React, { useState, useRef } from "react";
import Page from "../components/Page";
import { IconCalendar, IconCash, IconUser, IconPhone, IconClipboardList, IconArrowLeft, IconFilter } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { CURRENCIES } from "../config/currencies.config";
import { useNavigate } from "react-router-dom";
import { useUnpaidOrders } from "../controllers/unpaid-orders.controller";

// Sayısal değerleri formatlamak için yardımcı fonksiyon
const formatNumber = (value) => {
  if (!value) return "0";
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return numValue.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// Tarih formatlamak için yardımcı fonksiyon (UTC'den Türkiye saatine çevir)
const formatDateTime = (dateString) => {
  if (!dateString) return "";
  // DB'den gelen UTC tarihini Türkiye saatine çevir
  const utcDate = new Date(dateString + (dateString.includes('Z') ? '' : 'Z')); // UTC olarak parse et
  return utcDate.toLocaleString('tr-TR', {
    timeZone: 'Europe/Istanbul',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export default function UnpaidOrdersPage() {
  const navigate = useNavigate();
  const fromDateRef = useRef();
  const toDateRef = useRef();
  const fromTimeRef = useRef();
  const toTimeRef = useRef();
  const filterTypeRef = useRef();

  const filters = [
    { key: "today", value: "Bugün" },
    { key: "yesterday", value: "Dün" },
    { key: "custom", value: "Özel Tarih" }
  ];

  // Varsayılan tarih ve saat değerleri (Türkiye saat dilimi)
  const now = new Date();
  const turkeyDate = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Istanbul"}));
  const defaultDateFrom = `${turkeyDate.getFullYear()}-${(turkeyDate.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${turkeyDate.getDate().toString().padStart(2, "0")}`;
  const defaultDateTo = defaultDateFrom;
  const defaultTimeFrom = "00:00";
  const defaultTimeTo = "23:59";

  const [state, setState] = useState({
    filter: filters[0].key,
    fromDate: null,
    toDate: null,
    fromTime: null,
    toTime: null
  });

  // Açık borçları getir
  const { data, error, isLoading } = useUnpaidOrders({
    type: state.filter,
    from: state.fromDate && state.filter === 'custom' ? `${state.fromDate} ${state.fromTime || defaultTimeFrom}:00` : null,
    to: state.toDate && state.filter === 'custom' ? `${state.toDate} ${state.toTime || defaultTimeTo}:00` : null,
  });

  // Loading ve error handling
  if (isLoading) {
    return (
      <Page>
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/dashboard/reports')}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <IconArrowLeft size={20} stroke={iconStroke} />
              </button>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <IconCash stroke={iconStroke} />
                Açık Borç Bulucu
              </h1>
            </div>
          </div>
          <div className="text-center py-8">
            <span className="loading loading-spinner loading-lg"></span>
            <p className="mt-4">Lütfen bekleyin...</p>
          </div>
        </div>
      </Page>
    );
  }

  if (error) {
    console.error(error);
    return (
      <Page>
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/dashboard/reports')}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <IconArrowLeft size={20} stroke={iconStroke} />
              </button>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <IconCash stroke={iconStroke} />
                Açık Borç Bulucu
              </h1>
            </div>
          </div>
          <div className="text-center py-8">
            <p className="text-red-600">Açık borç verileri yüklenirken hata oluştu. Lütfen daha sonra deneyin!</p>
          </div>
        </div>
      </Page>
    );
  }

  // Data destructuring
  const {
    summary,
    all_orders
  } = data || {};

  const currency = CURRENCIES.find(c => c.cc === 'TRY')?.symbol || '₺';

  return (
    <Page>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/reports')}
              className="btn btn-sm btn-circle btn-ghost"
            >
              <IconArrowLeft size={20} stroke={iconStroke} />
            </button>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <IconCash stroke={iconStroke} />
              Açık Borç Bulucu
            </h1>
          </div>
        </div>

        {/* Arama Filtreleri */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Arama Filtreleri</h2>
            <button
              onClick={() => document.getElementById("filter-dialog").showModal()}
              className="btn btn-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
            >
              <IconFilter stroke={iconStroke} />
              Filtre
            </button>
          </div>

          <div className="text-center text-gray-600">
            <p>{filters.find(f => f.key === state.filter)?.value} tarihli açık borçlar gösteriliyor</p>
          </div>
        </div>

        {/* Sonuçlar */}
        {data && (
          <div className="space-y-6">
            {/* Özet Bilgiler */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="font-semibold text-red-800 mb-2">Toplam Açık Sipariş</h3>
                <p className="text-3xl font-bold text-red-600">
                  {summary?.total_unpaid_orders || 0}
                </p>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h3 className="font-semibold text-orange-800 mb-2">Toplam Borç</h3>
                <p className="text-3xl font-bold text-orange-600">
                  {formatNumber(summary?.total_debt_amount)} {currency}
                </p>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="font-semibold text-blue-800 mb-2">Sipariş Değeri</h3>
                <p className="text-3xl font-bold text-blue-600">
                  {formatNumber(summary?.total_order_value)} {currency}
                </p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="font-semibold text-green-800 mb-2">Ödenen Tutar</h3>
                <p className="text-3xl font-bold text-green-600">
                  {formatNumber(summary?.total_paid_amount)} {currency}
                </p>
              </div>
            </div>

            {/* İstatistik Kartları */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold mb-4">Sipariş İstatistikleri</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{all_orders?.length || 0}</div>
                  <div className="text-sm text-blue-800">Toplam Sipariş</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {all_orders?.filter(order => order.delivery_type === 'dinein').length || 0}
                  </div>
                  <div className="text-sm text-green-800">Masa Siparişi</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {all_orders?.filter(order => order.delivery_type === 'takeaway').length || 0}
                  </div>
                  <div className="text-sm text-purple-800">Paket Siparişi</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {all_orders?.filter(order => order.delivery_type === 'delivery').length || 0}
                  </div>
                  <div className="text-sm text-orange-800">Teslimat</div>
                </div>
              </div>
            </div>

            {/* Detaylı Sipariş Listesi */}
            {all_orders && Array.isArray(all_orders) && all_orders.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <IconClipboardList size={20} stroke={iconStroke} />
                    Detaylı Sipariş Listesi ({all_orders.length} sipariş)
                  </h3>
                </div>

                <div className="divide-y">
                  {all_orders.map((order, index) => (
                    <div key={order.order_id || index} className="p-6 hover:bg-gray-50">
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Sol Kolon - Temel Bilgiler */}
                        <div>
                          <div className="flex items-center gap-2 mb-3">
                            <IconCalendar size={18} stroke={iconStroke} className="text-gray-500" />
                            <span className="text-sm text-gray-600">
                              {formatDateTime(order.order_date)}
                            </span>
                          </div>

                          <div className="flex items-center gap-2 mb-3">
                            {order.delivery_type === 'dinein' ? (
                              <IconCash size={18} stroke={iconStroke} className="text-blue-500" />
                            ) : (
                              <IconUser size={18} stroke={iconStroke} className="text-green-500" />
                            )}
                            <span className="font-medium text-lg">
                              {order.table_title || order.customer_name || 'Bilinmeyen'}
                            </span>
                          </div>

                          {order.customer_phone && (
                            <div className="flex items-center gap-2 mb-3">
                              <IconPhone size={18} stroke={iconStroke} className="text-gray-500" />
                              <span className="text-gray-600">{order.customer_phone}</span>
                            </div>
                          )}

                          {order.customer_email && (
                            <div className="flex items-center gap-2 mb-3">
                              <IconUser size={16} stroke={iconStroke} className="text-gray-500" />
                              <span className="text-sm text-gray-600">{order.customer_email}</span>
                            </div>
                          )}

                          <div className="text-sm text-gray-500 space-y-1">
                            <div>Sipariş #{order.order_id}</div>
                            {order.invoice_number && (
                              <div>Fatura: {order.invoice_number}</div>
                            )}
                            {order.floor_title && (
                              <div>Kat: {order.floor_title}</div>
                            )}
                          </div>
                        </div>

                        {/* Orta Kolon - Finansal Bilgiler */}
                        <div>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600 text-sm">Brüt Tutar:</span>
                              <span className="font-medium">{formatNumber(order.order_total_gross || order.total_amount)} {currency}</span>
                            </div>
                            {order.discount_amount > 0 && (
                              <div className="flex justify-between items-center">
                                <span className="text-gray-600 text-sm">İndirim:</span>
                                <span className="text-orange-600">-{formatNumber(order.discount_amount)} {currency}</span>
                              </div>
                            )}
                            {order.tax_amount > 0 && (
                              <div className="flex justify-between items-center">
                                <span className="text-gray-600 text-sm">KDV:</span>
                                <span className="text-gray-700">{formatNumber(order.tax_amount)} {currency}</span>
                              </div>
                            )}
                            <div className="flex justify-between items-center border-t pt-2">
                              <span className="text-gray-600">Net Tutar:</span>
                              <span className="font-medium text-lg">{formatNumber(order.order_total)} {currency}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Ödenen:</span>
                              <span className="text-green-600 font-medium">{formatNumber(order.total_paid)} {currency}</span>
                            </div>
                            <div className="flex justify-between items-center border-t pt-2">
                              <span className="font-medium text-red-600">Kalan Borç:</span>
                              <span className="font-bold text-red-600 text-xl">{formatNumber(order.remaining_debt)} {currency}</span>
                            </div>
                          </div>

                          <div className="mt-4 space-y-2">
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                              order.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              order.payment_status === 'partial' ? 'bg-orange-100 text-orange-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {order.payment_status === 'pending' ? 'Ödeme Bekliyor' :
                               order.payment_status === 'partial' ? 'Kısmi Ödendi' : 'Ödenmedi'}
                            </span>

                            <div className="text-xs text-gray-500 space-y-1">
                              <div>Ürün Sayısı: {order.total_items_count || 0}</div>
                              {order.payment_transactions_count > 0 && (
                                <div>Ödeme İşlemi: {order.payment_transactions_count}</div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Sağ Kolon - Sipariş ve Ödeme Detayları */}
                        <div className="space-y-4">
                          {/* Sipariş İçeriği */}
                          <div>
                            <div className="text-gray-600 mb-2 font-medium">Sipariş İçeriği:</div>
                            <div className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg max-h-24 overflow-y-auto">
                              {order.order_items || 'Ürün bilgisi bulunamadı'}
                            </div>
                          </div>

                          {/* Ödeme Detayları */}
                          {order.payment_details && (
                            <div>
                              <div className="text-gray-600 mb-2 font-medium">Ödeme Detayları:</div>
                              <div className="text-sm text-gray-700 bg-green-50 p-3 rounded-lg max-h-20 overflow-y-auto">
                                {order.payment_details}
                              </div>
                            </div>
                          )}

                          {/* Personel Bilgileri */}
                          <div className="text-xs text-gray-500 space-y-1">
                            {order.created_by_name && (
                              <div>Sipariş Alan: {order.created_by_name}</div>
                            )}
                            {order.cashier_name && (
                              <div>Kasiyer: {order.cashier_name}</div>
                            )}
                            {order.cash_register_title && (
                              <div>Kasa: {order.cash_register_title}</div>
                            )}
                          </div>

                          {/* Notlar */}
                          {order.order_notes && (
                            <div>
                              <div className="text-gray-600 mb-1 font-medium text-sm">Notlar:</div>
                              <div className="text-sm text-gray-700 bg-yellow-50 p-2 rounded border-l-4 border-yellow-400">
                                {order.order_notes}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Veri yoksa mesaj */}
            {data && (!all_orders || all_orders.length === 0) && (
              <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
                <IconClipboardList size={48} stroke={iconStroke} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-600 mb-2">Açık Borç Bulunamadı</h3>
                <p className="text-gray-500">Seçilen tarih aralığında açık borç bulunmuyor.</p>
              </div>
            )}
          </div>
        )}

        {/* İlk yükleme mesajı */}
        {!data && !isLoading && (
          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <IconCash size={48} stroke={iconStroke} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">Açık Borç Bulucu</h3>
            <p className="text-gray-500">Açık borçları görüntülemek için yukarıdaki filtreleri kullanarak arama yapın.</p>
          </div>
        )}

        {/* Filter Dialog */}
        <dialog id="filter-dialog" className="modal">
          <div className="modal-box w-11/12 max-w-md">
            <h3 className="font-bold text-lg flex items-center gap-2">
              <IconFilter stroke={iconStroke} /> Filtre
            </h3>

            <div className="my-4">
              <div>
                <label className="block text-gray-500 text-sm">Filtre</label>
                <select
                  className="select select-sm select-bordered w-full text-sm"
                  ref={filterTypeRef}
                >
                  {filters.map((filter, index) => (
                    <option key={index} value={filter.key}>
                      {filter.value}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 mt-4">
                <div className="flex-1">
                  <label className="block text-gray-500 text-sm">
                    Başlangıç Tarihi
                  </label>
                  <input
                    defaultValue={defaultDateFrom}
                    type="date"
                    ref={fromDateRef}
                    className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-gray-500 text-sm">
                    Başlangıç Saati
                  </label>
                  <input
                    defaultValue={defaultTimeFrom}
                    type="time"
                    ref={fromTimeRef}
                    className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                  />
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 mt-4">
                <div className="flex-1">
                  <label className="block text-gray-500 text-sm">
                    Bitiş Tarihi
                  </label>
                  <input
                    defaultValue={defaultDateTo}
                    type="date"
                    ref={toDateRef}
                    className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-gray-500 text-sm">
                    Bitiş Saati
                  </label>
                  <input
                    defaultValue={defaultTimeTo}
                    type="time"
                    ref={toTimeRef}
                    className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                  />
                </div>
              </div>
            </div>

            <div className="modal-action">
              <form method="dialog" className="flex w-full gap-2">
                <button className="btn btn-sm flex-1">Kapat</button>
                <button
                  onClick={() => {
                    setState({
                      ...state,
                      filter: filterTypeRef.current.value,
                      fromDate: fromDateRef.current.value || null,
                      toDate: toDateRef.current.value || null,
                      fromTime: fromTimeRef.current.value || null,
                      toTime: toTimeRef.current.value || null,
                    });
                  }}
                  className="btn btn-sm flex-1 bg-restro-primary text-black"
                >
                  Uygula
                </button>
              </form>
            </div>
          </div>
        </dialog>
      </div>
    </Page>
  );
}
