import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

export function useFloorPrinterMappings() {
  const APIURL = "/printer-mapping";
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);

  return {
    mappings: data || [],
    isLoading,
    isError: error,
    mutate,
    APIURL,
  };
}

export async function addFloorPrinterMapping(floorId, categoryId, printerId, printerType) {
  try {
    const response = await ApiClient.post("/printer-mapping/add", {
      floorId,
      categoryId,
      printerId,
      printerType
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateFloorPrinterMapping(mappingId, floorId, categoryId, printerId, printerType) {
  try {
    const response = await ApiClient.put(`/printer-mapping/update/${mappingId}`, {
      floorId,
      categoryId,
      printerId,
      printerType
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteFloorPrinterMapping(mappingId) {
  try {
    const response = await ApiClient.delete(`/printer-mapping/delete/${mappingId}`);
    return response;
  } catch (error) {
    throw error;
  }
}
