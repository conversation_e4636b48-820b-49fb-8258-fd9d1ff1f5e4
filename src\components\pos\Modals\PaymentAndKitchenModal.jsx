import React from 'react';
import { IconX } from "@tabler/icons-react";

const PaymentAndKitchenModal = ({
  itemsTotal,
  taxTotal,
  payableTotal,
  currency,
  paymentTypes,
  selectedPaymentType,
  onPaymentTypeChange,
  onConfirmPayment,
  paymentIcons
}) => {
  return (
    <dialog id="modal-pay-and-send-kitchen-summary" className="modal modal-bottom sm:modal-middle">
      <div className="modal-box">
        <div className="flex items-center justify-between">
          <h3 className="font-bold text-lg">Ödeme Al ve Siparişi Gönder</h3>
          <form method='dialog'>
            <button className="hover:bg-red-100 border-none transition active:scale-95 bg-red-50 text-red-500 btn btn-sm btn-circle">
              <IconX size={18} stroke={1.8} />
            </button>
          </form>
        </div>

        {/* Summary Section */}
        <div className="my-6 flex items-center justify-center divide-x w-full">
          <div className="flex-1 text-center">
            <p>Ürünler Toplamı</p>
            <p className="text-lg">{itemsTotal.toFixed(2)} {currency}</p>
          </div>
          <div className="flex-1 text-center">
            <p>KDV</p>
            <p className="text-lg">+{taxTotal.toFixed(2)} {currency}</p>
          </div>
          <div className="flex-1 text-center">
            <p>Ödenecek Tutar</p>
            <p className="text-lg font-bold text-restro-green">{payableTotal.toFixed(2)} {currency}</p>
          </div>
        </div>

        {/* Payment Types Grid */}
        <div className="grid grid-cols-3 gap-2">
          {paymentTypes.map((paymentType, i) => (
            <label key={i}>
              <input 
                checked={selectedPaymentType == paymentType?.id}
                onChange={(e) => onPaymentTypeChange(e.target.value)}
                type="radio" 
                name="payment_type" 
                id={paymentType?.icon} 
                value={paymentType?.id} 
                className='peer hidden' 
              />
              <label 
                htmlFor={paymentType?.icon} 
                className='border rounded-2xl flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-restro-green peer-checked:text-restro-green peer-checked:font-bold cursor-pointer transition'
              >
                {paymentType?.icon && paymentIcons[paymentType?.icon] && (
                  <div>{paymentIcons[paymentType?.icon]}</div>
                )}
                <p className='text-xs'>{paymentType.title}</p>
              </label>
            </label>
          ))}
        </div>

        {/* Confirm Button */}
        <div className="modal-action flex items-center justify-center w-full">
          <form method="dialog" className='w-full'>
            <button 
              onClick={onConfirmPayment} 
              className="w-full text-center rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-4 py-3 bg-restro-green text-white"
            >
              Ödemeyi Onayla ve Siparişi Gönder
            </button>
          </form>
        </div>
      </div>
    </dialog>
  );
};

export default PaymentAndKitchenModal;