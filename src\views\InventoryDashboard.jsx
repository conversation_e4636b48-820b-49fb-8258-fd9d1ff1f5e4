import React, { useRef, useState } from "react";
import Page from "../components/Page";
import { Icon<PERSON><PERSON><PERSON>, IconArrowUpRight, IconArrowDownLeft, IconSearch, IconBox, IconAlertTriangle, IconCircleMinus, IconCopyMinus } from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { Link } from "react-router-dom";
import clsx from "clsx";
import { useInventoryDashboard } from "../controllers/inventory.controller";

export default function InventoryDashboardPage() {
  const filters = [
    { key: "today", value: "Bugün" },
    { key: "yesterday", value: "Dün" },
    { key: "last_7days", value: "Son 7 Gün" },
    { key: "this_month", value: "Bu Ay" },
    { key: "last_month", value: "Geçen Ay" },
    { key: "custom", value: "Özel" },
  ];

  const fromDateRef = useRef();
  const toDateRef = useRef();
  const filterTypeRef = useRef();

  const now = new Date();
  const defaultDateFrom = `${now.getFullYear()}-${(now.getMonth() + 1)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;
  const defaultDateTo = `${now.getFullYear()}-${(now.getMonth() + 2)
    .toString()
    .padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;

  const [state, setState] = useState({
    filter: filters[0].key,
    fromDate: null,
    toDate: null,
    warehouseId: "",
    search: ""
  });

  const { APIURL, data, error, isLoading } = useInventoryDashboard({
    type: state.filter,
    from: state.fromDate,
    to: state.toDate,
  });

  if (isLoading) {
    return <Page>Yükleniyor...</Page>;
  }

  if (error) {
    return <Page>Veri yüklenirken bir hata oluştu!</Page>;
  }

  const {
    cummulativeInventoryMovements,
    inventoryUsageVSCurrentStock
  } = data;

  return (
    <Page>
      <div className="breadcrumbs text-sm mb-1">
        <ul>
          <li>
            <Link to="/dashboard/inventory">Envanter</Link>
          </li>
          <li>Panel</li>
        </ul>
      </div>

      <div className="flex flex-col md:flex-row md:items-center gap-2">
        <h3 className="text-2xl flex-1">Envanter Paneli</h3>
        <div className="relative md:w-80">
          <IconSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="search"
            className="input input-sm input-bordered focus:outline-none focus:ring-1 focus-within:ring-gray-200 transition pl-8 pr-2 w-full rounded-lg text-gray-500 py-4 h-8"
            placeholder="Ara..."
            value={state.search}
            onChange={(e) => {
              setState({
                ...state,
                search: e.target.value,
              });
            }}
          />
        </div>
        <button
          onClick={() => document.getElementById("filter-dialog").showModal()}
          className="btn btn-sm rounded-lg bg-restro-green-light w-fit"
        >
          <IconFilter stroke={iconStroke} /> Filtreler
        </button>
      </div>

      <h3 className="mt-1 text-gray-500 text-base">
        Gösterilen veriler: {filters.find((f) => f.key == state.filter).value}
      </h3>

      <div className="grid grid-cols-1 lg:grid-cols-1 gap-4 mt-6">
        {/* Top Moving Inventory Items */}
        <div className="border border-restro-border-green-light rounded-3xl h-[calc(100vh-220px)]">
          <div className="pt-5 px-4 bg-white/80 backdrop-blur rounded-t-3xl sticky top-0 z-10 font-bold">
            Kümülatif Envanter Hareketleri
          </div>

          <div className="mt-4 h-[calc(100%-60px)] overflow-auto">
            {cummulativeInventoryMovements?.filter((item) => {
              if (!state.search) {
                return true;
              }
              return new String(item.title).trim().toLowerCase().includes(state.search.trim().toLowerCase());
            })?.length > 0 ? (
              <table className="table table-sm w-full text-xs lg:text-sm">
                <thead className="bg-gray-50 text-gray-600 px-4 sticky top-0 z-10">
                  <tr>
                    <th>No</th>
                    <th>Ürün</th>
                    <th className="text-green-600">Giriş</th>
                    <th className="text-red-600">Çıkış</th>
                    <th className="text-yellow-600">Fire</th>
                  </tr>
                </thead>
                <tbody>
                  {cummulativeInventoryMovements.filter((item) => {
                    if (!state.search) {
                      return true;
                    }
                    return new String(item.title).trim().toLowerCase().includes(state.search.trim().toLowerCase());
                  })?.map((item, index) => (
                    <tr key={item.inventory_item_id}>
                      <td>{index + 1}</td>
                      <td>{item.title}</td>
                      <td className="text-green-600">
                        <div className="flex gap-1 items-center">
                          <IconArrowDownLeft stroke={iconStroke} size={18} />
                          {item.total_in} {item.unit}
                        </div>
                      </td>
                      <td className="text-red-600">
                        <div className="flex gap-1 items-center">
                          <IconArrowUpRight stroke={iconStroke} size={18} />
                          {item.total_out} {item.unit}
                        </div>
                      </td>
                      <td className="text-yellow-600">
                        <div className="flex gap-1 items-center">
                          {item.total_wastage > 0 && <IconCircleMinus stroke={iconStroke} size={18} />}
                          {item.total_wastage > 0 ? `${item.total_wastage} ${item.unit}` : "Yok"}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="flex flex-col items-center justify-center py-10 text-gray-400 text-sm mt-8">
                <img
                  src="/assets/illustrations/top-selling-not-found.webp"
                  alt="Veri bulunamadı"
                  className="w-1/4 mx-auto mb-2"
                />
                <span>Envanter hareketi bulunamadı</span>
              </div>
            )}
          </div>
        </div>

        {/* Usage vs Current Stock */}
        <div className="border border-restro-border-green-light rounded-3xl h-[calc(100vh-220px)]">
          <div className="pt-5 px-4 bg-white/80 backdrop-blur rounded-t-3xl sticky top-0 z-10 font-bold">
            Kullanım vs Mevcut Stok
          </div>

          <div className="mt-4 h-[calc(100%-60px)] overflow-auto">
            {inventoryUsageVSCurrentStock?.filter((item) => {
              if (!state.search) {
                return true;
              }
              return new String(item.title).trim().toLowerCase().includes(state.search.trim().toLowerCase());
            }).length > 0 ? (
              <table className="table table-sm w-full text-xs lg:text-sm">
                <thead className="bg-gray-50 text-gray-600 sticky top-0 z-10">
                  <tr>
                    <th>No</th>
                    <th>Ürün</th>
                    <th className="text-red-600">Kullanılan</th>
                    <th className="text-green-600">Mevcut Stok</th>
                    <th className="text-yellow-600">Min. Miktar</th>
                    <th>Durum</th>
                  </tr>
                </thead>
                <tbody>
                  {inventoryUsageVSCurrentStock.filter((item) => {
                    if (!state.search) {
                      return true;
                    }
                    return new String(item.title).trim().toLowerCase().includes(state.search.trim().toLowerCase());
                  }).map((item, index) => {
                    return (
                      <tr key={index}>
                        <td>{index + 1}</td>
                        <td>{item.title}</td>
                        <td className="text-red-600">
                          <div className="flex gap-1 items-center">
                            <IconCopyMinus stroke={iconStroke} size={18} />
                            {item.total_usage} {item.unit}
                          </div>
                        </td>
                        <td className="text-green-600">
                          <div className="flex gap-1 items-center">
                            <IconBox stroke={iconStroke} size={18} />
                            {item.current_stock} {item.unit}
                          </div>
                        </td>
                        <td className="text-yellow-600 flex gap-1 items-center">
                          <IconAlertTriangle stroke={iconStroke} size={16} />
                          {Number(item.min_quantity_threshold).toFixed(2)} {item.unit}
                        </td>
                        <td>
                          <span
                            className={clsx(
                              "px-2 py-1 text-xs font-medium rounded-lg",
                              item.status === "in" && "bg-green-50 text-green-600",
                              item.status === "low" && "bg-yellow-50 text-yellow-600",
                              item.status === "out" && "bg-red-50 text-red-600"
                            )}
                          >
                            {item.status === "in" && "Stokta"}
                            {item.status === "low" && "Stok Az"}
                            {item.status === "out" && "Stokta Yok"}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            ) : (
              <div className="flex flex-col items-center justify-center py-10 text-gray-400 text-sm mt-8">
                <img
                  src="/assets/illustrations/kitchen-order-not-found.webp"
                  alt="Veri bulunamadı"
                  className="w-1/4 mx-auto mb-2"
                />
                <span>Kullanım ve stok verisi bulunamadı</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* filter dialog */}
      <dialog id="filter-dialog" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg flex items-center">
            <IconFilter stroke={iconStroke} /> Filtreler
          </h3>
          {/* filters */}
          <div className="my-4">
            <div>
              <label className="block text-gray-500 text-sm">Filtre</label>
              <select
                className="select select-sm select-bordered w-full"
                ref={filterTypeRef}
              >
                {filters.map((filter, index) => (
                  <option key={index} value={filter.key}>
                    {filter.value}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex gap-2 mt-4">
              <div className="flex-1">
                <label
                  htmlFor="fromDate"
                  className="block text-gray-500 text-sm"
                >
                  Başlangıç Tarihi
                </label>
                <input
                  defaultValue={defaultDateFrom}
                  type="date"
                  ref={fromDateRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
              <div className="flex-1">
                <label
                  htmlFor="toDate"
                  className="block text-gray-500 text-sm"
                >
                  Bitiş Tarihi
                </label>
                <input
                  defaultValue={defaultDateTo}
                  type="date"
                  ref={toDateRef}
                  className="text-sm w-full border rounded-lg px-4 py-1 bg-gray-50 outline-restro-border-green-light"
                />
              </div>
            </div>
          </div>
          {/* filters */}
          <div className="modal-action">
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn">Kapat</button>
              <button
                onClick={() => {
                  setState({
                    ...state,
                    filter: filterTypeRef.current.value,
                    fromDate: fromDateRef.current.value || null,
                    toDate: toDateRef.current.value || null,
                  });
                }}
                className="btn ml-2"
              >
                Uygula
              </button>
            </form>
          </div>
        </div>
      </dialog>
      {/* filter dialog */}
    </Page>
  );
}