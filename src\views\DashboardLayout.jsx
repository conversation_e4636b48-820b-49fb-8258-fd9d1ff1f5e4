import React, { useContext } from 'react'
import { Outlet } from "react-router-dom"
import Navbar from '../components/Navbar'
import AppBar from '../components/AppBar'
import { NavbarContext } from '../contexts/NavbarContext'
import { useNavbarVisibility } from '../contexts/NavbarVisibilityContext';

export default function DashboardLayout() {
  const { showNavbar } = useNavbarVisibility();
  const [isNavbarCollapsed] = useContext(NavbarContext)

  return (
    <div className='flex'>
      {showNavbar && <Navbar />}
      <div className={`w-full ${showNavbar ? (isNavbarCollapsed ? 'ml-[5.5rem]' : 'ml-[5.5rem] md:ml-72') : ''}`}>
        <AppBar />
        <Outlet />
      </div>
    </div>
  )
}