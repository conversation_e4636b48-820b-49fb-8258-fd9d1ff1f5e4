import React, { useState, useEffect, useCallback, useRef } from 'react';
import { IconX, IconCheck } from '@tabler/icons-react';
import QRCode from 'qrcode';
import { toast } from 'react-hot-toast';
import <PERSON>ie from 'js-cookie';
import { verifyPin, initPOS } from '../controllers/pin.controller';
import { getUserDetailsInLocalStorage, saveUserDetailsInLocalStorage } from '../helpers/UserDetails';
import { shouldDisableLockScreen, updatePinSettingsOnVerify } from '../helpers/LockScreenHelper';
import { useLock } from '../contexts/LockContext';
import { signOut } from '../controllers/auth.controller';

function LockScreen() {
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const { unlock } = useLock();
  const user = getUserDetailsInLocalStorage();
  const tenantId = user?.tenant_id;
  const [qrVisible, setQrVisible] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [qrDataUrl, setQrDataUrl] = useState('');
  const [storeSettings, setStoreSettings] = useState({});

  // Use refs to avoid stale closures
  const timerRef = useRef(null);
  const pinRef = useRef(pin);

  // Update ref when pin changes
  useEffect(() => {
    pinRef.current = pin;
  }, [pin]);

  // Initialize POS data only once on component mount
  useEffect(() => {
    let isMounted = true;

    async function _initPOS() {
      try {
        setIsLoading(true);
        const res = await initPOS();

        if (res.status === 200 && isMounted) {
          const data = res.data;
          setStoreSettings(data.storeSettings || {});
        }
      } catch (error) {
        console.error('Error initializing POS:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    _initPOS();
    generateQRCode('employee_123');

    return () => {
      isMounted = false;
    };
  }, []);

  // Generate QR code
  const generateQRCode = useCallback(async (value) => {
    try {
      const dataUrl = await QRCode.toDataURL(value, {
        width: 300,
        height: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      });
      setQrDataUrl(dataUrl);
    } catch (error) {
      console.error('QR Code generation error:', error);
    }
  }, []);

  // Update clock
  useEffect(() => {
    timerRef.current = setInterval(() => setCurrentTime(new Date()), 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // PIN verification
  const handleVerifyPin = useCallback(async () => {
    const currentPin = pinRef.current;

    if (currentPin.length !== 6) {
      setError('PIN 6 haneli olmalıdır!');
      return;
    }

    setIsLoading(true);
    try {
      const response = await verifyPin(currentPin, tenantId);

      if (response?.status === 200) {
        // Save user details
        saveUserDetailsInLocalStorage(response.data.user);

        // Update access token if provided
        if (response.data.accessToken) {
          // Remove existing token
          Cookie.remove('restroprosaas__authenticated');
          // Set new token
          Cookie.set('restroprosaas__authenticated', true, { path: '/' });
        }

        // PIN ayarlarını güncelle
        updatePinSettingsOnVerify(response.data.user);

        // Eğer disableScreenLock true ise, kilit ekranını devre dışı bırak
        if (shouldDisableLockScreen(response.data.user)) {
          // Kullanıcı kilit ekranını devre dışı bırakmış, kilit ekranını kapat
          console.log("Kilit ekranı kullanıcı ayarlarına göre devre dışı bırakıldı");
        }

        toast.success(response.data.message || 'PIN doğrulandı');
        unlock();
      }
    } catch (error) {
      console.error('PIN Verification Error:', error);
      setError(error?.response?.data?.message || 'PIN doğrulanamadı!');
      setPin('');
    } finally {
      setIsLoading(false);
    }
  }, [tenantId, unlock]);

  // Handle shift start/end via QR code
  const handleShiftStart = () => {
    setQrVisible(true);
  };

  // Simulate QR scan for testing
  const handleQRScan = useCallback((employeeId) => {
    setIsLoading(true);

    // Simulate processing delay - in production replace with actual API call
    setTimeout(() => {
      setIsLoading(false);
      setQrVisible(false);
      setSelectedEmployee(employeeId);
      toast.success(`Mesai işlemi başarılı: ${employeeId}`);
    }, 2000);
  }, []);

  // Email login (sign out and redirect)
  const handleEmailLogin = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await signOut();

      if (response.status === 200) {
        toast.success('Başarıyla çıkış yapıldı!');
        window.location.href = '/login';
      }
    } catch (error) {
      console.error('Çıkış yapılırken hata:', error);
      toast.error('Çıkış yapılırken bir hata oluştu!');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Close QR modal
  const handleCloseQRModal = () => {
    setQrVisible(false);
  };

  // Handle PIN input
  const handlePinInput = useCallback((value) => {
    if (value === 'C') {
      setPin('');
      setError('');
    } else if (value === '✓') {
      handleVerifyPin();
    } else if (pin.length < 6) {
      setPin(prev => {
        const newPin = prev + value;
        // Clear error when typing
        if (error) setError('');
        return newPin;
      });
    }
  }, [pin, error, handleVerifyPin]);

  // Numeric keypad
  const renderKeypad = () => {
    const keys = [1, 2, 3, 4, 5, 6, 7, 8, 9, 'C', 0, '✓'];

    return (
      <div className="grid grid-cols-3 gap-2">
        {keys.map((num, i) => (
          <button
            key={i}
            className="bg-gray-200 p-2 sm:p-4 rounded-lg text-base sm:text-xl font-bold transition-colors hover:bg-gray-300 active:bg-gray-400"
            onClick={() => handlePinInput(num)}
            disabled={isLoading}
          >
            {num}
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 flex flex-col h-screen bg-gray-100 overflow-y-auto z-50">
      {/* Header Bar */}
      <div className="sticky top-0 z-50 bg-white shadow-md p-4">
        <div className="container mx-auto flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <img src="/logo.svg" alt="Logo" className="h-8" />
            <span className="text-xl font-bold">{storeSettings.store_name || 'POS System'}</span>
          </div>
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center gap-2">
              {isOnline ? (
                <div className="flex items-center text-green-500 gap-1">
                  <div className="w-2 h-2 rounded-full bg-green-500" />
                  <span>Çevrimiçi</span>
                </div>
              ) : (
                <div className="flex items-center text-red-500 gap-1">
                  <div className="w-2 h-2 rounded-full bg-red-500" />
                  <span>Çevrimdışı</span>
                </div>
              )}
            </div>
            <div className="text-lg">{currentTime.toLocaleTimeString('tr-TR')}</div>
            <div className="text-gray-500">{currentTime.toLocaleDateString('tr-TR')}</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 flex-col md:flex-row min-h-0 overflow-y-auto">
        {/* Left Panel - Login */}
        <div className="w-full md:w-1/2 p-4 md:p-8 flex flex-col items-center justify-center bg-white">
          <div className="w-full max-w-md space-y-6">
            <div className="text-center">
              <h1 className="text-2xl font-bold mb-2">Hoş Geldiniz</h1>
              <p className="text-gray-500">Lütfen PIN kodunuzu girin.</p>
            </div>

            <input
              type="password"
              maxLength={6}
              value={pin}
              onChange={(e) => {
                setPin(e.target.value.replace(/\D/g, '').slice(0, 6));
                setError('');
              }}
              placeholder="······"
              className="text-center text-3xl tracking-widest w-full rounded-lg border px-4 py-2 bg-gray-50"
              disabled={isLoading}
            />

            {error && <p className="text-red-500 text-center">{error}</p>}

            {renderKeypad()}

            <button
              onClick={handleVerifyPin}
              disabled={isLoading}
              className={`w-full py-3 rounded-lg text-white font-bold ${
                isLoading ? 'bg-gray-300' : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isLoading ? 'Kontrol Ediliyor...' : 'Giriş Yap'}
            </button>

            <div className="flex gap-2">
              {/* Shift Start/End Button */}
              <button
                onClick={handleShiftStart}
                disabled={isLoading}
                className="flex-1 py-3 rounded-lg text-white font-bold bg-green-600 hover:bg-green-700 disabled:bg-green-300"
              >
                Mesai Başlat / Bitir
              </button>

              {/* Email Login Button */}
              <button
                onClick={handleEmailLogin}
                disabled={isLoading}
                className="flex-1 py-3 rounded-lg text-white font-bold bg-purple-600 hover:bg-purple-700 disabled:bg-purple-300"
              >
                E-posta ile Giriş
              </button>
            </div>
          </div>

          {/* QR Code Modal */}
          {qrVisible && (
            <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75 z-50">
              <div className="bg-white p-8 rounded-lg shadow-lg relative">
                <button
                  className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                  onClick={handleCloseQRModal}
                >
                  <IconX size={24} />
                </button>
                <div className="flex flex-col items-center">
                  <h2 className="text-xl font-bold mb-4">Personel Seçimi</h2>
                  <div className="bg-gray-100 p-6 rounded-lg">
                    <img src={qrDataUrl} alt="QR Code" className="w-48 h-48" />
                  </div>
                  <p className="mt-4 text-gray-600">Lütfen personelin QR kodunu okutun.</p>

                  {isLoading && (
                    <div className="mt-4 flex items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-gray-900"></div>
                      <span className="ml-2">Yükleniyor...</span>
                    </div>
                  )}

                  {selectedEmployee && (
                    <div className="mt-4 flex items-center text-green-500">
                      <IconCheck className="mr-2" />
                      <span>Mesai başlatıldı: {selectedEmployee}</span>
                    </div>
                  )}

                  {/* For demo purposes - in production, remove this button */}
                  <button
                    onClick={() => handleQRScan('Test_Employee')}
                    className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Test QR Scan
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Placeholder for future content */}
        <div className="w-full md:w-1/2 p-4 md:p-8 flex flex-col bg-gray-50">
          {/* You can add additional content here if needed */}
        </div>
      </div>
    </div>
  );
}

export default LockScreen;