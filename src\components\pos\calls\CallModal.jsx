import React from 'react';
import { IconPhone, IconPhoneOff, IconDeviceMobile, IconUser, IconClock } from "@tabler/icons-react";

export const CallModal = ({ calls, onClose }) => {
  return (
    <dialog id="modal-calls" className="modal modal-bottom sm:modal-middle">
      <div className="modal-box max-h-[80vh]">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-bold text-lg">Çağrı Geçmişi</h3>
          <form method="dialog">
            <button className="btn btn-sm btn-circle btn-ghost">✕</button>
          </form>
        </div>

        <div className="overflow-y-auto max-h-[60vh]">
          {calls.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              Hen<PERSON><PERSON> çağrı yok
            </div>
          ) : (
            <div className="flex flex-col gap-3">
              {calls.map((call, index) => (
                <div key={index} className="card bg-base-200 shadow-sm">
                  <div className="card-body p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {call.callType === 'INCOMING' ? (
                          <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                            <IconPhone className="text-green-600" size={20} />
                          </div>
                        ) : (
                          <div className="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                            <IconPhoneOff className="text-red-600" size={20} />
                          </div>
                        )}
                        <div>
                          <div className="font-semibold text-lg">
                            {call.phoneNumber}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center gap-2">
                            <IconClock size={14} />
                            {new Date(call.timestamp).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className={`badge ${call.callType === 'INCOMING' ? 'badge-success' : 'badge-error'} gap-1`}>
                        {call.callType === 'INCOMING' ? 'Gelen' : 'Giden'}
                      </div>
                    </div>
                    
                    {call.deviceInfo && (
                      <div className="mt-3 pt-3 border-t flex items-center gap-2 text-sm text-gray-500">
                        <IconDeviceMobile size={16} />
                        {call.deviceInfo.model} ({call.deviceInfo.manufacturer})
                      </div>
                    )}

                    {call.duration && (
                      <div className="mt-2 text-sm text-gray-500">
                        Süre: {Math.round(call.duration / 1000)} saniye
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="modal-action">
          <form method="dialog">
            <button className="btn">Kapat</button>
          </form>
        </div>
      </div>
    </dialog>
  );
};