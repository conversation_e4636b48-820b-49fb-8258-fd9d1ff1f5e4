import React from 'react';
import { IconX } from "@tabler/icons-react";
import { iconStroke } from "../../../config/config";
import { PAYMENT_ICONS } from "../../../config/payment_icons";
import { PaymentSummary } from './PaymentSummary';

export const PaymentModal = ({
  table,
  paymentState,
  currency,
  paymentTypes,
  onPaymentComplete,
  onPaymentTypeSelect
}) => {
  // Ödeme tipinin seçimini engellemek için form submit'i önle
  const handleSubmit = (e) => {
    e.preventDefault();
  };


  return (
    <dialog id={`quick-payment-modal-${table.id}`} className="modal">
      <div className="modal-box">
        <div className="flex items-center justify-between">
          <h3 className="font-bold text-lg">Ödeme Al - {table.table_title}</h3>
          <form method="dialog">
            <button className="hover:bg-red-100 border-none transition active:scale-95 bg-red-50 text-red-500 btn btn-sm btn-circle">
              <IconX size={18} stroke={iconStroke} />
            </button>
          </form>
        </div>

        <div className="my-6">
          <PaymentSummary 
            netTotal={paymentState.summaryNetTotal}
            taxTotal={paymentState.summaryTaxTotal}
            total={paymentState.summaryTotal}
            currency={currency}
          />
        </div>

        {/* Radio grup için form eklendi */}
        <form onSubmit={handleSubmit} className="grid grid-cols-3 gap-2">
          {paymentTypes.map((paymentType, i) => (
            <div key={i} className="relative">
              <input
                checked={paymentState.selectedPaymentType === paymentType.id}
                onChange={() => onPaymentTypeSelect(paymentType.id)}
                type="radio"
                name="payment_type"
                id={`${paymentType.icon}-${table.id}`}
                value={paymentType.id}
                className="peer hidden"
              />
              <label
                htmlFor={`${paymentType.icon}-${table.id}`}
                className="block border rounded-2xl flex items-center justify-center gap-1 flex-col px-4 py-3 text-gray-500 peer-checked:border-restro-green peer-checked:text-restro-green peer-checked:font-bold cursor-pointer transition"
              >
                {paymentType.icon && <div>{PAYMENT_ICONS[paymentType.icon]}</div>}
                <p className="text-xs">{paymentType.title}</p>
              </label>
            </div>
          ))}
        </form>

        <div className="modal-action">
          {/* Dialog form ayrı tutuldu */}
          <button
            onClick={() => onPaymentComplete(table.id)}
            disabled={!paymentState.selectedPaymentType}
            className="w-full btn hover:bg-restro-green-dark bg-restro-green text-white disabled:opacity-50"
          >
            Ödemeyi Al ve Siparişi Kapat
          </button>
        </div>
      </div>
    </dialog>
  );
};

export default PaymentModal;

/**
 * PaymentModal komponenti ödeme işlemi için modal pencere gösterir
 * 
 * @param {Object} table - Masa bilgileri
 * @param {Object} paymentState - Ödeme durumu ve tutarları
 * @param {string} currency - Para birimi
 * @param {Array} paymentTypes - Ödeme tipleri listesi
 * @param {Function} onPaymentComplete - Ödeme tamamlama fonksiyonu
 * @param {Function} onPaymentTypeSelect - Ödeme tipi seçme fonksiyonu
 * 
 * @example
 * <PaymentModal
 *   table={tableData}
 *   paymentState={paymentStateData}
 *   currency="₺"
 *   paymentTypes={paymentTypesList}
 *   onPaymentComplete={(tableId) => handlePayment(tableId)}
 *   onPaymentTypeSelect={(typeId) => handlePaymentTypeSelect(typeId)}
 * />
 */

// PropTypes tanımlamaları (opsiyonel)
