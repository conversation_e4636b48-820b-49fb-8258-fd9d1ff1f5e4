import React, { useRef, useState } from "react";
import Page from "../../components/Page";
import {
  IconPencil,
  IconPlus,
  IconTrash,
  IconCash,
} from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { 
  addCashRegister, 
  updateCashRegister, 
  deleteCashRegister, 
  useCashRegisters 
} from "../../controllers/cash-register.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";

export default function CashRegisterSettingsPage() {
  // Refs for add form
  const registerNameRef = useRef();
  const registerDescriptionRef = useRef();
  const registerLocationRef = useRef();

  // Refs for edit form
  const registerIdUpdateRef = useRef();
  const registerNameUpdateRef = useRef();
  const registerDescriptionUpdateRef = useRef();
  const registerLocationUpdateRef = useRef();
  const registerIsActiveUpdateRef = useRef();

  const { data: cashRegisters, error, isLoading, APIURL } = useCashRegisters();

  // Add new cash register
  const handleAddCashRegister = async (e) => {
    e.preventDefault();
    
    const name = registerNameRef.current.value;
    const description = registerDescriptionRef.current.value;
    const location = registerLocationRef.current.value;
    
    if (!name) {
      toast.error("Kasa adı zorunludur!");
      return;
    }
    
    try {
      toast.loading("Kasa ekleniyor...");
      
      const response = await addCashRegister({
        name,
        description,
        location
      });
      
      toast.dismiss();
      
      if (response.status === 201) {
        toast.success("Kasa başarıyla eklendi!");
        mutate(APIURL);
        
        // Clear form
        registerNameRef.current.value = "";
        registerDescriptionRef.current.value = "";
        registerLocationRef.current.value = "";
        
        // Close modal
        document.getElementById("modal-add-register").close();
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || "Kasa eklenirken bir hata oluştu!");
      console.error(error);
    }
  };

  // Update cash register
  const handleUpdateCashRegister = async (e) => {
    e.preventDefault();
    
    const id = registerIdUpdateRef.current.value;
    const name = registerNameUpdateRef.current.value;
    const description = registerDescriptionUpdateRef.current.value;
    const location = registerLocationUpdateRef.current.value;
    const is_active = registerIsActiveUpdateRef.current.checked;
    
    if (!name) {
      toast.error("Kasa adı zorunludur!");
      return;
    }
    
    try {
      toast.loading("Kasa güncelleniyor...");
      
      const response = await updateCashRegister(id, {
        name,
        description,
        location,
        is_active
      });
      
      toast.dismiss();
      
      if (response.status === 200) {
        toast.success("Kasa başarıyla güncellendi!");
        mutate(APIURL);
        
        // Close modal
        document.getElementById("modal-edit-register").close();
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || "Kasa güncellenirken bir hata oluştu!");
      console.error(error);
    }
  };

  // Delete cash register
  const handleDeleteCashRegister = async (id) => {
    if (!confirm("Bu kasayı silmek istediğinize emin misiniz?")) {
      return;
    }
    
    try {
      toast.loading("Kasa siliniyor...");
      
      const response = await deleteCashRegister(id);
      
      toast.dismiss();
      
      if (response.status === 200) {
        toast.success("Kasa başarıyla silindi!");
        mutate(APIURL);
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || "Kasa silinirken bir hata oluştu!");
      console.error(error);
    }
  };

  // Open edit modal with register data
  const openEditModal = (register) => {
    registerIdUpdateRef.current.value = register.id;
    registerNameUpdateRef.current.value = register.name;
    registerDescriptionUpdateRef.current.value = register.description || "";
    registerLocationUpdateRef.current.value = register.location || "";
    registerIsActiveUpdateRef.current.checked = register.is_active === 1;
    
    document.getElementById("modal-edit-register").showModal();
  };

  if (isLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }

  if (error) {
    console.error(error);
    return <Page className="px-8 py-6">Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!</Page>;
  }

  return (
    <Page className="px-8 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Kasa Yönetimi</h1>
        <button
          onClick={() => document.getElementById("modal-add-register").showModal()}
          className="btn btn-primary btn-sm"
        >
          <IconPlus size={18} stroke={iconStroke} />
          Yeni Kasa Ekle
        </button>
      </div>

      {/* Cash Registers List */}
      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th>Kasa Adı</th>
              <th>Konum</th>
              <th>Açıklama</th>
              <th>Durum</th>
              <th>İşlemler</th>
            </tr>
          </thead>
          <tbody>
            {cashRegisters && cashRegisters.length > 0 ? (
              cashRegisters.map((register) => (
                <tr key={register.id}>
                  <td>{register.name}</td>
                  <td>{register.location || "-"}</td>
                  <td>{register.description || "-"}</td>
                  <td>
                    <span
                      className={`badge ${
                        register.is_active === 1 ? "badge-success" : "badge-error"
                      }`}
                    >
                      {register.is_active === 1 ? "Aktif" : "Pasif"}
                    </span>
                    {register.has_active_session > 0 && (
                      <span className="badge badge-warning ml-2">Oturum Açık</span>
                    )}
                  </td>
                  <td>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => openEditModal(register)}
                        className="btn btn-sm btn-outline btn-info"
                      >
                        <IconPencil size={18} stroke={iconStroke} />
                      </button>
                      <button
                        onClick={() => handleDeleteCashRegister(register.id)}
                        className="btn btn-sm btn-outline btn-error"
                        disabled={register.has_active_session > 0}
                      >
                        <IconTrash size={18} stroke={iconStroke} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="5" className="text-center py-4">
                  Henüz kasa bulunmuyor. Yeni bir kasa ekleyin.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Add Cash Register Modal */}
      <dialog id="modal-add-register" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg mb-4">Yeni Kasa Ekle</h3>
          <form onSubmit={handleAddCashRegister}>
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Kasa Adı*</span>
              </label>
              <input
                type="text"
                ref={registerNameRef}
                placeholder="Kasa adı giriniz"
                className="input input-bordered"
                required
              />
            </div>
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Konum</span>
              </label>
              <input
                type="text"
                ref={registerLocationRef}
                placeholder="Kasa konumu giriniz"
                className="input input-bordered"
              />
            </div>
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Açıklama</span>
              </label>
              <textarea
                ref={registerDescriptionRef}
                placeholder="Kasa açıklaması giriniz"
                className="textarea textarea-bordered"
              ></textarea>
            </div>
            <div className="modal-action">
              <button type="submit" className="btn btn-primary">
                Kaydet
              </button>
              <button
                type="button"
                className="btn"
                onClick={() => document.getElementById("modal-add-register").close()}
              >
                İptal
              </button>
            </div>
          </form>
        </div>
      </dialog>

      {/* Edit Cash Register Modal */}
      <dialog id="modal-edit-register" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg mb-4">Kasa Düzenle</h3>
          <form onSubmit={handleUpdateCashRegister}>
            <input type="hidden" ref={registerIdUpdateRef} />
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Kasa Adı*</span>
              </label>
              <input
                type="text"
                ref={registerNameUpdateRef}
                placeholder="Kasa adı giriniz"
                className="input input-bordered"
                required
              />
            </div>
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Konum</span>
              </label>
              <input
                type="text"
                ref={registerLocationUpdateRef}
                placeholder="Kasa konumu giriniz"
                className="input input-bordered"
              />
            </div>
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Açıklama</span>
              </label>
              <textarea
                ref={registerDescriptionUpdateRef}
                placeholder="Kasa açıklaması giriniz"
                className="textarea textarea-bordered"
              ></textarea>
            </div>
            <div className="form-control mb-3">
              <label className="label cursor-pointer">
                <span className="label-text">Aktif</span>
                <input
                  type="checkbox"
                  ref={registerIsActiveUpdateRef}
                  className="toggle toggle-primary"
                />
              </label>
            </div>
            <div className="modal-action">
              <button type="submit" className="btn btn-primary">
                Güncelle
              </button>
              <button
                type="button"
                className="btn"
                onClick={() => document.getElementById("modal-edit-register").close()}
              >
                İptal
              </button>
            </div>
          </form>
        </div>
      </dialog>
    </Page>
  );
}
