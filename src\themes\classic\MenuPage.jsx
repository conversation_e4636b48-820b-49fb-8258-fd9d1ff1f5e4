import {
  IconCarrot,
  IconChevronRight,
  IconMenu,
  IconX,
  IconChevronLeft
} from "@tabler/icons-react";
import React, { useEffect, useState, useRef } from "react";
import { iconStroke } from "../../config/config";

import {
  getCart,
  setCart,
  getQRMenuInit,
} from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import toast from "react-hot-toast";
import { useParams, useSearchParams, useNavigate, useLocation  } from "react-router-dom";
import { getQRMenuLink } from "../../helpers/QRMenuHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";


export default function MenuPage() {

  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;

  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;
  const location = useLocation();
  const defaultCategory = location.state?.selectedCategory || "all";

  const { t } = useTranslation(['menu_item', 'category', 'menu_description']);


  // Kategoriler için scroll ref
  const categoryScrollRef = useRef(null);


  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    storeTable: null,
    categories: [],
    currentCategory: defaultCategory,
    menuItems: [],
    searchQuery: "",
    currentItem: null,
    cartItems: [],
    currentItemId: null,
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  useEffect(() => {
    if (categoryScrollRef.current) {
      // Seçili kategoriye ait elemente scroll yapacağız
      const selectedElement = document.getElementById(`cat-${defaultCategory}`);
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        });
      }
    }
  }, [defaultCategory]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);

      const storedCart = getCart();
      if (res.status == 200) {
        const data = res.data;

        const currency = CURRENCIES.find(
          (c) => c.cc == data?.storeSettings?.currency
        );
        if (data?.translations) {
                  updateI18nResources(data.translations);
        }

        setState({
          ...state,
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          menuItems: data?.menuItems,
          storeTable: data?.storeTable || null,
          cartItems: [...storedCart],
          currency: currency?.symbol || "",
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - Menü - SewPOS`;
        }
      }
    } catch (error) {
    }
  };

  const {
    isLoading,
    storeSettings,
    categories,
    menuItems,
    currentCategory,
    searchQuery,
    currentItem,
    cartItems,
    currentItemId,
    currency,
    sew_points
  } = state;

  const storeName = storeSettings?.store_name || "";
  const is_qr_menu_enabled = storeSettings?.is_qr_menu_enabled || false;
  const is_qr_order_enabled = storeSettings?.is_qr_order_enabled1 || false;
  const storeImage = storeSettings?.store_image;

  const bodyStyles = {
    backgroundColor: storeSettings?.background_color || "#f3f4f6",
    color: storeSettings?.text_color || "#000000",
  };

  const headerStyles = {
    backgroundColor: storeSettings?.header_color || "#047857",
    color: storeSettings?.header_text_color || "#ffffff",
  };

  const headtextStlyes = {
    color: storeSettings?.head_text_color || "#FFFFFF",
  };

  const pricetextStlyes = {
    color: storeSettings?.price_text_color || "#70b56a",
  };

  const descriptionStlyes = {
    color: storeSettings?.text_color || "#ffffff",
  };

  if (isLoading) {
    return <QRMenuLoading />;
  }

  if (!qrcode) {
    return (
      <div className="w-full">
        <div className="container mx-auto px-4 flex h-screen items-center justify-center">
          Broken Link!
        </div>
      </div>
    );
  }

  if (!is_qr_menu_enabled) {
    return (
      <div className="w-full">
        <div className="container mx-auto px-4 flex h-screen items-center justify-center">
          Menu Not Available!
        </div>
      </div>
    );
  }

  const QR_MENU_LINK = getQRMenuLink(qrcode);

  const btnShare = async () => {
    const shareData = {
      title: "Menu",
      text: "Menu",
      url: QR_MENU_LINK,
    };

    try {
      if (navigator.canShare) {
        if (navigator?.canShare(shareData)) {
          await navigator.share(shareData);
        }
      } else {
        await navigator.clipboard.writeText(QR_MENU_LINK);
        toast.success("Menu Link Copied!");
      }
    } catch (error) {
    }
  };

  const btnOpenMenuItemDetail = (
    addons,
    variants,
    category_id,
    category_title,
    id,
    imageURL,
    price,
    tax_id,
    tax_rate,
    tax_title,
    tax_type,
    title,
    description,
    sew_points
  ) => {
    setState({
      ...state,
      currentItem: {
        addons: addons,
        variants: variants,
        category_id: category_id,
        category_title: category_title,
        id,
        image: imageURL,
        price,
        tax_id,
        tax_rate,
        tax_title,
        tax_type,
        title,
        description,
        sew_points
      },
    });
    document.getElementById("modal_item_detail").showModal();
  };

  // cart
  function addItemToCart(item) {
    const modifiedItem = {
      ...item,
      quantity: 1,
      notes: null,
    };

    const newCart = cartItems;

    newCart.push(modifiedItem);

    setState({
      ...state,
      cartItems: [...newCart],
    });
    setCart(newCart);
  }

  const btnAddMenuItemToCartWithVariantsAndAddon = () => {
    let price = 0;
    let selectedVariantId = null;
    const selectedAddonsId = [];

    const itemVariants = document.getElementsByName("variants");
    itemVariants.forEach((item) => {
      if (item.checked) {
        selectedVariantId = item.value;
        return;
      }
    });


    price = parseFloat(currentItem.price);

    const itemAddons = document.getElementsByName("addons");
    itemAddons.forEach((item) => {
      if (item.checked) {
        selectedAddonsId.push(item.value);
      }
    });

    const addons = currentItem?.addons || [];
    const variants = currentItem?.variants || [];

    let selectedVariant = null;
    if (selectedVariantId) {
      selectedVariant = variants.find((v) => v.id == selectedVariantId);
      price = parseFloat(selectedVariant.price);
    }

    let selectedAddons = [];
    if (selectedAddonsId.length > 0) {
      selectedAddons = selectedAddonsId.map((addonId) =>
        addons.find((addon) => addon.id == addonId)
      );
      selectedAddons.forEach((addon) => {
        const addonPrice = parseFloat(addon.price);
        price += addonPrice;
      });
    }

    const itemCart = {
      ...currentItem,
      price: price,
      variant_id: selectedVariantId,
      variant: selectedVariant,
      addons_ids: selectedAddonsId,
      addons: selectedAddons,
    };

    addItemToCart(itemCart);
  };
  return (
    <div className="w-full" style={bodyStyles}>
      <header className="w-full py-4 sticky top-0 z-50" style={headerStyles}>
        <div className="container mx-auto px-4 flex items-center justify-between">
          <IconChevronLeft
                      size={28}
                      stroke={3}
                      className="cursor-pointer"
                      onClick={() => navigate(-1)}

                    />
          <div className="flex items-center">
            {storeImage ? (
              <img
                src={getImageURL(storeImage)}
                alt={storeName}
                className="h-12 object-contain"
              />
            ) : (
              <h1 className="text-xl font-bold">{storeName}</h1>
            )}
          </div>
          <div className="flex items-center w-6"> </div>

        </div>
      </header>

      <div className="container mx-auto px-4">
        {/* appbar */}
        <div className="bg-white shadow-lg rounded-full p-2 w-full md:w-96 mx-auto mt-4 flex gap-2 sticky top-24 z-50">
          <input
            type="search"
            name="search"
            id="search"
            className="bg-gray-100 rounded-full outline-none px-4 py-2 flex-1 text-black"
            placeholder="Ürün ara..."
            value={searchQuery}
            onChange={(e) => {
              setState({
                ...state,
                searchQuery: e.target.value,
              });
            }}
          />
        </div>
        {/* appbar */}



        {/* menu items */}
        <div className="p-2 w-full md:w-96 mx-auto mt-4 flex flex-col gap-4 min-h-screen">
          {menuItems
            .filter((item) => {
              const { category_id } = item;
              if (currentCategory == "all") {
                return true;
              }
              if (currentCategory == category_id) {
                return true;
              }
              return false;
            })
            .filter((menuItem) => {
              if (!searchQuery) {
                return true;
              }
              return new String(menuItem.title)
                .trim()
                .toLowerCase()
                .includes(searchQuery.trim().toLowerCase());
            })
            .map((item, i) => {
              const {
                addons,
                variants,
                category_id,
                category_title,
                id,
                image,
                price,
                description,
                tax_id,
                tax_rate,
                tax_title,
                tax_type,
                title,
                sew_points,
              } = item;
              // addon {id, item_id, title, price}
              // variant {id, item_id, title, price}

              const imageURL = getImageURL(image);
              const hasVariantOrAddon =
                variants?.length > 0 || addons?.length > 0;

              return (
                <div
                  key={id}
                  className={`w-full rounded-3xl flex gap-4 ${
                    !is_qr_order_enabled
                      ? " cursor-pointer"
                      : ""
                  }`}
                  onClick={
                    !is_qr_order_enabled
                      ? () =>
                          btnOpenMenuItemDetail(
                            addons,
                            variants,
                            category_id,
                            category_title,
                            id,
                            image ? imageURL : null,
                            price,
                            tax_id,
                            tax_rate,
                            tax_title,
                            tax_type,
                            title,
                            description,
                            sew_points
                          )
                      : undefined
                  }
                >
                  <div>
                    <div className="rounded-2xl w-32 h-32 object-cover relative bg-gray-200 flex items-center justify-center text-gray-500 ">
                      {image ? (
                        <img
                          src={imageURL}
                          alt={title}
                          className="w-full h-full absolute top-0 left-0 rounded-2xl object-cover z-0"
                        />
                      ) : (
                        <IconCarrot />
                      )}
                      {/* Add button overlapping image */}
                      {is_qr_order_enabled && (
                        <button
                          className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 text-restro-green bg-white border border-restro-green py-1 px-6 rounded-lg font-bold hover:bg-restro-green hover:text-white"
                          onClick={(e) => {
                            e.stopPropagation();

                            if (hasVariantOrAddon) {
                              btnOpenMenuItemDetail(
                                addons,
                                variants,
                                category_id,
                                category_title,
                                id,
                                image ? imageURL : null,
                                price,
                                tax_id,
                                tax_rate,
                                tax_title,
                                tax_type,
                                title,
                                description,
                                sew_points
                              );
                            } else {
                              addItemToCart(item);
                            }
                          }}
                        >
                          EKLE
                        </button>
                      )}
                    </div>

                    {is_qr_order_enabled &&
                      (variants.length > 0 || addons.length > 0) && (
                        <div className="text-center mt-5">
                          <p className="text-xs text-gray-500">Özelleştirebilir</p>
                        </div>
                      )}
                  </div>

                  <div className="flex-1">
                    <p className="text-lg" style={headtextStlyes} >{t(`menu_item:${id}`, { defaultValue: title })}</p>
                    <p className="text-sm font-bold " style={pricetextStlyes} >
                      {price} {currency}
                    </p>
                    <div className="flex gap-2 mt-2 text-xs " style={descriptionStlyes} >
                        <p>{t(`menu_description:${id}`)}</p>
                    </div>
                    {sew_points && (
                      <div className="flex gap-2 mt-1 text-xs">
                        <p className="font-bold" style={pricetextStlyes}>HollyPuan: {sew_points}</p>
                      </div>
                    )}
                    <div className="flex gap-2 mt-2 text-xs "  style={descriptionStlyes}>
                      {variants.length > 0 && (
                        <p>{variants.length} Seçenek Mevcut</p>
                      )}
                      {addons.length > 0 && (
                        <p>{addons.length} Ekstra Mevcut</p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
        </div>
        {/* menu items */}

        {is_qr_order_enabled && <div className="h-28" />}

        {is_qr_order_enabled && cartItems.length > 0 && (
          <div
            className="p-2 w-full md:w-96 mx-auto fixed bottom-0 left-0 right-0 bg-white shadow-lg flex justify-between items-center rounded-2xl"
            style={{ boxShadow: "0 -4px 10px rgba(0, 0, 0, 0.1)" }}
          >
            <button
            onClick={() => {
              navigate(`/m/${qrcode}/cart`, {
                state: { storeTable: state.storeTable, currency: currency },
              });
            }}
            className="bg-restro-green text-white py-4 px-6 flex justify-between items-center rounded-xl w-full">
              <p className="text-md font-bold">
                {cartItems.length} Ürün eklendi
              </p>
              <p
                className="text-white text-lg font-bold px-2 rounded-lg flex gap-1 items-center"
              >
                Sepeti gör
                <IconChevronRight size={20} stroke={3} />
              </p>
            </button>
          </div>
        )}


        {/* dialog for detail menu item view with addon, variants */}
        <dialog
          id="modal_item_detail"
          className="modal modal-bottom sm:modal-middle"
        >
          <div className="modal-box">
            <div className="absolute top-4 right-4">
              <form method="dialog">
                <button className="btn btn-circle">
                  <IconX stroke={iconStroke} />
                </button>
              </form>
            </div>

            <div className="w-full flex gap-4">
              <div className="rounded-2xl w-32 h-32 object-cover relative bg-gray-200 flex items-center justify-center text-gray-500 ">
                {currentItem?.image ? (
                  <img
                    src={currentItem?.image}
                    alt={currentItem?.title}
                    className="w-full h-full absolute top-0 left-0 rounded-2xl object-cover z-0"
                  />
                ) : (
                  <IconCarrot />
                )}
              </div>
              <div className="flex-1">
                <p className="text-lg  text-gray-500  ">{currentItem?.title}</p>
                <p className="text-sm text-restro-green font-bold">
                  {currentItem?.price}  {currency}
                </p>
                <p className="text-xs text-gray-500 ">
                  {currentItem?.description}
                </p>
                {currentItem?.sewPoints && (
                  <p className="text-xs font-bold mt-1" style={pricetextStlyes}>
                    HollyPuan: {currentItem.sewPoints}
                  </p>
                )}
                <div className="flex gap-2 mt-2 text-xs text-gray-500">
                  {currentItem?.variants.length > 0 && (
                    <p>{currentItem?.variants.length} Seçenek</p>
                  )}
                  {currentItem?.addons.length > 0 && (
                    <p>{currentItem?.addons.length} Esktra</p>
                  )}
                </div>
              </div>
            </div>

            <div className="my-4 flex flex-col sm:flex-row gap-2">
              {currentItem?.variants.length > 0 && (
                <div className="flex-1">
                  <h3>Seçenekler</h3>
                  <div className="flex flex-col gap-2 mt-2">
                    {currentItem?.variants?.map((variant, index) => {
                      const { id, title, price } = variant;
                      return (
                        <label
                          key={index}
                          className="cursor-pointer label justify-start gap-2"
                        >
                          <input
                            type="radio"
                            className="radio"
                            name="variants"
                            id={id}
                            value={id}
                            defaultChecked={index === 0}
                          />
                          <span className="label-text">
                            {title} - {price} {currency}

                          </span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              )}

              {currentItem?.addons.length > 0 && (
                <div className="flex-1">
                  <h3>Ekstralar</h3>
                  <div className="flex flex-col gap-2 mt-2">
                    {currentItem?.addons?.map((addon, index) => {
                      const { id, title, price } = addon;
                      return (
                        <label
                          key={index}
                          className="cursor-pointer label justify-start gap-2"
                        >
                          <input
                            type="checkbox"
                            name="addons"
                            className="checkbox checkbox-sm"
                            value={id}
                          />
                          <span className="label-text">
                            {title} ( + {price} {currency} )
                          </span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            {is_qr_order_enabled && (
              <div className="modal-action flex justify-end w-full">
                <form method="dialog" className="w-full">
                  <button
                    onClick={() => {
                      btnAddMenuItemToCartWithVariantsAndAddon();
                    }}
                    className="w-full rounded-lg hover:bg-green-800 transition active:scale-95 hover:shadow-lg px-6 py-2 bg-restro-green text-white font-bold"
                  >
                    Sepete Ekle
                  </button>
                </form>
              </div>
            )}
          </div>
        </dialog>

        {/* dialog for detail menu item view */}
      </div>
    </div>
  );
}
