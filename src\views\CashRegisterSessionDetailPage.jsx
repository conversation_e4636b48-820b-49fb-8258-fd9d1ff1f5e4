import React, { useState } from "react";
import Page from "../components/Page";
import { useParams, useNavigate } from "react-router-dom";
import {
  IconArrowLeft,
  IconCash,
  IconClock,
  IconCreditCard,
  IconReceipt,
  IconUser,
} from "@tabler/icons-react";
import { iconStroke } from "../config/config";
import { useCashRegisterSession, closeCashRegisterSession } from "../controllers/cash-register.controller";
import { CURRENCIES } from "../config/currencies.config";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { toast } from "react-hot-toast";
import { getUserDetailsInLocalStorage } from "../helpers/UserDetails";

export default function CashRegisterSessionDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data, error, isLoading, mutate } = useCashRegisterSession(id);
  const [closingAmount, setClosingAmount] = useState("");
  const [closingNotes, setClosingNotes] = useState("");
  const user = getUserDetailsInLocalStorage();
  const currency = user?.currency || "₺";

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "dd MMMM yyyy HH:mm", { locale: tr });
    } catch (error) {
      return dateString;
    }
  };

  // Handle close session
  const handleCloseSession = async (e) => {
    e.preventDefault();
    
    if (!closingAmount || isNaN(parseFloat(closingAmount)) || parseFloat(closingAmount) < 0) {
      toast.error("Geçerli bir kapanış tutarı giriniz!");
      return;
    }
    
    if (!confirm("Kasa oturumunu kapatmak istediğinize emin misiniz?")) {
      return;
    }
    
    try {
      toast.loading("Kasa oturumu kapatılıyor...");
      
      const response = await closeCashRegisterSession(id, {
        closing_amount: parseFloat(closingAmount),
        closing_notes: closingNotes
      });
      
      toast.dismiss();
      
      if (response.status === 200) {
        toast.success("Kasa oturumu başarıyla kapatıldı!");
        mutate();
        document.getElementById("modal-close-session").close();
      }
    } catch (error) {
      toast.dismiss();
      toast.error(error.response?.data?.message || "Kasa oturumu kapatılırken bir hata oluştu!");
      console.error(error);
    }
  };

  if (isLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }

  if (error) {
    console.error(error);
    return <Page className="px-8 py-6">Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!</Page>;
  }

  if (!data) {
    return <Page className="px-8 py-6">Kasa oturumu bulunamadı!</Page>;
  }

  const { session, transactions } = data;
  const multiplePaymentOrders = session?.multiple_payment_orders || [];

  return (
    <Page className="px-8 py-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate(-1)}
          className="btn btn-ghost btn-sm mr-4"
        >
          <IconArrowLeft size={20} stroke={iconStroke} />
        </button>
        <h1 className="text-2xl font-bold">Kasa Oturumu Detayları</h1>
        {session.status === "open" && (
          <button
            onClick={() => document.getElementById("modal-close-session").showModal()}
            className="btn btn-primary btn-sm ml-auto"
          >
            <IconClock size={18} stroke={iconStroke} />
            Oturumu Kapat
          </button>
        )}
      </div>

      {/* Session Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-base-200 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Oturum Bilgileri</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Kasa</p>
              <p className="font-medium">{session.register_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Konum</p>
              <p className="font-medium">{session.register_location || "-"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Durum</p>
              <span
                className={`badge ${
                  session.status === "open" ? "badge-success" : "badge-info"
                }`}
              >
                {session.status === "open" ? "Açık" : "Kapalı"}
              </span>
            </div>
            <div>
              <p className="text-sm text-gray-500">Toplam İşlem</p>
              <p className="font-medium">{parseFloat(session.total_transactions).toFixed(2)} {currency}</p>
            </div>
          </div>
        </div>

        <div className="bg-base-200 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Zaman Bilgileri</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Açılış Zamanı</p>
              <p className="font-medium">{formatDate(session.opened_at)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Kapanış Zamanı</p>
              <p className="font-medium">{formatDate(session.closed_at) || "-"}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Açan Kullanıcı</p>
              <p className="font-medium">{session.opener_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Kapatan Kullanıcı</p>
              <p className="font-medium">{session.closer_name || "-"}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Financial Info */}
      <div className="bg-base-200 p-6 rounded-lg mb-8">
        <h2 className="text-xl font-semibold mb-4">Finansal Bilgiler</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <p className="text-sm text-gray-500">Açılış Tutarı</p>
            <p className="font-medium">{parseFloat(session.opening_amount).toFixed(2)} {currency}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Kapanış Tutarı</p>
            <p className="font-medium">
              {session.closing_amount
                ? `${parseFloat(session.closing_amount).toFixed(2)} ${currency}`
                : "-"}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Beklenen Tutar</p>
            <p className="font-medium">
              {session.expected_amount
                ? `${parseFloat(session.expected_amount).toFixed(2)} ${currency}`
                : "-"}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Fark</p>
            <p
              className={
                session.difference_amount
                  ? parseFloat(session.difference_amount) < 0
                    ? "font-medium text-error"
                    : parseFloat(session.difference_amount) > 0
                    ? "font-medium text-success"
                    : "font-medium"
                  : "font-medium"
              }
            >
              {session.difference_amount
                ? `${parseFloat(session.difference_amount).toFixed(2)} ${currency}`
                : "-"}
            </p>
          </div>
        </div>
        <div className="mt-4">
          <p className="text-sm text-gray-500">Açılış Notu</p>
          <p className="font-medium">{session.opening_notes || "-"}</p>
        </div>
        <div className="mt-4">
          <p className="text-sm text-gray-500">Kapanış Notu</p>
          <p className="font-medium">{session.closing_notes || "-"}</p>
        </div>
      </div>

      {/* Transactions */}
      <h2 className="text-xl font-semibold mb-4">İşlemler</h2>
      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr>
              <th>Tarih</th>
              <th>Sipariş No</th>
              <th>Masa</th>
              <th>Ödeme Türü</th>
              <th>Tutar</th>
              <th>İşlem Türü</th>
              <th>Kullanıcı</th>
              <th>Not</th>
            </tr>
          </thead>
          <tbody>
            {transactions && transactions.length > 0 ? (
              transactions.map((transaction) => (
                <tr key={transaction.id}>
                  <td>{formatDate(transaction.created_at)}</td>
                  <td>{transaction.order_id}</td>
                  <td>{transaction.table_title || "-"}</td>
                  <td>{transaction.payment_type}</td>
                  <td>{parseFloat(transaction.amount).toFixed(2)} {currency}</td>
                  <td>
                    <span
                      className={`badge ${
                        transaction.transaction_type === "payment"
                          ? "badge-success"
                          : transaction.transaction_type === "refund"
                          ? "badge-error"
                          : "badge-info"
                      }`}
                    >
                      {transaction.transaction_type === "payment"
                        ? "Ödeme"
                        : transaction.transaction_type === "refund"
                        ? "İade"
                        : transaction.transaction_type}
                    </span>
                  </td>
                  <td>{transaction.user_name}</td>
                  <td>{transaction.notes || "-"}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="8" className="text-center py-4">
                  Bu oturumda henüz işlem yapılmamış.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Multiple Payment Orders */}
      {multiplePaymentOrders && multiplePaymentOrders.length > 0 && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Çoklu Ödeme Siparişleri</h2>
          <div className="space-y-6">
            {multiplePaymentOrders.map((order, index) => (
              <div key={index} className="bg-base-200 p-6 rounded-lg">
                {/* Sipariş Başlığı */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 pb-4 border-b">
                  <div>
                    <h3 className="text-lg font-bold">Sipariş #{order.order_id}</h3>
                    <div className="flex flex-wrap gap-2 mt-1 text-sm text-gray-600">
                      <span>📅 {formatDate(order.order_date)}</span>
                      {order.table_title && <span>🪑 {order.table_title}</span>}
                      {order.customer_name && <span>👤 {order.customer_name}</span>}
                      <span>👨‍🍳 {order.created_by_name}</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mt-3 sm:mt-0 text-center">
                    <div>
                      <p className="text-xs text-gray-500">Ödeme Sayısı</p>
                      <p className="font-bold text-blue-600">{order.payment_count}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Sipariş Toplamı</p>
                      <p className="font-bold">{parseFloat(order.order_total).toFixed(2)} {currency}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Bu Oturumda Ödenen</p>
                      <p className="font-bold text-green-600">{parseFloat(order.total_paid_in_session).toFixed(2)} {currency}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Kalan Tutar</p>
                      <p className={`font-bold ${parseFloat(order.remaining_amount) > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {parseFloat(order.remaining_amount).toFixed(2)} {currency}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Ödemeler */}
                {order.payments && order.payments.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-semibold mb-3 text-gray-700">Bu Oturumdaki Ödemeler</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {order.payments.map((payment, paymentIndex) => (
                        <div key={paymentIndex} className="bg-white p-3 rounded-lg border">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              {payment.payment_icon === 'cash' ? (
                                <IconCash size={16} className="text-green-600" />
                              ) : (
                                <IconCreditCard size={16} className="text-blue-600" />
                              )}
                              <span className="font-medium text-sm">{payment.payment_type}</span>
                            </div>
                            <span className="font-bold text-green-600">{parseFloat(payment.amount).toFixed(2)} {currency}</span>
                          </div>
                          <div className="text-xs text-gray-500">
                            <p>📅 {formatDate(payment.payment_date)}</p>
                            <p>👤 {payment.payment_by_name}</p>
                            {payment.notes && <p>📝 {payment.notes}</p>}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* İndirimler */}
                {order.discounts && order.discounts.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-semibold mb-3 text-gray-700">İndirimler</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {order.discounts.map((discount, discountIndex) => (
                        <div key={discountIndex} className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-sm">
                              {discount.item_title ? `${discount.item_title} İndirimi` : 'Genel İndirim'}
                            </span>
                            <span className="font-bold text-yellow-600">-{parseFloat(discount.calculated_discount).toFixed(2)} {currency}</span>
                          </div>
                          <div className="text-xs text-gray-600">
                            <p>Tür: {discount.discount_type === 'amount' ? 'Tutar' : 'Yüzde'}</p>
                            <p>👤 {discount.discount_by_name}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Ürünler */}
                {order.items && order.items.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3 text-gray-700">Sipariş Ürünleri</h4>
                    <div className="space-y-2">
                      {order.items.map((item, itemIndex) => (
                        <div key={itemIndex} className={`p-3 rounded-lg border ${
                          item.status === 'cancelled' ? 'bg-red-50 border-red-200' :
                          item.status === 'completed' ? 'bg-green-50 border-green-200' :
                          'bg-gray-50 border-gray-200'
                        }`}>
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div className="flex-1 min-w-0 mb-2 sm:mb-0">
                              <div className="flex items-center gap-2">
                                <h5 className="font-medium text-gray-800">{item.item_title}</h5>
                                {item.variant_title && (
                                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                    {item.variant_title}
                                  </span>
                                )}
                                <span className={`text-xs px-2 py-1 rounded-full ${
                                  item.status === 'cancelled' ? 'bg-red-100 text-red-700' :
                                  item.status === 'completed' ? 'bg-green-100 text-green-700' :
                                  'bg-gray-100 text-gray-700'
                                }`}>
                                  {item.status === 'cancelled' ? 'İptal' :
                                   item.status === 'completed' ? 'Tamamlandı' : item.status}
                                </span>
                              </div>
                              <div className="text-sm text-gray-600 mt-1">
                                <span>Birim: {parseFloat(item.price).toFixed(2)} {currency}</span>
                                <span className="mx-2">•</span>
                                <span>Adet: {item.quantity}</span>
                                {item.notes && (
                                  <>
                                    <span className="mx-2">•</span>
                                    <span>Not: {item.notes}</span>
                                  </>
                                )}
                              </div>
                              {item.addons && item.addons.length > 0 && (
                                <div className="text-xs text-gray-500 mt-1">
                                  Ekstralar: {item.addons.map(addon => `${addon.title} (+${parseFloat(addon.price).toFixed(2)} ${currency})`).join(', ')}
                                </div>
                              )}
                              {item.status === 'cancelled' && item.reason_title && (
                                <div className="text-xs text-red-600 mt-1">
                                  İptal Nedeni: {item.reason_title} • {item.action_by}
                                </div>
                              )}
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-lg">{parseFloat(item.total_price).toFixed(2)} {currency}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Close Session Modal */}
      <dialog id="modal-close-session" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg mb-4">Kasa Oturumunu Kapat</h3>
          <form onSubmit={handleCloseSession}>
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Kapanış Tutarı*</span>
              </label>
              <input
                type="number"
                step="0.01"
                value={closingAmount}
                onChange={(e) => setClosingAmount(e.target.value)}
                placeholder="Kasadaki tutarı giriniz"
                className="input input-bordered"
                required
              />
            </div>
            <div className="form-control mb-3">
              <label className="label">
                <span className="label-text">Kapanış Notu</span>
              </label>
              <textarea
                value={closingNotes}
                onChange={(e) => setClosingNotes(e.target.value)}
                placeholder="Kapanış notu giriniz"
                className="textarea textarea-bordered"
              ></textarea>
            </div>
            <div className="modal-action">
              <button type="submit" className="btn btn-primary">
                Oturumu Kapat
              </button>
              <button
                type="button"
                className="btn"
                onClick={() => document.getElementById("modal-close-session").close()}
              >
                İptal
              </button>
            </div>
          </form>
        </div>
      </dialog>
    </Page>
  );
}
