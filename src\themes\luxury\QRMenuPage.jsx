import React, { useEffect, useState } from "react";
import {
  getQRMenuInit,
  sendFeedback,
} from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import {
  IconCategory,
  IconInfoCircle,
  IconLanguage,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandInstagram,
  IconBrandWhatsapp,
  IconCrown,
  IconStar,
  IconSparkles,
  IconDiamond,
  IconAward,
} from "@tabler/icons-react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    campaigns: null,
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("dynamic");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          campaigns: data.campaigns,
          storeSettings: data?.storeSettings,
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, campaigns } = state;
  const storeName = storeSettings?.store_name || t("storeName");
  const slides = storeSettings?.slides?.split(",") || [];

  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [slides.length]);

  if (isLoading) {
    return <QRMenuLoading theme="luxury" />;
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 font-serif">
      {/* LUXURY HEADER */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-amber-600 via-yellow-600 to-amber-700"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>

        <header className="relative z-10 px-6 py-12">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <IconCrown size={40} className="text-yellow-200 animate-pulse" />
              <div className="flex flex-col items-center">
                {storeSettings?.store_image ? (
                  <img
                    src={getImageURL(storeSettings.store_image)}
                    alt={storeName}
                    className="h-20 object-contain mb-2"
                  />
                ) : (
                  <h1 className="text-5xl font-bold text-white tracking-wide drop-shadow-lg">{storeName}</h1>
                )}
                <div className="flex items-center space-x-2">
                  <IconStar size={16} className="text-yellow-200" />
                  <span className="text-yellow-100 text-sm uppercase tracking-widest">Premium Experience</span>
                  <IconStar size={16} className="text-yellow-200" />
                </div>
              </div>
              <IconCrown size={40} className="text-yellow-200 animate-pulse" />
            </div>
            <p className="text-amber-100 text-xl italic">
              {t("welcomeMessage", { ns: "dynamic", defaultValue: "Lüks Gastronomi Deneyimi" })}
            </p>
          </div>
        </header>
      </div>

      {/* LUXURY SHOWCASE */}
      <div className="px-6 -mt-8 relative z-20">
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-3xl shadow-2xl overflow-hidden border-4 border-amber-200">
            {slides.length > 0 ? (
              <div className="relative">
                <div className="h-96 overflow-hidden">
                  <div
                    className="flex transition-all duration-1000 ease-in-out"
                    style={{
                      transform: `translateX(-${currentSlide * 100}%)`,
                      width: `${slides.length * 100}%`,
                    }}
                  >
                    {slides.map((slide, index) => (
                      <div key={index} className="relative" style={{ flex: "0 0 100%" }}>
                        <img
                          src={getImageURL(slide)}
                          alt={`Slide ${index + 1}`}
                          className="w-full h-96 object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-amber-900/30 via-transparent to-transparent"></div>
                        <div className="absolute top-6 left-6">
                          <div className="bg-amber-600 text-white px-4 py-2 rounded-full flex items-center space-x-2">
                            <IconDiamond size={16} />
                            <span className="text-sm font-bold">PREMIUM</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Luxury Slide Indicators */}
                <div className="absolute bottom-6 left-0 right-0 flex justify-center gap-4">
                  {slides.map((_, index) => (
                    <button
                      key={index}
                      className={`transition-all duration-500 ${
                        currentSlide === index
                          ? "w-12 h-3 bg-amber-400 rounded-full"
                          : "w-3 h-3 bg-amber-200 rounded-full hover:bg-amber-300"
                      }`}
                      onClick={() => setCurrentSlide(index)}
                    />
                  ))}
                </div>
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center bg-gradient-to-br from-amber-100 to-yellow-100">
                <div className="text-center">
                  <IconSparkles size={64} className="text-amber-500 mx-auto mb-6 animate-pulse" />
                  <p className="text-amber-700 font-bold text-2xl">
                    {t("noSlides", { ns: "dynamic", defaultValue: "Özel İçerikler Hazırlanıyor..." })}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* LUXURY MENU SECTION */}
      <div className="px-6 mt-16">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-4 mb-4">
              <IconStar size={32} className="text-amber-600" />
              <h2 className="text-4xl font-bold text-amber-800 tracking-wide">
                Exclusive Menu
              </h2>
              <IconStar size={32} className="text-amber-600" />
            </div>
            <div className="w-32 h-1 bg-gradient-to-r from-transparent via-amber-600 to-transparent mx-auto"></div>
          </div>

          {/* Main Menu Button */}
          <div className="relative group mb-12">
            <div className="absolute -inset-2 bg-gradient-to-r from-amber-400 via-yellow-400 to-amber-400 rounded-3xl blur opacity-30 group-hover:opacity-60 transition duration-1000"></div>
            <button
              onClick={() =>
                navigate(
                  encryptedTableId
                    ? `/m/${qrcode}/category?table=${encryptedTableId}`
                    : `/m/${qrcode}/category`
                )
              }
              className="relative w-full bg-gradient-to-r from-amber-600 via-yellow-600 to-amber-700 text-white py-8 px-8 rounded-3xl font-bold text-2xl shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-2 flex items-center justify-center gap-6 border-2 border-amber-400"
            >
              <IconCrown size={36} />
              <span>{t("menuButton", { defaultValue: "Premium Menüyü Keşfet" })}</span>
              <IconAward size={32} className="animate-bounce" />
            </button>
          </div>

          {/* Luxury Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl p-8 shadow-xl border-2 border-amber-200 text-center">
              <IconDiamond size={48} className="text-amber-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-amber-800 mb-2">Premium Quality</h3>
              <p className="text-amber-700">En kaliteli malzemeler</p>
            </div>

            <div className="bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl p-8 shadow-xl border-2 border-amber-200 text-center">
              <IconAward size={48} className="text-amber-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-amber-800 mb-2">Award Winning</h3>
              <p className="text-amber-700">Ödüllü şef tarifları</p>
            </div>

            <div className="bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl p-8 shadow-xl border-2 border-amber-200 text-center">
              <IconSparkles size={48} className="text-amber-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-amber-800 mb-2">Exclusive</h3>
              <p className="text-amber-700">Özel lezzet deneyimi</p>
            </div>
          </div>

          {/* Social Links */}
          {(storeSettings?.facebook || storeSettings?.twitter || storeSettings?.instagram || storeSettings?.whatsapp) && (
            <div className="text-center">
              <h3 className="text-2xl font-bold text-amber-800 mb-6">Connect With Us</h3>
              <div className="flex justify-center gap-4">
                {storeSettings?.facebook && (
                  <a
                    href={storeSettings.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group p-4 bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-2 border-amber-200"
                  >
                    <IconBrandFacebook size={28} className="text-amber-600 group-hover:scale-110 transition-transform" />
                  </a>
                )}
                {storeSettings?.twitter && (
                  <a
                    href={storeSettings.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group p-4 bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-2 border-amber-200"
                  >
                    <IconBrandTwitter size={28} className="text-amber-600 group-hover:scale-110 transition-transform" />
                  </a>
                )}
                {storeSettings?.instagram && (
                  <a
                    href={storeSettings.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group p-4 bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-2 border-amber-200"
                  >
                    <IconBrandInstagram size={28} className="text-amber-600 group-hover:scale-110 transition-transform" />
                  </a>
                )}
                {storeSettings?.whatsapp && (
                  <a
                    href={storeSettings.whatsapp}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group p-4 bg-gradient-to-br from-white via-amber-50 to-yellow-50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-2 border-amber-200"
                  >
                    <IconBrandWhatsapp size={28} className="text-amber-600 group-hover:scale-110 transition-transform" />
                  </a>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* LUXURY FOOTER */}
      <footer className="mt-20 bg-gradient-to-r from-amber-800 via-yellow-800 to-amber-900 text-white">
        <div className="max-w-4xl mx-auto px-6 py-12">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-4 mb-4">
              <IconCrown size={24} className="text-yellow-200" />
              <span className="text-xl font-bold uppercase tracking-widest">Luxury Dining</span>
              <IconCrown size={24} className="text-yellow-200" />
            </div>
            <div className="flex items-center justify-center space-x-8 mt-8">
              <button className="hover:bg-white/20 transition-colors p-3 rounded-full">
                <IconInfoCircle size={24} />
              </button>
              <button className="hover:bg-white/20 transition-colors p-3 rounded-full">
                <IconLanguage size={24} />
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
