import React, { useEffect, useState } from "react";
import {
  getQRMenuInit,
  sendFeedback,
} from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import {
  IconCategory,
  IconInfoCircle,
  IconLanguage,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandInstagram,
  IconBrandWhatsapp,
} from "@tabler/icons-react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    campaigns: null,
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("dynamic");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          campaigns: data.campaigns,
          storeSettings: data?.storeSettings,
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, campaigns } = state;
  const storeName = storeSettings?.store_name || t("storeName");
  const slides = storeSettings?.slides?.split(",") || [];

  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    if (slides.length > 1) {
      const interval = setInterval(() => {
        setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [slides.length]);

  // Dil değiştirme fonksiyonu
  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  if (isLoading) {
    return <QRMenuLoading theme="minimal" />;
  }

  return (
    <div className="w-full min-h-screen bg-white flex flex-col">
      {/* BACKGROUND SLIDES - FULL SCREEN */}
      <div className="fixed inset-0 z-0">
        {slides.length > 0 ? (
          <div className="relative w-full h-full">
            <div
              className="flex transition-all duration-1000 ease-in-out w-full h-full"
              style={{
                transform: `translateX(-${currentSlide * 100}%)`,
                width: `${slides.length * 100}%`,
              }}
            >
              {slides.map((slide, index) => (
                <div key={index} className="relative w-full h-full flex-shrink-0">
                  <img
                    src={getImageURL(slide)}
                    alt={`Slide ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/40"></div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200"></div>
        )}
      </div>

      {/* HEADER - WHITE */}
      <header className="relative z-10 bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex items-center justify-center">
            {storeSettings?.store_image ? (
              <img
                src={getImageURL(storeSettings.store_image)}
                alt={storeName}
                className="h-12 object-contain"
              />
            ) : (
              <h1 className="text-2xl font-bold text-gray-800">{storeName}</h1>
            )}
          </div>
        </div>
      </header>

      {/* MAIN CONTENT - CENTERED */}
      <div className="flex-1 flex items-center justify-center relative z-10 px-6">
        <div className="max-w-md w-full">
          {/* MENU BUTTON */}
          <button
            onClick={() =>
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                  : `/m/${qrcode}/menu`
              )
            }
            className="w-full bg-white text-gray-800 py-4 px-8 rounded-lg font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-gray-200"
          >
            {t("menuButton", { defaultValue: "Menüyü Gör" })}
          </button>
        </div>
      </div>

      {/* FOOTER - TRANSPARENT WITH LANGUAGE OPTIONS */}
      <footer className="relative z-10 bg-black/20 backdrop-blur-sm">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex flex-col items-center space-y-4">
            {/* Language Options */}
            <div className="flex items-center space-x-6">
              <button
                onClick={() => changeLanguage('tr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'tr' ? 'text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                Türkçe
              </button>
              <button
                onClick={() => changeLanguage('en')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'en' ? 'text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                English
              </button>
              <button
                onClick={() => changeLanguage('ar')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ar' ? 'text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                العربية
              </button>
              <button
                onClick={() => changeLanguage('ru')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ru' ? 'text-white' : 'text-white/70 hover:text-white'
                }`}
              >
                Русский
              </button>
            </div>

            {/* Social Links - Only if available */}
            {(storeSettings?.facebook || storeSettings?.twitter || storeSettings?.instagram || storeSettings?.whatsapp) && (
              <div className="flex items-center space-x-4">
                {storeSettings?.facebook && (
                  <a
                    href={storeSettings.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/70 hover:text-white transition-colors p-2"
                  >
                    <IconBrandFacebook size={20} />
                  </a>
                )}
                {storeSettings?.twitter && (
                  <a
                    href={storeSettings.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/70 hover:text-white transition-colors p-2"
                  >
                    <IconBrandTwitter size={20} />
                  </a>
                )}
                {storeSettings?.instagram && (
                  <a
                    href={storeSettings.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/70 hover:text-white transition-colors p-2"
                  >
                    <IconBrandInstagram size={20} />
                  </a>
                )}
                {storeSettings?.whatsapp && (
                  <a
                    href={storeSettings.whatsapp}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/70 hover:text-white transition-colors p-2"
                  >
                    <IconBrandWhatsapp size={20} />
                  </a>
                )}
              </div>
            )}
          </div>
        </div>
      </footer>
    </div>
  );
}