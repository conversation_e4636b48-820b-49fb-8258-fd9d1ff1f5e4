import React from 'react';
import { IconCarrot } from "@tabler/icons-react";

const ProductGrid = ({
  menuItems,
  selectedCategory,
  searchQuery,
  currency,
  onItemClick,
  getImageURL
}) => {
  // Türkçe karakterleri İngilizce karakterlere dönüştüren yardımcı fonksiyon
  const normalizeText = (text) => {
    return text
      .replace(/ı/g, 'i')
      .replace(/İ/g, 'i')
      .replace(/ç/g, 'c')
      .replace(/Ç/g, 'c')
      .replace(/ğ/g, 'g')
      .replace(/Ğ/g, 'g')
      .replace(/ş/g, 's')
      .replace(/Ş/g, 's')
      .replace(/ö/g, 'o')
      .replace(/Ö/g, 'o')
      .replace(/ü/g, 'u')
      .replace(/Ü/g, 'u')
      .toLowerCase();
  };

  const filteredItems = menuItems
    .filter((menuItem) => {
      // Eğer arama yapılıyorsa, kategori filtres<PERSON> yoksay ve tüm ürünleri ara
      if (searchQuery && searchQuery.trim() !== '') {
        const normalizedTitle = normalizeText(menuItem.title.trim());
        const normalizedQuery = normalizeText(searchQuery.trim());
        return normalizedTitle.includes(normalizedQuery);
      }

      // Arama yapılmıyorsa, kategori filtresini uygula
      if (selectedCategory === "all") {
        return true;
      }
      return selectedCategory === menuItem.category_id;
    });

  return (
    <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-4 w-full z-0 px-4 pb-4 rounded-b-2xl'>
      {filteredItems.map((menuItem, i) => {
        const { title, id, price, image, category_title, addons, variants } = menuItem;
        const imageURL = image ? getImageURL(image) : null;
        const hasVariantOrAddon = variants?.length > 0 || addons?.length > 0;

        return (
          <div
            key={i}
            className='bg-white border border-restro-border-green-light rounded-2xl p-2 flex gap-2 cursor-pointer transition-transform active:bg-gray-200 active:scale-95'
            onClick={() => onItemClick(menuItem, hasVariantOrAddon)}
          >
            <div className="w-28 h-20 bg-gray-100 rounded-lg hidden md:flex items-center justify-center text-gray-300 relative">
              {image ? (
                <img src={imageURL} alt={title} className="w-full h-full absolute top-0 left-0 rounded-lg object-cover" />
              ) : (
                <IconCarrot />
              )}
            </div>
            <div>
              <p>{title}</p>
              <p>{price} {currency}</p>
              <p className="mt-2 text-xs text-gray-500">{category_title}</p>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProductGrid;