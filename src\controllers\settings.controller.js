import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

export function useStoreSettings() {
  const APIURL = `/settings/store-setting`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function clearTenantData(tables) {
  try {
    const response = await ApiClient.delete('/settings/clear-data', {
      data: { tables }
    });
    return response;
  } catch (error) {
    console.error("Veri silme hatası:", error.response?.data || error.message);
    throw error;
  }
}

export function usePrinters() {
  const APIURL = `/printers`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function addPrinter(printerData) {
  try {
    const response = await ApiClient.post("/printers/add", printerData);
    return response.data;
  } catch (error) {
    console.error("Yazıcı ekleme hatası:", error.response?.data || error.message);
    throw error;
  }
}

export async function updatePrinter(id, printerData) {
  try {
    const response = await ApiClient.put(`/printers/update/${id}`, printerData);
    return response.data;
  } catch (error) {
    console.error("Yazıcı güncelleme hatası:", error.response?.data || error.message);
    throw error;
  }
}

export async function deletePrinter(id) {
  try {
    const response = await ApiClient.delete(`/printers/delete/${id}`);
    return response.data;
  } catch (error) {
    console.error("Yazıcı silme hatası:", error.response?.data || error.message);
    throw error;
  }
}


export async function saveStoreSettings(storeName, address, phone, email, currency, isQRMenuEnabled, isQROrderEnabled, defaultOrderType, dineInEnabled, deliveryEnabled, takeawayEnabled, facebook, instagram, twitter, whatsapp) {
  try {
    const response = await ApiClient.post("/settings/store-setting", {
      storeName, address, phone, email, currency,
      isQRMenuEnabled, isQROrderEnabled, defaultOrderType,
      dineInEnabled,
      deliveryEnabled,
      takeawayEnabled,
      facebook,
      instagram,
      twitter,
      whatsapp
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function uploadStoreImage(formData) {
  try {
    const response = await ApiClient.post("/settings/store-setting/upload-store-image", formData);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteStoreImage(uniqueId) {
  try {
    const response = await ApiClient.post("/settings/store-setting/delete-store-image", {
      uniqueId
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export function usePrintSettings() {
  const APIURL = `/settings/print-setting`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function savePrintSettings(pageFormat, header, footer, showNotes, isEnablePrint, showStoreDetails, showCustomerDetails, printToken) {
  try {
    const response = await ApiClient.post("/settings/print-setting", {
      pageFormat, header, footer, showNotes, isEnablePrint, showStoreDetails, printToken, showCustomerDetails
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function uploadSlidesPhoto(formData) {
  try {
    const response = await ApiClient.post('/settings/update/upload-photo', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function removeSlidesPhoto(imageUrl) {
  try {
    const response = await ApiClient.post('/settings/update/remove-photo', { imageUrl });
    return response;
  } catch (error) {
    throw error;
  }
}
export function usePaymentTypes() {
  const APIURL = `/settings/payment-types`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function addNewPaymentType(title, isActive, icon) {
  try {
    const response = await ApiClient.post("/settings/payment-types/add", {
      title,
      isActive,
      icon
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export async function deletePaymentType(id) {
  try {
    const response = await ApiClient.delete(`/settings/payment-types/${id}`)
    return response;
  } catch (error) {
    throw error;
  }
};

export async function togglePaymentType(id, isActive) {
  try {
    const response = await ApiClient.post(`/settings/payment-types/${id}/toggle`, {
      isActive
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export async function updatePaymentType(id, title, isActive, icon) {
  try {
    const response = await ApiClient.post(`/settings/payment-types/${id}/update`, {
      title,
      isActive,
      icon
    })
    return response;
  } catch (error) {
    throw error;
  }
};


export function useTaxes() {
  const APIURL = `/settings/taxes`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function addNewTax(title, rate, type) {
  try {
    const response = await ApiClient.post("/settings/taxes/add", {
      title,
      rate, type
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export async function deleteTax(id) {
  try {
    const response = await ApiClient.delete(`/settings/taxes/${id}`)
    return response;
  } catch (error) {
    throw error;
  }
};

export async function updateTax(id, title, rate, type) {
  try {
    const response = await ApiClient.post(`/settings/taxes/${id}/update`, {
      title,
      rate, type
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export function useStoreTables() {
  const APIURL = `/settings/store-tables`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function deleteTable(id) {
  try {
    const response = await ApiClient.delete(`/settings/store-tables/${id}`)
    return response;
  } catch (error) {
    throw error;
  }
};

export async function addNewStoreTable(title, floor, seatingCapacity) {
  try {
    const response = await ApiClient.post("/settings/store-tables/add", {
      title, floor, seatingCapacity
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export async function updateStoreTable(id, title, floor, seatingCapacity) {
  try {
    const response = await ApiClient.post(`/settings/store-tables/${id}/update`, {
      title, floor, seatingCapacity
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export async function bulkAddStoreTables(baseTitle, count, floor, seatingCapacity) {
  try {
    const response = await ApiClient.post(`/settings/store-tables/bulk-add`, {
      baseTitle, count, floor, seatingCapacity
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export function useCategories() {
  const APIURL = `/settings/categories`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function addCategory(title, parent_id) {
  try {
    const response = await ApiClient.post("/settings/categories/add", {
      title,
      parent_id,  // parent_id burada ekleniyor
    });
    return response;
  } catch (error) {
    throw error;
  }
}


export async function deleteCategory(id) {
  try {
    const response = await ApiClient.delete(`/settings/categories/${id}`)
    return response;
  } catch (error) {
    throw error;
  }
};

export async function updateCategory(id, payload) {
  try {
    const response = await ApiClient.post(`/settings/categories/${id}/update`, payload);
    return response;
  } catch (error) {
    throw error;
  }
}



export async function uploadCategoryPhoto(id, form) {
  try {
    const response = await ApiClient.post(`/settings/categories/${id}/upload-photo`, form);
    return response;
  } catch (error) {
    throw error;
  }
}

export function useDevices() {
  const APIURL = `/auth/devices`;
  const { data, error, isLoading } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
  };
}

export async function removeDevice(deviceId) {
  try {
    const response = await ApiClient.post(`/auth/remove-device`, {
      device_id: deviceId
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export async function saveTenantConfig(configData) {
  try {
      const response = await ApiClient.post("/settings/tenant-config/update", configData);
      return response.data;
  } catch (error) {
      throw error;
  }
}

export async function getTenantConfig() {
  try {
    const response = await ApiClient.get("/settings/tenant-config");
    return response.data;
  } catch (error) {
    throw error;
  }
}

