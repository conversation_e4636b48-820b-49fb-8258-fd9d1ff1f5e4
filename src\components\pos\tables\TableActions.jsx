import React from 'react';
import {
  IconCash,
  IconReceipt,
  IconPlus,
  IconArrowsExchange,
  IconArrowsJoin,
  IconList,
  IconBolt
} from "@tabler/icons-react";
import { iconStroke } from "../../../config/config";

export const TableActions = ({
  table,
  status,
  tableOrders,
  onClose,
  onAdvancedPayment,
  onShowOrdersDetail,
  onQuickPayment,
  onPrint,
  onAddProduct,
  onTableChange,
  onTableMerge
}) => (
  <dialog id={`table-actions-${table.id}`} className="modal">
    <div className="modal-box w-[700px]">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold">Masa <PERSON>: {table.table_title}</h3>
        <form method="dialog">
          <button onClick={onClose} className="btn btn-l btn-circle">✕</button>
        </form>
      </div>
      
      <div className="grid grid-cols-3 gap-4">
        {status !== 'empty' ? (
          <>
            <button
              onClick={() => {
                document.getElementById(`table-actions-${table.id}`).close();
                onAdvancedPayment(table.id);
              }}
              className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
            >
              <IconCash size={28} stroke={iconStroke} className="text-green-500" />
              <span className="text-m text-center">Gelişmiş Ödeme</span>
            </button>

            <button
        onClick={onShowOrdersDetail}
        className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
      >
        <IconList size={28} stroke={iconStroke} className="text-indigo-500" />
        <span className="text-m text-center">Sipariş Detayları</span>
      </button>
            

            <button
              onClick={() => {
                document.getElementById(`table-actions-${table.id}`).close();
                onQuickPayment(table.id);
              }}
              className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
            >
              <IconBolt size={28} stroke={iconStroke} className="text-yellow-500" />
              <span className="text-m text-center">Hızlı Öde</span>
            </button>

            <button
              onClick={onPrint}
              className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
            >
              <IconReceipt size={28} stroke={iconStroke} className="text-gray-500" />
              <span className="text-m text-center">Yazdır</span>
            </button>

            <button
              onClick={onAddProduct}
              className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
            >
              <IconPlus size={28} stroke={iconStroke} className="text-blue-500" />
              <span className="text-m text-center">Ürün Ekle</span>
            </button>

            <button
              onClick={onTableChange}
              className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
            >
              <IconArrowsExchange size={28} stroke={iconStroke} className="text-purple-500" />
              <span className="text-m text-center">Masayı Değiştir</span>
            </button>

            <button
              onClick={onTableMerge}
              className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
            >
              <IconArrowsJoin size={28} stroke={iconStroke} className="text-orange-500" />
              <span className="text-m text-center">Masaları Birleştir</span>
            </button>
            
          </>
        ) : (
          <button
            onClick={onAddProduct}
            className="flex flex-col items-center justify-center gap-2 p-4 rounded-lg hover:bg-gray-50 transition"
          >
            <IconPlus size={28} stroke={iconStroke} className="text-blue-500" />
            <span className="text-m text-center">Sipariş Ekle</span>
          </button>
        )}
      </div>
    </div>
    <form method="dialog" className="modal-backdrop">
      <button>Kapat</button>
    </form>
  </dialog>
);

export default TableActions;

/**
 * Props Types:
 * @param {Object} table - Masa bilgileri
 * @param {string} status - Masanın durumu ('empty', 'occupied', 'warning', 'locked')
 * @param {Object} tableOrders - Masa siparişleri
 * @param {Function} onClose - Modal kapatma fonksiyonu
 * @param {Function} onPayment - Ödeme fonksiyonu
 * @param {Function} onPrint - Yazdırma fonksiyonu
 * @param {Function} onAddProduct - Ürün ekleme fonksiyonu
 * @param {Function} onTableChange - Masa değiştirme fonksiyonu
 * @param {Function} onTableMerge - Masa birleştirme fonksiyonu
 */