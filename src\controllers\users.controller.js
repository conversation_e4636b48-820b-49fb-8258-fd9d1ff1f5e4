import Api<PERSON>lient from "../helpers/ApiClient";
import use<PERSON><PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);


export function useUsers() {
    const APIURL = `/users`;
    const { data, error, isLoading } = useSWR(APIURL, fetcher);
    return {
      data,
      error,
      isLoading,
      APIURL,
    };
  }


export async function addNewUser(
  username,
  password,
  name,
  designation,
  phone,
  email,
  userScopes,
  isActive = true,
  restrictedFloorIds = []
) {
  try {
    const response = await ApiClient.post("/users/add", {
      username,
      password,
      name,
      designation,
      phone,
      email,
      userScopes,
      isActive,
      restrictedFloorIds
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteUser(username) {
  try {
    const response = await ApiClient.delete(`/users/delete/${username}`)
    return response;
  } catch (error) {
    throw error;
  }
};

export async function resetUserPassword(username, password) {
  try {
    const response = await ApiClient.post(`/users/update-password/${username}`, {
      password
    })
    return response;
  } catch (error) {
    throw error;
  }
};

export async function updateUser(
  username,
  name,
  pin,
  phone,
  email,
  userScopes,
  isActive,
  restrictedFloorIds
) {
  try {
    const response = await ApiClient.post(`/users/update/${username}`, {
      name,
      pin,
      phone,
      email,
      userScopes,
      isActive,
      restrictedFloorIds
    });
    return response;
  } catch (error) {
    throw error;
  }
}

export async function getUserFloorRestrictions(username) {
  try {
    const response = await ApiClient.get(`/users/${username}/floor-restrictions`);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateUserFloorRestrictions(username, restrictedFloorIds) {
  try {
    const response = await ApiClient.post(`/users/${username}/floor-restrictions`, {
      restrictedFloorIds
    });
    return response;
  } catch (error) {
    throw error;
  }
}
