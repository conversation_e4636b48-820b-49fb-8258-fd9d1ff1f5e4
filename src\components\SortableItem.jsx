import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { IconGripVertical } from "@tabler/icons-react";

export default function SortableItem({ id, children, isMenuItem }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 100 : 1,
    opacity: isDragging ? 0.8 : 1,
  };

  // Eğer bu bir menü öğesi ise farklı stil uygula
  if (isMenuItem) {
    return (
      <div
        ref={setNodeRef}
        style={style}
        {...attributes}
        className={`bg-white rounded-lg border mb-2 ${isDragging ? 'shadow-lg' : ''}`}
      >
        <div className="relative w-full h-full">
          {/* Sürükleme tutamacı */}
          <div
            {...listeners}
            className="absolute top-0 left-0 w-full h-full cursor-grab opacity-0"
          />

          {/* <PERSON><PERSON>erik */}
          <div className="w-full">{children}</div>
        </div>
      </div>
    );
  }

  // Kategori için normal stil
  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={`flex bg-white rounded-lg border p-4 mb-2 ${isDragging ? 'shadow-lg' : ''}`}
    >
      {/* Sürükleme ikonu */}
      <button
        {...listeners}
        className="cursor-grab p-2 mr-4 bg-gray-100 hover:bg-gray-200 rounded"
      >
        <IconGripVertical size={20} />
      </button>

      {/* İçerik */}
      <div className="flex-1 items-center">{children}</div>
    </div>
  );
}
