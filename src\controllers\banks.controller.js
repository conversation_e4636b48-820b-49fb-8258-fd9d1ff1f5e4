import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

// Tüm bankaları getir
export function useBanks(activeOnly = true) {
  const params = activeOnly ? "?active_only=true" : "";
  const APIURL = `/banks${params}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  
  return {
    banks: data?.data || [],
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

// Yeni banka ekle
export const addBank = async (bankData) => {
  try {
    const response = await ApiClient.post("/banks", bankData);
    return response;
  } catch (error) {
    throw error;
  }
};

// Banka güncelle
export const updateBank = async (bankId, bankData) => {
  try {
    const response = await ApiClient.put(`/banks/${bankId}`, bankData);
    return response;
  } catch (error) {
    throw error;
  }
};

// Banka sil
export const deleteBank = async (bankId) => {
  try {
    const response = await ApiClient.delete(`/banks/${bankId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

// Banka durumunu değiştir (aktif/pasif)
export const toggleBankStatus = async (bankId, isActive) => {
  try {
    const response = await ApiClient.put(`/banks/${bankId}`, { is_active: isActive });
    return response;
  } catch (error) {
    throw error;
  }
};
