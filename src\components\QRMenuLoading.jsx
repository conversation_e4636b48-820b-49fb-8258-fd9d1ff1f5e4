import React from 'react';
import { IconLoader2 } from '@tabler/icons-react';

export default function QRMenuLoading({ theme = 'classic' }) {
  const getLoadingStyle = () => {
    switch (theme) {
      case 'modern':
        return {
          container: 'w-full min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center',
          content: 'bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl',
          spinner: 'animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto',
          text: 'mt-4 text-blue-800 font-medium'
        };
      case 'minimal':
        return {
          container: 'w-full min-h-screen bg-white flex items-center justify-center font-mono',
          content: 'text-center',
          spinner: 'w-8 h-8 border-2 border-black border-t-transparent rounded-full animate-spin mx-auto',
          text: 'mt-4 text-black text-sm uppercase tracking-wider'
        };
      case 'luxury':
        return {
          container: 'w-full min-h-screen bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 flex items-center justify-center font-serif',
          content: 'text-center',
          spinner: 'w-16 h-16 border-4 border-amber-200 border-t-amber-600 rounded-full animate-spin mx-auto',
          text: 'mt-6 text-amber-800 text-lg'
        };
      default: // classic
        return {
          container: 'w-full min-h-screen flex items-center justify-center bg-gray-50',
          content: 'text-center',
          spinner: 'animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto',
          text: 'mt-4 text-gray-600'
        };
    }
  };

  const styles = getLoadingStyle();

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.spinner}></div>
        <p className={styles.text}>
          {theme === 'luxury' ? 'Loading Luxury Experience...' : 
           theme === 'minimal' ? 'Loading...' :
           theme === 'modern' ? 'Yükleniyor...' : 
           'Lütfen bekleyin...'}
        </p>
      </div>
    </div>
  );
}
