import { knowledgeBase } from '../data/chatbotKnowledgeBase';

// OpenAI API anahtarını .env dosyasından al
const OPENAI_API_KEY = import.meta.env.VITE_OPENAI_API_KEY;

// AI yanıtı gönderen fonksiyon
export const sendMessageToAI = async (userMessage) => {
  try {
    // Önce bilgi tabanından yanıt ara
    const knowledgeBaseResponse = findResponseFromKnowledgeBase(userMessage);

    // Eğer bilgi tabanında yanıt bulunduysa, onu döndür
    if (knowledgeBaseResponse) {
      return knowledgeBaseResponse;
    }

    // Bilgi tabanında yanıt bulunamadıysa, genel bir yanıt döndür
    return `Üzgünüm, bu konuda henüz detaylı bilgim yok. SewPOS sisteminin özellikleri hakkında sorular sorabilirsiniz. Örneğin:

- <PERSON><PERSON><PERSON>n nasıl eklenir?
- Kategori nasıl oluşturulur?
- Sipariş nasıl alınır?
- Rapor nasıl görüntülenir?

Daha spesifik yardım için lütfen destek ekibimizle iletişime geçin.`;

    /* OpenAI API entegrasyonu şu an devre dışı
    // Eğer basit eşleştirme bulunamazsa, OpenAI API'sine istek gönder
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `Sen SewPOS adlı bir Point of Sale sisteminin yardımcı asistanısın.
            Kullanıcılara sistemin nasıl kullanılacağı konusunda yardımcı oluyorsun.
            Cevaplarını kısa, net ve adım adım talimatlar şeklinde ver.
            Sadece SewPOS sistemi hakkında konuş, başka konularda yardımcı olma.
            Bilmediğin bir soru sorulursa, bilmediğini söyle ve kullanıcıyı destek ekibine yönlendir.`
          },
          {
            role: 'user',
            content: userMessage
          }
        ],
        temperature: 0.7,
        max_tokens: 500
      })
    });

    const data = await response.json();
    return data.choices[0].message.content;
    */
  } catch (error) {
    console.error('AI yanıt hatası:', error);
    return 'Üzgünüm, şu anda yanıt veremiyorum. Lütfen daha sonra tekrar deneyin.';
  }
};

// Bilgi tabanından basit anahtar kelime eşleştirmesi yapan fonksiyon
const findResponseFromKnowledgeBase = (userMessage) => {
  const message = userMessage.toLowerCase();

  // Ürün ekleme ile ilgili sorular
  if (
    message.includes('ürün ekle') ||
    message.includes('ürün nasıl eklenir') ||
    message.includes('yeni ürün') ||
    message.includes('ürün oluştur') ||
    message.includes('ürün eklemek') ||
    message.includes('ürün kayıt') ||
    message.includes('ürün tanımla') ||
    (message.includes('ürün') && message.includes('nasıl')) ||
    (message.includes('ürün') && message.includes('ekle'))
  ) {
    const info = knowledgeBase.product_management.add_product;
    const steps = info.steps;
    const link = info.direct_link;
    const modal = info.open_modal ? `\n\nSayfaya gittiğinizde otomatik olarak ürün ekleme formu açılacaktır.` : '';

    return `**${info.title}**\n\n${steps.map((step, index) => `${index + 1}. ${step}`).join('\n')}\n\nHızlı erişim: [Tıklayın ve ürün ekleyin](${link})${modal}`;
  }

  // Ürün düzenleme ile ilgili sorular
  if (
    message.includes('ürün düzenle') ||
    message.includes('ürün nasıl düzenlenir') ||
    message.includes('ürün güncelle')
  ) {
    const info = knowledgeBase.product_management.edit_product;
    const steps = info.steps;
    const link = info.direct_link;

    return `**${info.title}**\n\n${steps.map((step, index) => `${index + 1}. ${step}`).join('\n')}\n\nHızlı erişim: [Tıklayın ve ürünleri düzenleyin](${link})`;
  }

  // Kategori ekleme ile ilgili sorular
  if (
    message.includes('kategori ekle') ||
    message.includes('kategori nasıl eklenir') ||
    message.includes('yeni kategori')
  ) {
    const info = knowledgeBase.product_management.product_categories;
    const steps = info.steps;
    const link = info.direct_link;
    const modal = info.open_modal ? `\n\nSayfaya gittiğinizde otomatik olarak kategori ekleme formu açılacaktır.` : '';

    return `**${info.title}**\n\n${steps.map((step, index) => `${index + 1}. ${step}`).join('\n')}\n\nHızlı erişim: [Tıklayın ve kategori ekleyin](${link})${modal}`;
  }

  // Sipariş oluşturma ile ilgili sorular
  if (
    message.includes('sipariş oluştur') ||
    message.includes('sipariş nasıl oluşturulur') ||
    message.includes('yeni sipariş') ||
    message.includes('sipariş ekle')
  ) {
    const info = knowledgeBase.order_management.create_order;
    const steps = info.steps;
    const link = info.direct_link;

    return `**${info.title}**\n\n${steps.map((step, index) => `${index + 1}. ${step}`).join('\n')}\n\nHızlı erişim: [Tıklayın ve sipariş oluşturun](${link})`;
  }

  // Eğer eşleşme bulunamazsa null döndür (OpenAI API'si kullanılacak)
  return null;
};

// Markdown formatını HTML'e çeviren yardımcı fonksiyon
export const markdownToHtml = (markdown) => {
  if (!markdown) return '';

  // Başlıklar ve kalın metin
  let html = markdown.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // İtalik metin
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Satır sonları
  html = html.replace(/\n/g, '<br>');

  // Linkler (bu aşamada işlenmeyecek, ChatBot.jsx içinde özel olarak işlenecek)
  // html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

  return html;
};
