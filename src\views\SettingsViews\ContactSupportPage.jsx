import React from 'react'
import Page from "../../components/Page";
import { IconBrandGmail, IconCopy, IconExternalLink, IconMail, IconX } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import toast from 'react-hot-toast';

export default function ContactSupportPage() {
  const email = "<EMAIL>";

  return (
    <Page>
      <h3>Destek</h3>

      <div className="mt-6 w-full lg:max-w-lg rounded-2xl px-4 py-3 border flex items-center gap-8 justify-between">
        <p className='text-2xl'>
          Desteğe mi ihtiycanız var?<br/>
          <span className="text-base font-normal">
          Her<PERSON><PERSON> bir sorunla karşılaştığınızda, bizimle iletişime geçin, 24 saat içinde yanıt vereceğiz. <br/> E-posta: <b><u>{email}</u></b>
          </span>
        </p>

        <button onClick={()=>{
          document.getElementById("mailto").showModal();
        }} className='mailtoui flex items-center gap-2 justify-center rounded-full text-white bg-restro-green px-4 py-3'><IconMail stroke={iconStroke} /> {email}</button>
      </div>

      <dialog id="mailto" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <div className="flex items-center justify-between">
            <h3 className="font-bold text-lg">E-posta Gönder!</h3>
            <form method="dialog">
              {/* if there is a button in form, it will close the modal */}
              <button className="btn btn-circle btn-ghost"><IconX stroke={iconStroke} /></button>
            </form>  
          </div>
          
          <div className="flex gap-2 mt-4">
            <input type="email" id='mailto_email' disabled value={email} className='input input-bordered input-disabled flex-1' />
            <button onClick={()=>{
              const mailElem = document.getElementById("mailto_email");

              mailElem.select();

              navigator.clipboard.writeText(mailElem.value);
              toast.success("Copied to clipboard!");
            }} className='btn bg-gray-600 text-white'><IconCopy stroke={iconStroke} />Koyala</button>
          </div>


          <div className="border-b my-6"></div>

          <p className='text-center mb-2'>yada şunlar ile aç...</p>

          <a href={`mailto:${email}`} className='flex items-center gap-2 transition active:scale-95 bg-gray-100 hover:bg-gray-200 px-4 rounded-full py-3 text-gray-600'><IconMail stroke={iconStroke} />Default Mail App</a>

          <a target='_blank' href={`https://mail.google.com/mail/?view=cm&fs=1&to=${email}&su=&cc=&bcc=&body=`} className='flex items-center gap-2 transition active:scale-95 bg-gray-100 hover:bg-gray-200 px-4 rounded-full py-3 text-gray-600 mt-2'><IconBrandGmail stroke={iconStroke} />GMail in Browser</a>
        </div>

      </dialog>
    </Page>
  )
}
