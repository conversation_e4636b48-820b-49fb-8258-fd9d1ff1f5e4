import React from 'react';

const OrderTypeButtons = ({ defaultValue, onChange, enabledTypes }) => {
  const buttonTypes = [
    { value: 'dinein', label: '<PERSON><PERSON>' },
    { value: 'delivery', label: '<PERSON><PERSON>' },
    { value: 'takeaway', label: '<PERSON><PERSON><PERSON><PERSON><PERSON> Satış' },
  ];

  // Etkin türlerin sayısını hesapla
  const enabledButtonTypes = buttonTypes.filter(type => enabledTypes[type.value]);

  // Eğer etkin tür birden azsa, hiçbir şey render etme
  if (enabledButtonTypes.length <= 1) {
    return null;
  }

  return (
    <div className="min-w-64 ">
      <div className="flex space-x-3 ">
        {enabledButtonTypes.map((type) => (
          <button
            key={type.value}
            className={`flex-1 h-8 px-4 text-sm font-medium rounded-lg transition-colors shadow-sm ${
              defaultValue === type.value
                ? 'bg-restro-green text-white shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            onClick={() => onChange(type.value)}
          >
            {type.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default OrderTypeButtons;
