import React, { useRef } from "react";
import Page from "../../components/Page";
import {
  IconPencil,
  IconPlus,
  IconTrash,
  IconBuildingSkyscraper,
} from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { addFloor, deleteFloor, updateFloor, useFloors } from "../../controllers/floors.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";

export default function FloorSettingsPage() {
  const floorNameRef = useRef();
  const floorDescriptionRef = useRef();

  const floorIdRef = useRef();
  const floorNameUpdateRef = useRef();
  const floorDescriptionUpdateRef = useRef();

  const { APIURL, data: floors, error, isLoading } = useFloors();

  if (isLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }

  if (error) {
    console.error(error);
    return <Page className="px-8 py-6"><PERSON><PERSON><PERSON>, Daha Sonra Deneyin!</Page>;
  }

  const btnAddFloor = async () => {
    const name = floorNameRef.current.value;
    const description = floorDescriptionRef.current.value;

    if (!name) {
      toast.error("Kat adı boş olamaz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await addFloor(name, description);

      if (res.status === 201) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message || "Kat başarıyla eklendi!");
        document.getElementById("modal-add").close();
        floorNameRef.current.value = "";
        floorDescriptionRef.current.value = "";
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const btnShowUpdate = (id, name, description) => {
    floorIdRef.current = id;
    floorNameUpdateRef.current.value = name;
    floorDescriptionUpdateRef.current.value = description || "";
    document.getElementById("modal-update").showModal();
  };

  const btnUpdateFloor = async () => {
    const id = floorIdRef.current;
    const name = floorNameUpdateRef.current.value;
    const description = floorDescriptionUpdateRef.current.value;

    if (!name) {
      toast.error("Kat adı boş olamaz!");
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await updateFloor(id, name, description);

      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message || "Kat başarıyla güncellendi!");
        document.getElementById("modal-update").close();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  const btnDelete = async (id) => {
    if (!confirm("Bu katı silmek istediğinize emin misiniz?")) {
      return;
    }

    try {
      toast.loading("Lütfen bekleyin...");
      const res = await deleteFloor(id);

      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message || "Kat başarıyla silindi!");
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Bir şeyler ters gitti!";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  return (
    <Page className="px-8 py-6">
      <div className="flex items-center gap-6">
        <h3 className="text-3xl font-light">Kat Yönetimi</h3>
        <button
          onClick={() => document.getElementById("modal-add").showModal()}
          className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
        >
          <IconPlus size={22} stroke={iconStroke} /> Yeni
        </button>
      </div>

      <div className="mt-8 w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {floors.map((floor) => {
          const { id, title, description } = floor;

          return (
            <div
              key={id}
              className="border px-4 py-3 rounded-2xl flex flex-col gap-4 text-sm"
            >
              <div className="flex items-center gap-2">
                <div className="w-12 h-12 rounded-full flex items-center justify-center bg-gray-100 text-gray-400">
                  <IconBuildingSkyscraper stroke={iconStroke} />
                </div>
                <div>
                  <p className="font-medium">{title}</p>
                  {description && (
                    <p className="text-gray-400">{description}</p>
                  )}
                </div>
                <div className="flex gap-0 ml-auto">
                  <button
                    onClick={() => {
                      btnShowUpdate(id, title, description);
                    }}
                    className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-100 transition active:scale-95"
                  >
                    <IconPencil stroke={iconStroke} />
                  </button>
                  <button
                    onClick={() => {
                      btnDelete(id);
                    }}
                    className="w-8 h-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-100 transition active:scale-95"
                  >
                    <IconTrash stroke={iconStroke} />
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Yeni Kat Ekleme Modal */}
      <dialog id="modal-add" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Kat Ekle</h3>
          <div className="py-4 flex flex-col gap-4">
            <div className="form-control w-full">
              <label className="label">
                <span className="label-text">Kat Adı</span>
              </label>
              <input
                ref={floorNameRef}
                type="text"
                placeholder="Kat adını girin"
                className="input input-bordered w-full"
              />
            </div>
            <div className="form-control w-full">
              <label className="label">
                <span className="label-text">Açıklama (İsteğe bağlı)</span>
              </label>
              <textarea
                ref={floorDescriptionRef}
                className="textarea textarea-bordered h-24"
                placeholder="Açıklama girin"
              ></textarea>
            </div>
          </div>
          <div className="modal-action">
            <form method="dialog">
              <button className="btn btn-sm">İptal</button>
            </form>
            <button onClick={btnAddFloor} className="btn btn-sm btn-primary">
              Ekle
            </button>
          </div>
        </div>
      </dialog>

      {/* Kat Güncelleme Modal */}
      <dialog id="modal-update" className="modal">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Kat Güncelle</h3>
          <div className="py-4 flex flex-col gap-4">
            <div className="form-control w-full">
              <label className="label">
                <span className="label-text">Kat Adı</span>
              </label>
              <input
                ref={floorNameUpdateRef}
                type="text"
                placeholder="Kat adını girin"
                className="input input-bordered w-full"
              />
            </div>
            <div className="form-control w-full">
              <label className="label">
                <span className="label-text">Açıklama (İsteğe bağlı)</span>
              </label>
              <textarea
                ref={floorDescriptionUpdateRef}
                className="textarea textarea-bordered h-24"
                placeholder="Açıklama girin"
              ></textarea>
            </div>
          </div>
          <div className="modal-action">
            <form method="dialog">
              <button className="btn btn-sm">İptal</button>
            </form>
            <button onClick={btnUpdateFloor} className="btn btn-sm btn-primary">
              Güncelle
            </button>
          </div>
        </div>
      </dialog>
    </Page>
  );
}
