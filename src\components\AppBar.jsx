import { IconDeviceTablet, IconChefHat, IconToolsKitchen3, IconUsers, IconLock, IconPhone, IconCopy, IconX, IconMaximize, IconMinimize, IconTrash, IconRefresh } from "@tabler/icons-react";
import { useEffect, useState } from "react";
import { iconStroke } from "../config/config";
import { useNavigate, useLocation } from "react-router-dom"; // useLocation ekledik
import { getUserDetailsInLocalStorage } from "../helpers/UserDetails";
import { SCOPES } from "../config/scopes";
import AppBarDropdown from "./AppBarDropdown";
import { useLock } from '../contexts/LockContext';
import { toast } from "react-hot-toast";
import { getCallHistory } from "../controllers/calls.controller";
import { getMaliModeStatus, enableMaliMode, disableMaliMode } from "../controllers/mali-mode.controller";

export default function AppBar() {
    const navigate = useNavigate();
    const location = useLocation(); // Mevcut konumu almak için useLocation hook'u ekledik
    const { lock, shouldLockPath } = useLock(); // shouldLockPath değerini de alıyoruz
    const user = getUserDetailsInLocalStorage();
    const { role: userRole, scope, tenant_id: tenantId } = user;
    const userScopes = scope?.split(",");
    const [isCallModalOpen, setIsCallModalOpen] = useState(false);
    const [calls, setCalls] = useState([]);
    const [isLoadingCalls, setIsLoadingCalls] = useState(false);
    const [isFullScreen, setIsFullScreen] = useState(false);
    const [maliModeStatus, setMaliModeStatus] = useState(false);
    const [maliModeLoading, setMaliModeLoading] = useState(false);
    const [showPinModal, setShowPinModal] = useState(false);
    const [pinInput, setPinInput] = useState("");

    // Kilitleme butonu sadece POS sayfasında görünecek
    const showLockButton = shouldLockPath;

    // Mali mode butonunu sadece KASIYER scope'una sahip kullanıcılara göster
    const showMaliModeButton = userScopes?.includes(SCOPES.KASIYER);

    useEffect(() => {
        const down = (e) => {
            if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
                e.preventDefault();
            }
        };

        document.addEventListener("keydown", down);
        return () => document.removeEventListener("keydown", down);
    }, []);

    // Mali mode durumunu kontrol et - sayfa yüklendiğinde ve showMaliModeButton değiştiğinde
    useEffect(() => {
        if (showMaliModeButton) {
            checkMaliModeStatus();
        }
    }, [showMaliModeButton]);

    // Sayfa yüklendiğinde mali mode durumunu kontrol et
    useEffect(() => {
        checkMaliModeStatus();
    }, []);

    const checkMaliModeStatus = async () => {
        try {
            const response = await getMaliModeStatus();
            if (response.status === 200) {
                setMaliModeStatus(response.data.mali_mode_active || false);
            }
        } catch (error) {
            console.error("Mali mode durumu kontrol edilemedi:", error);
        }
    };

    // PIN modal fonksiyonları
    const handlePinButtonClick = (number) => {
        if (pinInput.length < 6) {
            setPinInput(prev => prev + number);
        }
    };

    const handlePinClear = () => {
        setPinInput("");
    };

    const handlePinSubmit = () => {
        if (pinInput === "262626") {
            setShowPinModal(false);
            setPinInput("");
            toggleMaliMode();
        } else {
            toast.error("Yanlış PIN! Tekrar deneyin.");
            setPinInput("");
        }
    };

    const handleMaliModeClick = () => {
        setShowPinModal(true);
    };

    const handleRefresh = () => {
        window.location.reload();
    };

    const toggleMaliMode = async () => {
        if (maliModeLoading) return;

        try {
            setMaliModeLoading(true);

            if (maliModeStatus) {
                // Mali modu kapat
                const response = await disableMaliMode();
                if (response.status === 200) {
                    setMaliModeStatus(false);
                    toast.success("Pasifleştirildi");

                    // Sayfayı yenile
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else {
                // Mali modu aç
                const response = await enableMaliMode();
                if (response.status === 200) {
                    setMaliModeStatus(true);
                    toast.success("Başarılı");

                    // Sayfayı yenile
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            }
        } catch (error) {
            const message = error?.response?.data?.message || "Hata tekrar dene!";
            console.error(error);
            toast.error(message);
        } finally {
            setMaliModeLoading(false);
        }
    };

    const menuItems = [
        {
            title: "Satış",
            icon: <IconDeviceTablet stroke={iconStroke} />,
            link: "/dashboard/pos",
            scopes: [SCOPES.POS, SCOPES.GARSON]
        },
        {
            title: "Mutfak",
            icon: <IconChefHat stroke={iconStroke} />,
            link: "/dashboard/kitchen",
            scopes: [SCOPES.POS, SCOPES.MUTFAK]
        },
        {
            title: "Siparişler",
            icon: <IconToolsKitchen3 stroke={iconStroke} />,
            link: "/dashboard/orders",
            scopes: [SCOPES.POS, SCOPES.KASIYER]
        },
        {
            title: "Yönetim",
            icon: <IconToolsKitchen3 stroke={iconStroke} />,
            link: "/dashboard/",
            scopes: [SCOPES.POS, SCOPES.DASHBOARD]
        },
        {
            title: "Kullanıcı",
            icon: <IconUsers stroke={iconStroke} />,
            link: "/dashboard/users",
            scopes: [SCOPES.ADDUSER]
        },
        {
            title: "Yenile",
            icon: <IconRefresh stroke={iconStroke} />,
            action: handleRefresh,
            scopes: []
        },
    ];

    const fetchCallHistory = async () => {
        try {
            setIsLoadingCalls(true);
            const { data } = await getCallHistory(tenantId);
            setCalls(data);
        } catch (error) {
            console.error("Error fetching call history:", error);
            toast.error("Çağrı geçmişi getirilemedi!");
        } finally {
            setIsLoadingCalls(false);
        }
    };

    const handleCopy = (phoneNumber) => {
        navigator.clipboard.writeText(phoneNumber);
        toast.success("Telefon numarası kopyalandı!");
    };

    const toggleFullScreen = () => {
        if (!isFullScreen) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
        setIsFullScreen(!isFullScreen);
    };

    // Kilitleme işlemi için güvenli fonksiyon
    const handleLock = () => {
        if (lock) {
            lock();
        }
    };

    return (
        <>
            <div className="flex items-center justify-between px-4 py-3 border-b border-restro-border-green-light w-full bg-white sticky backdrop-blur-md z-[9999]">
                <div className="flex items-center space-x-4">
                    {menuItems
                        .filter((navItem) => {
                            if (userRole === "admin") return true;
                            if (navItem.scopes.length === 0) return true;
                            return navItem.scopes.every((scope) => userScopes.includes(scope));
                        })
                        .map((item, index) => (
                            <button
                                onClick={() => item.action ? item.action() : navigate(item.link)}
                                key={index}
                                className="flex items-center gap-2 px-4 py-3 transition hover:bg-gray-100 active:scale-90 focus:bg-gray-100 rounded-2xl"
                            >
                                {item.icon}
                                <p className="hidden md:block">{item.title}</p>
                            </button>
                        ))}
                </div>

                <div className="flex items-center gap-4">
                    {/* Kilitleme butonu sadece POS sayfasında gösterilecek */}
                    {showLockButton && (
                        <button
                            onClick={handleLock}
                            className="flex items-center gap-2 px-4 py-3 transition hover:bg-gray-100 active:scale-90 focus:bg-gray-100 rounded-2xl text-gray-500"
                        >
                            <IconLock stroke={iconStroke} size={20} />
                            <p className="hidden md:block">Kilitle</p>
                        </button>
                    )}

                    {/* Mali Mode butonu - sadece KASIYER scope'una sahip kullanıcılara gösterilecek */}
                    {showMaliModeButton && (
                        <button
                            onClick={handleMaliModeClick}
                            disabled={maliModeLoading}
                            className={`flex items-center justify-center w-8 h-8 transition rounded-full active:scale-90 ${
                                maliModeStatus
                                    ? "bg-red-500 hover:bg-red-600"
                                    : "bg-green-500 hover:bg-green-600"
                            }`}
                            title="Sistem Ayarları"
                        >
                            <IconTrash stroke={2} size={16} className="text-white" />
                        </button>
                    )}

                    {/* <button
                        onClick={() => {
                            setIsCallModalOpen(true);
                            fetchCallHistory();
                        }}
                        className="flex items-center gap-2 px-4 py-3 transition hover:bg-gray-100 active:scale-90 focus:bg-gray-100 rounded-2xl text-gray-500"
                    >
                        <IconPhone stroke={iconStroke} size={20} />
                        <p className="hidden md:block">Çağrılar</p>
                    </button> */}

                    <button
                        onClick={toggleFullScreen}
                        className="hidden md:flex items-center gap-2 px-4 py-3 transition hover:bg-gray-100 active:scale-90 focus:bg-gray-100 rounded-2xl text-gray-500"
                    >
                        {isFullScreen ? <IconMinimize stroke={iconStroke} size={20} /> : <IconMaximize stroke={iconStroke} size={20} />}
                    </button>

                    <AppBarDropdown />
                </div>
            </div>

            {/* Modal */}
            {isCallModalOpen && (
                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[10000]">
                    <div className="bg-white w-[90%] max-w-4xl rounded-lg shadow-lg p-6">
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-2xl font-bold">Çağrı Geçmişi</h3>
                            <button
                                onClick={() => setIsCallModalOpen(false)}
                                className="text-red-500 hover:text-red-700"
                            >
                                <IconX size={24} />
                            </button>
                        </div>
                        <div className="max-h-[70vh] overflow-y-auto">
                            {isLoadingCalls ? (
                                <p>Yükleniyor...</p>
                            ) : calls.length > 0 ? (
                                <table className="w-full text-left table-auto border-collapse">
                                    <thead>
                                        <tr className="border-b text-gray-700">
                                            <th className="py-2 px-4">Telefon Numarası</th>
                                            <th className="py-2 px-4">Çağrı Türü</th>
                                            <th className="py-2 px-4">Tarih</th>
                                            <th className="py-2 px-4">İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {calls.map((call, index) => (
                                            <tr
                                                key={index}
                                                className="hover:bg-gray-100 transition"
                                            >
                                                <td className="py-2 px-4">{call.phone_number}</td>
                                                <td className="py-2 px-4">{call.call_type}</td>
                                                <td className="py-2 px-4">
                                                    {new Date(call.created_at).toLocaleString()}
                                                </td>
                                                <td className="py-2 px-4">
                                                    <button
                                                        onClick={() =>
                                                            handleCopy(call.phone_number)
                                                        }
                                                        className="text-blue-500 hover:text-blue-700"
                                                    >
                                                        <IconCopy size={20} />
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            ) : (
                                <p className="text-gray-500 text-sm">Çağrı geçmişi yok.</p>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* PIN Modal */}
            {showPinModal && (
                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[10000]">
                    <div className="bg-white w-[90%] max-w-md rounded-lg shadow-lg p-6">
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-xl font-bold">Sistemi Silmek İçin PIN Girin</h3>
                            <button
                                onClick={() => {
                                    setShowPinModal(false);
                                    setPinInput("");
                                }}
                                className="text-red-500 hover:text-red-700"
                            >
                                <IconX size={24} />
                            </button>
                        </div>

                        {/* PIN Display */}
                        <div className="mb-6">
                            <div className="flex justify-center gap-2 mb-4">
                                {[...Array(6)].map((_, index) => (
                                    <div
                                        key={index}
                                        className={`w-12 h-12 border-2 rounded-lg flex items-center justify-center text-xl font-bold ${
                                            index < pinInput.length
                                                ? "border-green-500 bg-green-50 text-green-700"
                                                : "border-gray-300 bg-gray-50"
                                        }`}
                                    >
                                        {index < pinInput.length ? "●" : ""}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Numeric Keypad */}
                        <div className="grid grid-cols-3 gap-3 mb-6">
                            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((number) => (
                                <button
                                    key={number}
                                    onClick={() => handlePinButtonClick(number.toString())}
                                    className="h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-bold transition active:scale-95"
                                >
                                    {number}
                                </button>
                            ))}
                            <button
                                onClick={handlePinClear}
                                className="h-12 bg-red-100 hover:bg-red-200 text-red-600 rounded-lg font-bold transition active:scale-95"
                            >
                                Temizle
                            </button>
                            <button
                                onClick={() => handlePinButtonClick("0")}
                                className="h-12 bg-gray-100 hover:bg-gray-200 rounded-lg text-xl font-bold transition active:scale-95"
                            >
                                0
                            </button>
                            <button
                                onClick={handlePinSubmit}
                                disabled={pinInput.length !== 6}
                                className={`h-12 rounded-lg font-bold transition active:scale-95 ${
                                    pinInput.length === 6
                                        ? "bg-green-500 hover:bg-green-600 text-white"
                                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                                }`}
                            >
                                Onayla
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}