import React from 'react';

export const FloorFilter = ({ floors, selectedFloor, onSelect }) => (
  <div className="flex gap-2 overflow-x-auto pt-4 pb-2 px-4 scrollbar-hide">
    {floors.map((floor) => (
      <button
        key={floor}
        onClick={() => onSelect(floor)}
        className={`flex-shrink-0 px-4 py-2 rounded-full transition active:scale-95 ${
          selectedFloor === floor 
            ? 'bg-restro-green text-white' 
            : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
        }`}
      >
        {floor === 'all' ? 'Tüm Alanlar' : floor}
      </button>
    ))}
  </div>
);

export default FloorFilter;

/**
 * FloorFilter komponenti kat/alan filtreleme butonlarını gösterir
 * 
 * @param {Array} floors - Katların/alanların listesi
 * @param {string} selectedFloor - Seçili kat/alan
 * @param {Function} onSelect - Kat/alan se<PERSON>me fonksiyonu
 * 
 * @example
 * <FloorFilter
 *   floors={['all', 'Bahçe', 'Teras', 'İç Mekan']}
 *   selectedFloor="Teras"
 *   onSelect={(floor) => handleFloorSelect(floor)}
 * />
 */
