import React from "react";

interface RequireScopesProps {
  requiredScopes: string[];
  userScopes: string[];
  userRole?: string; // Eğer userRole "admin" ise tüm scope izin verilsin
  children: React.ReactNode;
}

const RequireScopes: React.FC<RequireScopesProps> = ({
  requiredScopes,
  userScopes,
  userRole,
  children,
}) => {
  // Eğer kullanıcı admin ise, tüm yetkiler otomatik geçerli olur.
  if (userRole === "admin") {
    return <>{children}</>;
  }

  // Diğer kullanıcılar için gerekli scope'ların kullanıcıya ait olup olmadığını kontrol et.
  const isAuthorized = requiredScopes.every((scope) => userScopes.includes(scope));
  return isAuthorized ? <>{children}</> : null;
};

export default RequireScopes;
