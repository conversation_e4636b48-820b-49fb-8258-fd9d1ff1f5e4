/* QuickEditMenuItemsPage için özel stiller */

/* Textarea'ların görü<PERSON><PERSON>nü input'lara benzetme */
.textarea.textarea-sm {
  line-height: 1.5;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

/* Resize handle'ın görünümünü iyileştirme */
textarea.resize-x {
  resize: horizontal;
}

/* React-select için özel stiller */
.basic-single .select__control {
  border-color: #e5e7eb;
}

.basic-single .select__control:hover {
  border-color: #d1d5db;
}

.basic-single .select__control--is-focused {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 1px #10b981 !important;
}

/* Tablo hücrelerinin minimum genişliği */
.table td:first-child {
  min-width: 150px;
}

.table td:nth-child(2) {
  min-width: 150px;
}

.table td:nth-child(3) {
  min-width: 100px;
}

.table td:nth-child(4) {
  min-width: 150px;
}
