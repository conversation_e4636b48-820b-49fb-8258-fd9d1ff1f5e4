import React from 'react';
import { IconClearAll, IconX, IconClipboardList, IconArmchair2, IconUser, IconPencil, IconTrash } from "@tabler/icons-react";

const QROrdersModal = ({ 
  qrOrders, 
  onClearAll, 
  onSelectOrder, 
  onCancelOrder 
}) => {
  return (
    <dialog id="modal-qrorders" className="modal modal-bottom sm:modal-middle">
      <div className="modal-box p-0">
        <div className="flex justify-between items-center sticky top-0 bg-white/80 backdrop-blur px-6 py-4">
          <h3 className="font-bold text-lg">QR Menü Si<PERSON>leri</h3>
          <form method="dialog" className='flex gap-2'>
            <button 
              onClick={onClearAll} 
              className="rounded-full hover:bg-red-200 transition active:scale-95 bg-red-50 text-red-500 w-9 h-9 flex items-center justify-center"
            >
              <IconClearAll stroke={1.8} />
            </button>
            <button className="rounded-full hover:bg-gray-200 transition active:scale-95 bg-gray-100 text-gray-500 w-9 h-9 flex items-center justify-center">
              <IconX stroke={1.8} />
            </button>
          </form>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 px-6 pb-6">
          {qrOrders?.map((qrOrder, index) => {
            const { customer_type, customer_name, table_title, items, id } = qrOrder;

            return (
              <div key={index} className='flex items-center gap-1 rounded-2xl p-2 border'>
                <div className='w-12 h-12 rounded-full bg-gray-100 text-gray-500 flex items-center justify-center'>
                  <IconClipboardList stroke={1.8} />
                </div>
                <div className='flex-1'>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <IconArmchair2 stroke={1.8} size={14} />
                    <p className=''>{table_title || "N/A"}</p>
                  </div>
                  <div className="flex items-center gap-1 text-xs">
                    <IconUser stroke={1.8} size={14} />
                    <p className=''>{customer_type === "WALKIN" ? "WALKIN" : customer_name}</p>
                  </div>
                  <p className='text-xs'>{items?.length} Sepet Ürünü</p>
                </div>
                <div className="flex flex-col gap-1">
                  <button 
                    onClick={() => onSelectOrder(qrOrder)}  
                    className="rounded-full transition active:scale-95 text-restro-green w-6 h-6 bg-green-50 hover:bg-restro-green-dark hover:text-white flex items-center justify-center"
                  >
                    <IconPencil size={14} stroke={1.8} />
                  </button>

                  <button 
                    onClick={() => onCancelOrder(id)} 
                    className="rounded-full transition active:scale-95 text-red-500 w-6 h-6 bg-red-50 hover:bg-red-200 flex items-center justify-center"
                  >
                    <IconTrash size={14} stroke={1.8} />
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </dialog>
  );
};

export default QROrdersModal;