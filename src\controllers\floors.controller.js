import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

// Tüm katları getiren hook
export function useFloors() {
  const APIURL = `/floors`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);

  return {
    APIURL,
    data: data?.data || [],
    error,
    isLoading,
    mutate,
  };
}

// ID'ye göre kat getiren hook
export function useFloorById(id) {
  const APIURL = `/floors/${id}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);

  return {
    APIURL,
    data: data?.data || null,
    error,
    isLoading,
    mutate,
  };
}

// Yeni kat ekleme
export async function addFloor(name, description) {
  try {
    const response = await ApiClient.post("/floors/add", {
      title: name,
      description,
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// Kat güncelleme
export async function updateFloor(id, name, description) {
  try {
    const response = await ApiClient.post(`/floors/${id}/update`, {
      title: name,
      description,
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// Kat silme
export async function deleteFloor(id) {
  try {
    const response = await ApiClient.delete(`/floors/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}
