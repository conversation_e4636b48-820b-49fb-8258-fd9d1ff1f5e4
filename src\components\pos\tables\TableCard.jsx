import React from 'react';
import { IconDotsVertical, IconLock } from "@tabler/icons-react";
import { iconStroke } from "../../../config/config";

export const TableCard = ({ 
  table, 
  status, 
  tableOrders, 
  timers, 
  onTableClick, 
  onActionsClick,
  calculateTableTotal 
}) => {
  const isOverTime = status === 'warning';
  
  return (
    <div
      onClick={() => onTableClick(table, status)}
      className={`relative p-3 cursor-pointer transition-transform hover:scale-105 rounded-lg min-h-28
        ${status === 'empty' ? 'bg-white shadow hover:shadow-lg' : ''}
        ${status === 'busy' ? 'bg-yellow-500 text-white shadow-lg' : ''}
        ${isOverTime ? 'bg-red-500 text-white shadow-lg animate-pulse' : ''}
        ${status === 'locked' ? 'bg-gray-200 cursor-not-allowed' : ''}`}
    >
      <button 
        onClick={(e) => {
          e.stopPropagation();
          onActionsClick(table.id);
        }}
        className="absolute top-2 right-2 w-14 h-10 flex items-center justify-center bg-black/10 rounded-md hover:bg-black/20 transition-colors"
      >
        <IconDotsVertical 
          size={25} 
          stroke={1.8} 
          className={status === 'empty' ? 'text-gray-500' : 'text-white'} 
        />
      </button>

      <div className="flex flex-col gap-1">
        <div className="flex justify-between items-center">
          <p className="font-bold text-lg">{table.table_title}</p>
          {status === 'locked' && <IconLock size={18} stroke={iconStroke} />}
        </div>
        
        {tableOrders ? (
          <p className="text-sm font-medium">
            {calculateTableTotal(tableOrders).toFixed(2)} ₺
          </p>
        ) : (
          <p className="text-sm opacity-80">{table.floor}</p>
        )}
      </div>
    </div>
  );
};

export default TableCard;