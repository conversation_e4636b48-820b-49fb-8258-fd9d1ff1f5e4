// src/i18n.js
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Statik çeviriler
const staticResources = {
  tr: {
    translation: {
      loading: "<PERSON>üt<PERSON> bekleyin...",
      menuButton: "Men<PERSON>yü Görü<PERSON>üle",
      complaint: "Şikayet",
      suggestion: "Öneri",
      startDate:"Kampanya Başlangıç",
      endDate: "Kampanya Bitiş",
      noCampaignImage: "Resim Buulunamadı",
      campaignImagePlaceholder: "Resim Yok",
      thankYou: "Teşekkür",
      campaigns: "Kampanyalar",
      noCampaigns: "Kampanya bulunamadı.",
      complaintPlaceholder: "Şikayetinizi yazınız...",
      suggestionPlaceholder: "Önerinizi yazınız...",
      thankYouMessage: "Hizmetimizi değerlendirmenizi rica ederiz:",
      send: "<PERSON><PERSON><PERSON>",
      sending: "Gönderiliyor...",
      feedbackSuccess: "Geri bildiriminiz başarıyla gönderildi.",
      feedbackError: "Geri bildirim gönderilirken bir hata oluştu.",
      selectEmojiError: "Lütfen bir emoji seçin.",
      fillFieldsError: "Lütfen tüm alanları doldurun.",
      info: "Bilgi",
      language: "Dil",
      address: "Adres",
      phone: "Telefon",
      email: "E-posta",
      // CategoryPage için eklenen çeviriler
      noSubcategories: "Bu kategoride alt kategori bulunmamaktadır.",
      languageSelection: "Dil Seçimi",
      languageSelected: "{{lang}} seçildi",
    },
  },
  en: {
    translation: {
      loading: "Please wait...",
      menuButton: "View Menu",
      complaint: "Complaint",
      suggestion: "Suggestion",
      thankYou: "Thank You",
      campaigns: "Campaigns",
      noCampaigns: "No campaigns found.",
      complaintPlaceholder: "Write your complaint...",
      suggestionPlaceholder: "Write your suggestion...",
      thankYouMessage: "Please rate our service:",
      send: "Send",
      sending: "Sending...",
      feedbackSuccess: "Your feedback has been successfully sent.",
      feedbackError: "An error occurred while sending feedback.",
      selectEmojiError: "Please select an emoji.",
      fillFieldsError: "Please fill in all fields.",
      info: "Info",
      language: "Language",
      address: "Address",
      phone: "Phone",
      email: "Email",
      // CategoryPage için eklenen çeviriler
      noSubcategories: "There are no subcategories in this category.",
      languageSelection: "Language Selection",
      languageSelected: "{{lang}} selected",
    },
  },
  fr: {
    translation: {
      loading: "Veuillez patienter...",
      menuButton: "Voir le menu",
      complaint: "Plainte",
      suggestion: "Suggestion",
      thankYou: "Merci",
      campaigns: "Campagnes",
      noCampaigns: "Aucune campagne trouvée.",
      complaintPlaceholder: "Écrivez votre plainte...",
      suggestionPlaceholder: "Écrivez votre suggestion...",
      thankYouMessage: "Veuillez évaluer notre service :",
      send: "Envoyer",
      sending: "Envoi...",
      feedbackSuccess: "Votre commentaire a été envoyé avec succès.",
      feedbackError: "Une erreur s'est produite lors de l'envoi du commentaire.",
      selectEmojiError: "Veuillez sélectionner un emoji.",
      fillFieldsError: "Veuillez remplir tous les champs.",
      info: "Info",
      language: "Langue",
      address: "Adresse",
      phone: "Téléphone",
      email: "E-mail",
      // CategoryPage için eklenen çeviriler
      noSubcategories: "Il n'y a pas de sous-catégories dans cette catégorie.",
      languageSelection: "Sélection de la langue",
      languageSelected: "{{lang}} sélectionné",
    },
  },
  de: {
    translation: {
      loading: "Bitte warten...",
      menuButton: "Menü anzeigen",
      complaint: "Beschwerde",
      suggestion: "Vorschlag",
      thankYou: "Danke",
      campaigns: "Kampagnen",
      noCampaigns: "Keine Kampagnen gefunden.",
      complaintPlaceholder: "Schreiben Sie Ihre Beschwerde...",
      suggestionPlaceholder: "Schreiben Sie Ihren Vorschlag...",
      thankYouMessage: "Bitte bewerten Sie unseren Service:",
      send: "Senden",
      sending: "Senden...",
      feedbackSuccess: "Ihr Feedback wurde erfolgreich gesendet.",
      feedbackError: "Beim Senden des Feedbacks ist ein Fehler aufgetreten.",
      selectEmojiError: "Bitte wählen Sie ein Emoji aus.",
      fillFieldsError: "Bitte füllen Sie alle Felder aus.",
      info: "Info",
      language: "Sprache",
      address: "Adresse",
      phone: "Telefon",
      email: "E-Mail",
      // CategoryPage için eklenen çeviriler
      noSubcategories: "Es gibt keine Unterkategorien in dieser Kategorie.",
      languageSelection: "Sprachauswahl",
      languageSelected: "{{lang}} ausgewählt",
    },
  },
};

export const updateI18nResources = (translations) => {
  translations.forEach(translation => {
    const { object_type, language_code, translation: translatedText, object_id } = translation;
    
    // Namespace kontrolü (category, menu_item gibi)
    if (['category', 'menu_item', 'menu_description'].includes(object_type)) {
      // Mevcut i18n instance'ına çeviriyi ekle
      i18n.addResource(
        language_code, 
        object_type, 
        String(object_id), 
        translatedText
      );
    }
  });
};

  
  i18n
    .use(initReactI18next)
    .init({
      resources: staticResources,
      lng: "tr",
      fallbackLng: "tr",
      interpolation: {
        escapeValue: false,
      },
      ns: ["translation", "category", "menu_item"],
      defaultNS: "translation",
      react: {
        useSuspense: false,
      },
      missingKeyHandler: (lng, ns, key, fallbackValue) => {
        console.warn(`Çeviri bulunamadı: ${ns}:${key} (${lng})`);
        return fallbackValue;
      },
    });
  
  export default i18n;