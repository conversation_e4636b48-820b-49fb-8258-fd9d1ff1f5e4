import ApiClient from "../helpers/ApiClient";
import useSWR from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

// ============================================================================
// MODÜL AYARLARI
// ============================================================================

export function useStorageLocationSettings() {
  const APIURL = "/storage-locations/settings";
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export async function updateStorageLocationSettings(settings) {
  try {
    const response = await ApiClient.put("/storage-locations/settings", settings);
    return response;
  } catch (error) {
    throw error;
  }
}

// ============================================================================
// LOKASYON YÖNETİMİ
// ============================================================================

export function useStorageLocations() {
  const APIURL = "/storage-locations";
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export function useStorageLocation(id) {
  const APIURL = id ? `/storage-locations/${id}` : null;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export async function createStorageLocation(locationData) {
  try {
    const response = await ApiClient.post("/storage-locations", locationData);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateStorageLocation(id, locationData) {
  try {
    const response = await ApiClient.put(`/storage-locations/${id}`, locationData);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteStorageLocation(id) {
  try {
    const response = await ApiClient.delete(`/storage-locations/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

// ============================================================================
// FLOOR-LOKASYON EŞLEŞTİRME
// ============================================================================

export function useFloorLocationMappings() {
  const APIURL = "/storage-locations/floors/mappings";
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export async function createFloorLocationMapping(mappingData) {
  try {
    const response = await ApiClient.post("/storage-locations/floors/mappings", mappingData);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function updateFloorLocationMapping(id, mappingData) {
  try {
    const response = await ApiClient.put(`/storage-locations/floors/mappings/${id}`, mappingData);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteFloorLocationMapping(id) {
  try {
    const response = await ApiClient.delete(`/storage-locations/floors/mappings/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

// ============================================================================
// STOK YÖNETİMİ
// ============================================================================

export function useLocationInventory(locationId) {
  const APIURL = locationId ? `/storage-locations/${locationId}/inventory` : null;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export async function addLocationMovement(locationId, movementData) {
  try {
    const response = await ApiClient.post(`/storage-locations/${locationId}/movements`, movementData);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function transferBetweenLocations(transferData) {
  try {
    const response = await ApiClient.post("/storage-locations/transfer", transferData);
    return response;
  } catch (error) {
    throw error;
  }
}

// ============================================================================
// RAPORLAR VE GEÇMİŞ
// ============================================================================

export function useMovementHistory(filters = {}) {
  const queryParams = new URLSearchParams(filters).toString();
  const APIURL = `/storage-locations/movements/history${queryParams ? `?${queryParams}` : ''}`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

export function useLocationSummaryReport() {
  const APIURL = "/storage-locations/summary/report";
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

// ============================================================================
// YARDIMCI FONKSİYONLAR
// ============================================================================

export const LOCATION_TYPES = {
  warehouse: { label: "Depo", icon: "📦", color: "blue" },
  bar: { label: "Bar", icon: "🍸", color: "purple" },
  kitchen: { label: "Mutfak", icon: "👨‍🍳", color: "orange" },
  storage: { label: "Soğuk Depo", icon: "❄️", color: "cyan" }
};

export const MOVEMENT_TYPES = {
  IN: { label: "Giriş", icon: "📥", color: "green" },
  OUT: { label: "Çıkış", icon: "📤", color: "red" },
  TRANSFER_IN: { label: "Transfer Giriş", icon: "🔄", color: "blue" },
  TRANSFER_OUT: { label: "Transfer Çıkış", icon: "🔄", color: "orange" },
  ADJUSTMENT: { label: "Düzeltme", icon: "⚖️", color: "yellow" },
  WASTAGE: { label: "Zayi", icon: "🗑️", color: "gray" }
};

export function getLocationTypeInfo(type) {
  return LOCATION_TYPES[type] || { label: type, icon: "📍", color: "gray" };
}

export function getMovementTypeInfo(type) {
  return MOVEMENT_TYPES[type] || { label: type, icon: "📋", color: "gray" };
}

export function formatLocationCode(name, type) {
  const typePrefix = {
    warehouse: "WH",
    bar: "BAR", 
    kitchen: "KIT",
    storage: "COLD"
  };
  
  const prefix = typePrefix[type] || "LOC";
  const number = String(Math.floor(Math.random() * 99) + 1).padStart(2, '0');
  return `${prefix}${number}`;
}

export function validateLocationData(data) {
  const errors = {};
  
  if (!data.name?.trim()) {
    errors.name = "Lokasyon adı gereklidir";
  }
  
  if (!data.type) {
    errors.type = "Lokasyon türü seçilmelidir";
  }
  
  if (!data.location_code?.trim()) {
    errors.location_code = "Lokasyon kodu gereklidir";
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

export function validateMovementData(data) {
  const errors = {};
  
  if (!data.inventory_item_id) {
    errors.inventory_item_id = "Ürün seçilmelidir";
  }
  
  if (!data.movement_type) {
    errors.movement_type = "Hareket türü seçilmelidir";
  }
  
  if (!data.quantity || data.quantity <= 0) {
    errors.quantity = "Miktar pozitif bir sayı olmalıdır";
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

export function validateTransferData(data) {
  const errors = {};
  
  if (!data.from_location_id) {
    errors.from_location_id = "Kaynak lokasyon seçilmelidir";
  }
  
  if (!data.to_location_id) {
    errors.to_location_id = "Hedef lokasyon seçilmelidir";
  }
  
  if (data.from_location_id === data.to_location_id) {
    errors.to_location_id = "Hedef lokasyon, kaynak lokasyondan farklı olmalıdır";
  }
  
  if (!data.inventory_item_id) {
    errors.inventory_item_id = "Ürün seçilmelidir";
  }
  
  if (!data.quantity || data.quantity <= 0) {
    errors.quantity = "Miktar pozitif bir sayı olmalıdır";
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}
