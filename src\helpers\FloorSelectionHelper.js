/**
 * Floor seçimi için yardımcı fonksiyonlar
 */

// Floor seçimi için localStorage anahtarı
const SELECTED_FLOOR_KEY = "SEWPOS__SELECTED_FLOOR";

/**
 * Seçili floor ID'sini localStorage'a kaydeder
 * @param {number|string} floorId Floor ID
 */
export function saveSelectedFloor(floorId) {
  try {
    console.log("Floor localStorage'a kaydediliyor:", floorId);
    localStorage.setItem(SELECTED_FLOOR_KEY, String(floorId));
  } catch (error) {
    console.error("Floor seçimi kaydedilirken hata oluştu:", error);
  }
}

/**
 * Seçili floor ID'sini localStorage'dan alır
 * @param {Array} availableFloors Mevcut floor listesi
 * @returns {number|string|null} Seçili floor ID veya null
 */
export function getSelectedFloor(availableFloors = []) {
  try {
    const savedFloorId = localStorage.getItem(SELECTED_FLOOR_KEY);
    console.log("localStorage'dan okunan floor:", savedFloorId);
    console.log("Mevcut floor'lar:", availableFloors.map(f => ({ id: f.floor_id, name: f.floor_name })));

    if (savedFloorId && availableFloors.length > 0) {
      // Kaydedilen floor ID'sinin mevcut floor'lar arasında olup olmadığını kontrol et
      // floor_id ile karşılaştır (hem string hem number olarak)
      const floorExists = availableFloors.some(floor =>
        String(floor.floor_id) === String(savedFloorId) ||
        Number(floor.floor_id) === Number(savedFloorId)
      );

      console.log("Floor mevcut mu?", floorExists);

      if (floorExists) {
        // Sayı olarak döndür (TableSelection'da selectedFloor number olarak kullanılıyor)
        const result = Number(savedFloorId);
        console.log("Döndürülen floor:", result);
        return result;
      } else {
        // Kaydedilen floor artık mevcut değilse, ilk floor'u döndür
        const result = availableFloors.length > 0 ? Number(availableFloors[0].floor_id) : null;
        console.log("Varsayılan floor döndürülüyor:", result);
        return result;
      }
    }

    // Kaydedilen floor yoksa veya floor listesi boşsa, ilk floor'u döndür
    const result = availableFloors.length > 0 ? Number(availableFloors[0].floor_id) : null;
    console.log("İlk floor döndürülüyor:", result);
    return result;
  } catch (error) {
    console.error("Floor seçimi alınırken hata oluştu:", error);
    // Hata durumunda ilk floor'u döndür
    return availableFloors.length > 0 ? Number(availableFloors[0].floor_id) : null;
  }
}

/**
 * Floor seçimini localStorage'dan siler
 */
export function clearSelectedFloor() {
  try {
    localStorage.removeItem(SELECTED_FLOOR_KEY);
  } catch (error) {
    console.error("Floor seçimi silinirken hata oluştu:", error);
  }
}
