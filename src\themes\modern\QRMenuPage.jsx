import React, { useEffect, useState, useRef  } from "react";
import { IconChevronRight, IconAlertCircle, IconX } from "@tabler/icons-react";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";
import { ALLERGENS } from "../../constants/allergens";


export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;
  const allergenDialogRef = useRef(null);
  const openAllergenDialog = () => {
    allergenDialogRef.current?.showModal();
  };
  const closeAllergenDialog = () => {
    allergenDialogRef.current?.close();
  };

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories || [],
        });

        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name}`;
        }
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, categories } = state;

  // Dil değiştirme fonksiyonu
  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  if (isLoading) {
    return <QRMenuLoading />;
  }

  return (
    <div className="w-full min-h-screen flex flex-col" style={{ backgroundColor: '#f8f7f3', fontFamily: 'Georgia, "Times New Roman", serif' }}>
      {/* Main Content - Centered */}
      <div className="flex-1 flex items-center justify-center px-6">
        <div className="max-w-md w-full text-center">
          {/* Logo */}
          {storeSettings?.store_image && (
            <img
              src={getImageURL(storeSettings.store_image)}
              alt="Logo"
              className="h-20 object-contain mx-auto mb-8"
            />
          )}
          
          {/* Ana Kategoriler */}
          <div className="w-full">
            {/* Welcome Message */}
            <div className="flex items-center justify-center mb-6">
              <div className="h-px flex-1 max-w-[50px] mx-4 bg-gray-300"></div>
              <span className="text-gray-900 text-base font-normal">HOŞGELDİNİZ</span>
              <div className="h-px flex-1 max-w-[50px] mx-4 bg-gray-300"></div>
            </div>

            {/* Categories List */}
            <div className="space-y-2">
              {categories
                .filter(category => !category.parent_id) // Sadece ana kategoriler
                .map((category) => (
                  <div
                    key={category.id}
                    className="flex justify-between items-center py-2 cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-200"
                    onClick={() =>
                      navigate(
                        encryptedTableId
                          ? `/m/${qrcode}/category?table=${encryptedTableId}`
                          : `/m/${qrcode}/category`,
                        {
                          state: { parentCategory: category.id },
                        }
                      )
                    }
                  >
                    <span className="text-gray-900 text-sm font-normal">
                      {category.title}
                    </span>
                    <IconChevronRight size={16} className="text-gray-400" />
                  </div>
                ))}
            </div>
          </div>

          {/* Modern Alerjen Butonu */}
          <div className="mt-8 mb-4">
            <button
              onClick={openAllergenDialog}
              className="inline-flex items-center gap-2 px-4 py-2 text-sm text-gray-600 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <IconAlertCircle size={16} className="text-amber-500" />
              <span>Alerjen Bilgileri</span>
            </button>
          </div>
        </div>
      </div>

      {/* Footer - Language Options */}
      <footer className="bg-gray-50 border-t border-gray-200">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex justify-center">
            <div className="flex items-center space-x-6">
              <button
                onClick={() => changeLanguage('tr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'tr' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Türkçe
              </button>
              <button
                onClick={() => changeLanguage('en')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'en' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                English
              </button>
              <button
                onClick={() => changeLanguage('fr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'fr' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Français
              </button>
              <button
                onClick={() => changeLanguage('de')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'de' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Deutsch
              </button>
              <button
                onClick={() => changeLanguage('ru')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ru' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Русский
              </button>
            </div>
          </div>
        </div>
      </footer>

      {/* Modern Alerjenler Dialog */}
      <dialog
        ref={allergenDialogRef}
        className="rounded-xl bg-white border border-gray-200 shadow-2xl p-0 max-w-md w-full max-h-[80vh]"
        style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          margin: 0
        }}
        onClick={(e) => e.target === e.currentTarget && closeAllergenDialog()}
      >
        <div className="sticky top-0 bg-white border-b border-gray-100 px-6 py-4 rounded-t-xl">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Alerjen Bilgileri
            </h3>
            <button
              onClick={closeAllergenDialog}
              className="p-1 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <IconX size={20} className="text-gray-500" />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="grid grid-cols-1 gap-3">
            {ALLERGENS.map(({ value, label }) => (
              <div
                key={value}
                className="flex items-center gap-3 p-3 bg-amber-50 border border-amber-100 rounded-lg hover:bg-amber-100 transition-colors"
              >
                <div className="flex-shrink-0 w-8 h-8 bg-white rounded-full flex items-center justify-center border border-amber-200">
                  <img
                    src={`/assets/allergens/${value}.svg`}
                    alt={label}
                    className="w-5 h-5"
                    loading="lazy"
                  />
                </div>
                <span className="text-gray-800 font-medium text-sm">{label}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="sticky bottom-0 bg-white border-t border-gray-100 px-6 py-4 rounded-b-xl">
          <button
            onClick={closeAllergenDialog}
            className="w-full px-4 py-2 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors font-medium"
          >
            Kapat
          </button>
        </div>
      </dialog>
    </div>
  );
}