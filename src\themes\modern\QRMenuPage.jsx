import React, { useEffect, useState } from "react";
import {
  getQRMenuInit,
  sendFeedback,
} from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import {
  IconX,
  IconCategory,
  IconMenuDeep,
  IconInfoCircle,
  IconLanguage,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandInstagram,
  IconBrandWhatsapp,
} from "@tabler/icons-react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";

export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    campaigns: null,
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("dynamic");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          campaigns: data.campaigns,
          storeSettings: data?.storeSettings,
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, campaigns } = state;
  const storeName = storeSettings?.store_name || t("storeName");
  const slides = storeSettings?.slides?.split(",") || [];

  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [slides.length]);

  if (isLoading) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">{t("loading")}</p>
        </div>
      </div>
    );
  }

  // Modern tema stilleri
  const bodyStyles = {
    background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
    minHeight: "100vh"
  };

  const headerStyles = {
    background: "linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)",
    boxShadow: "0 4px 12px rgba(37, 99, 235, 0.3)"
  };

  const MenuButton = {
    background: "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)",
    color: "#ffffff",
    boxShadow: "0 4px 12px rgba(59, 130, 246, 0.3)"
  };

  return (
    <div className="w-full min-h-screen" style={bodyStyles}>
      {/* Modern Header */}
      <header className="w-full py-6" style={headerStyles}>
        <div className="container mx-auto px-4 flex items-center justify-between">
          {storeSettings?.store_image ? (
            <img
              src={getImageURL(storeSettings.store_image)}
              alt={storeName}
              className="h-12 object-contain"
            />
          ) : (
            <h1 className="text-2xl font-bold text-white">{storeName}</h1>
          )}
          <IconMenuDeep size={28} stroke={2} className="cursor-pointer hidden text-white" />
        </div>
      </header>

      {/* Modern Slider */}
      <div className="w-full">
        <div className="container mx-auto">
          <div className="h-64 bg-gradient-to-r from-blue-100 to-indigo-100 flex items-center justify-center overflow-hidden relative rounded-b-3xl">
            {slides.length > 0 ? (
              <>
                <div
                  className="flex transition-transform duration-500"
                  style={{
                    transform: `translateX(-${currentSlide * 100}%)`,
                    width: `${slides.length * 100}%`,
                  }}
                >
                  {slides.map((slide, index) => (
                    <img
                      key={index}
                      src={getImageURL(slide)}
                      alt={`Slide ${index + 1}`}
                      className="w-full h-64 object-cover"
                      style={{ flex: "0 0 100%" }}
                    />
                  ))}
                </div>
                <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                  {slides.map((_, index) => (
                    <button
                      key={index}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        currentSlide === index 
                          ? "bg-white scale-125" 
                          : "bg-white/50 hover:bg-white/75"
                      }`}
                      onClick={() => setCurrentSlide(index)}
                    />
                  ))}
                </div>
              </>
            ) : (
              <p className="text-blue-600 font-medium">{t("noSlides", { ns: "dynamic" })}</p>
            )}
          </div>
        </div>
      </div>

      {/* Modern Social Links */}
      <div className="w-full">
        <div className="container mx-auto px-4">
          <div className="flex justify-center gap-6 mt-6 mb-8">
            {storeSettings?.facebook && (
              <a 
                href={storeSettings.facebook} 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <IconBrandFacebook size={28} stroke={1.5} className="text-blue-600" />
              </a>
            )}
            {storeSettings?.twitter && (
              <a 
                href={storeSettings.twitter} 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <IconBrandTwitter size={28} stroke={1.5} className="text-blue-400" />
              </a>
            )}
            {storeSettings?.instagram && (
              <a 
                href={storeSettings.instagram} 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <IconBrandInstagram size={28} stroke={1.5} className="text-pink-500" />
              </a>
            )}
            {storeSettings?.whatsapp && (
              <a 
                href={storeSettings.whatsapp} 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-3 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <IconBrandWhatsapp size={28} stroke={1.5} className="text-green-500" />
              </a>
            )}
          </div>
        </div>
      </div>

      {/* Modern Menu Button */}
      <div className="w-full">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-4 mt-4">
            <button
              onClick={() =>
                navigate(
                  encryptedTableId
                    ? `/m/${qrcode}/category?table=${encryptedTableId}`
                    : `/m/${qrcode}/category`
                )
              }
              className="flex h-24 items-center justify-center gap-3 p-6 rounded-2xl text-center font-bold text-xl transition-all duration-300 hover:scale-105 hover:shadow-xl"
              style={MenuButton}
            >
              <IconCategory size={28} stroke={1.5} /> {t("menuButton")}
            </button>
          </div>
        </div>
      </div>

      {/* Modern Campaigns */}
      <div className="w-full mt-8 pb-24">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            {t("campaigns")}
          </h2>
          {campaigns && campaigns.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {campaigns.map((campaign) => (
                <div
                  key={campaign.id}
                  className="bg-white/90 backdrop-blur-sm border border-white/20 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 cursor-pointer hover:-translate-y-2"
                >
                  <div className="h-48 bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                    {campaign.image_url ? (
                      <img
                        src={getImageURL(campaign.image_url)}
                        alt={campaign.campaign_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <p className="text-blue-600 text-sm font-medium">{t("noCampaignImage")}</p>
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{campaign.campaign_name}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {campaign.description || t("noDescription", { ns: "dynamic" })}
                    </p>
                    <div className="text-sm text-blue-600 space-y-1">
                      <p className="font-medium">{t("startDate")}: {new Date(campaign.start_date).toLocaleDateString("tr-TR")}</p>
                      <p className="font-medium">{t("endDate")}: {new Date(campaign.end_date).toLocaleDateString("tr-TR")}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
                <p className="text-gray-500 text-lg">{t("noCampaigns")}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modern Footer */}
      <div className="fixed bottom-0 left-0 w-full py-4 bg-white/90 backdrop-blur-sm shadow-lg flex justify-between items-center px-6 border-t border-white/20">
        <button className="p-3 rounded-full bg-blue-100 hover:bg-blue-200 transition-colors">
          <IconInfoCircle size={24} className="text-blue-600" />
        </button>
        <button className="p-3 rounded-full bg-blue-100 hover:bg-blue-200 transition-colors">
          <IconLanguage size={24} className="text-blue-600" />
        </button>
      </div>
    </div>
  );
}
