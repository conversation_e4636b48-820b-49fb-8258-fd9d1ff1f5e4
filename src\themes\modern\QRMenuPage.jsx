import React, { useEffect, useState } from "react";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
  });

  useEffect(() => {
    _getQRMenu(qrcode);
  }, [qrcode]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
        });

        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name}`;
        }
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings } = state;

  // Dil değiştirme fonksiyonu
  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  if (isLoading) {
    return <QRMenuLoading />;
  }

  return (
    <div className="w-full min-h-screen bg-gray-50 flex flex-col">
      {/* Main Content - Centered */}
      <div className="flex-1 flex items-center justify-center px-6">
        <div className="max-w-md w-full text-center">
          {/* Logo */}
          {storeSettings?.store_image && (
            <img
              src={getImageURL(storeSettings.store_image)}
              alt="Logo"
              className="h-20 object-contain mx-auto mb-8"
            />
          )}
          
          {/* Menu Button */}
          <button
            onClick={() =>
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/category?table=${encryptedTableId}`
                  : `/m/${qrcode}/category`
              )
            }
            className="w-full text-black border-2 border-solid py-4 px-8 rounded-lg font-semibold text-lg  transition-all duration-200 "
          >
            {t("menuButton", { defaultValue: "Menüyü Gör" })}
          </button>
        </div>
      </div>

      {/* Footer - Language Options */}
      <footer className="bg-gray-50 border-t border-gray-200">
        <div className="max-w-4xl mx-auto px-6 py-6">
          <div className="flex justify-center">
            <div className="flex items-center space-x-6">
              <button
                onClick={() => changeLanguage('tr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'tr' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Türkçe
              </button>
              <button
                onClick={() => changeLanguage('en')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'en' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                English
              </button>
              <button
                onClick={() => changeLanguage('fr')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ar' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Français
              </button>
              <button
                onClick={() => changeLanguage('de')}
                className={`text-sm font-medium transition-colors ${
                  i18n.language === 'ru' ? 'text-gray-800' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Deutsch
              </button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}