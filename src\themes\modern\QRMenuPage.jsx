import React, { useEffect, useState } from "react";
import {
  getQRMenuInit,
  sendFeedback,
} from "../../controllers/qrmenu.controller";
import { getImageURL } from "../../helpers/ImageHelper";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import {
  IconCategory,
  IconInfoCircle,
  IconLanguage,
  IconBrandFacebook,
  IconBrandTwitter,
  IconBrandInstagram,
  IconBrandWhatsapp,
  IconSparkles,
  IconTrendingUp,
  IconStar,
  IconHeart,
  IconMessageCircle,
  IconThumbUp,
} from "@tabler/icons-react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";
import QRMenuLoading from "../../components/QRMenuLoading";

export default function QRMenuPage() {
  const { t, i18n } = useTranslation(["translation", "dynamic"]);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    campaigns: null,
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("dynamic");
  }, [qrcode, i18n.language]);

  // Sayfa başlığını güncelle
  useEffect(() => {
    if (storeSettings?.store_name) {
      document.title = `${storeSettings.store_name} - Menu - SewPOS`;
    }
  }, [storeSettings]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        if (data?.translations) {
          updateI18nResources(data.translations);
        }
        setState({
          isLoading: false,
          campaigns: data.campaigns,
          storeSettings: data?.storeSettings,
        });

        // Sayfa başlığını güncelle
        if (data?.storeSettings?.store_name) {
          document.title = `${data.storeSettings.store_name} - SewPOS`;
        }
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, campaigns } = state;
  const storeName = storeSettings?.store_name || t("storeName");
  const slides = storeSettings?.slides?.split(",") || [];

  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [slides.length]);

  if (isLoading) {
    return <QRMenuLoading />;
  }

  // Modern tema stilleri
  const bodyStyles = {
    background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
    minHeight: "100vh"
  };

  const headerStyles = {
    background: "linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)",
    boxShadow: "0 4px 12px rgba(37, 99, 235, 0.3)"
  };

  const MenuButton = {
    background: "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)",
    color: "#ffffff",
    boxShadow: "0 4px 12px rgba(59, 130, 246, 0.3)"
  };

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Modern Floating Header */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>

        <header className="relative z-10 px-6 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <IconSparkles size={32} className="text-yellow-300 animate-pulse" />
              {storeSettings?.store_image ? (
                <img
                  src={getImageURL(storeSettings.store_image)}
                  alt={storeName}
                  className="h-16 object-contain"
                />
              ) : (
                <h1 className="text-4xl font-bold text-white tracking-wide">{storeName}</h1>
              )}
              <IconSparkles size={32} className="text-yellow-300 animate-pulse" />
            </div>
            <p className="text-blue-100 text-lg font-medium">
              {t("welcomeMessage", { ns: "dynamic", defaultValue: "Dijital Menü Deneyimi" })}
            </p>
          </div>
        </header>
      </div>

      {/* Modern Interactive Slider */}
      <div className="px-6 -mt-8 relative z-20">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/90 backdrop-blur-md rounded-3xl shadow-2xl overflow-hidden border border-white/20">
            {slides.length > 0 ? (
              <div className="relative">
                <div className="h-80 overflow-hidden">
                  <div
                    className="flex transition-all duration-700 ease-out"
                    style={{
                      transform: `translateX(-${currentSlide * 100}%)`,
                      width: `${slides.length * 100}%`,
                    }}
                  >
                    {slides.map((slide, index) => (
                      <div key={index} className="relative" style={{ flex: "0 0 100%" }}>
                        <img
                          src={getImageURL(slide)}
                          alt={`Slide ${index + 1}`}
                          className="w-full h-80 object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Modern Slide Indicators */}
                <div className="absolute bottom-6 left-0 right-0 flex justify-center gap-3">
                  {slides.map((_, index) => (
                    <button
                      key={index}
                      className={`transition-all duration-300 ${
                        currentSlide === index
                          ? "w-8 h-3 bg-white rounded-full"
                          : "w-3 h-3 bg-white/60 rounded-full hover:bg-white/80"
                      }`}
                      onClick={() => setCurrentSlide(index)}
                    />
                  ))}
                </div>
              </div>
            ) : (
              <div className="h-80 flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100">
                <div className="text-center">
                  <IconSparkles size={48} className="text-blue-400 mx-auto mb-4" />
                  <p className="text-blue-600 font-medium text-lg">
                    {t("noSlides", { ns: "dynamic", defaultValue: "Yakında özel içerikler..." })}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modern Action Cards */}
      <div className="px-6 mt-12">
        <div className="max-w-4xl mx-auto">
          {/* Social Links */}
          {(storeSettings?.facebook || storeSettings?.twitter || storeSettings?.instagram || storeSettings?.whatsapp) && (
            <div className="flex justify-center gap-4 mb-8">
              {storeSettings?.facebook && (
                <a
                  href={storeSettings.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                >
                  <IconBrandFacebook size={24} className="text-blue-600 group-hover:scale-110 transition-transform" />
                </a>
              )}
              {storeSettings?.twitter && (
                <a
                  href={storeSettings.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                >
                  <IconBrandTwitter size={24} className="text-blue-400 group-hover:scale-110 transition-transform" />
                </a>
              )}
              {storeSettings?.instagram && (
                <a
                  href={storeSettings.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                >
                  <IconBrandInstagram size={24} className="text-pink-500 group-hover:scale-110 transition-transform" />
                </a>
              )}
              {storeSettings?.whatsapp && (
                <a
                  href={storeSettings.whatsapp}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group p-4 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                >
                  <IconBrandWhatsapp size={24} className="text-green-500 group-hover:scale-110 transition-transform" />
                </a>
              )}
            </div>
          )}

          {/* Main Menu Button */}
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl blur opacity-25 group-hover:opacity-75 transition duration-1000 group-hover:duration-200"></div>
            <button
              onClick={() =>
                navigate(
                  encryptedTableId
                    ? `/m/${qrcode}/category?table=${encryptedTableId}`
                    : `/m/${qrcode}/category`
                )
              }
              className="relative w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-6 px-8 rounded-3xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 flex items-center justify-center gap-4"
            >
              <IconCategory size={32} stroke={1.5} />
              <span>{t("menuButton", { defaultValue: "Menüyü Keşfet" })}</span>
              <IconTrendingUp size={24} className="animate-bounce" />
            </button>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-3 gap-4 mt-8">
            <button className="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <IconHeart size={24} className="text-red-500 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <p className="text-sm font-medium text-gray-700">{t("favorites", { defaultValue: "Favoriler" })}</p>
            </button>

            <button className="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <IconStar size={24} className="text-yellow-500 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <p className="text-sm font-medium text-gray-700">{t("popular", { defaultValue: "Popüler" })}</p>
            </button>

            <button className="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <IconMessageCircle size={24} className="text-blue-500 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <p className="text-sm font-medium text-gray-700">{t("feedback", { defaultValue: "Geri Bildirim" })}</p>
            </button>
          </div>
        </div>
      </div>

      {/* Modern Campaigns */}
      <div className="w-full mt-8 pb-24">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            {t("campaigns")}
          </h2>
          {campaigns && campaigns.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {campaigns.map((campaign) => (
                <div
                  key={campaign.id}
                  className="bg-white/90 backdrop-blur-sm border border-white/20 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 cursor-pointer hover:-translate-y-2"
                >
                  <div className="h-48 bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                    {campaign.image_url ? (
                      <img
                        src={getImageURL(campaign.image_url)}
                        alt={campaign.campaign_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <p className="text-blue-600 text-sm font-medium">{t("noCampaignImage")}</p>
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{campaign.campaign_name}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {campaign.description || t("noDescription", { ns: "dynamic" })}
                    </p>
                    <div className="text-sm text-blue-600 space-y-1">
                      <p className="font-medium">{t("startDate")}: {new Date(campaign.start_date).toLocaleDateString("tr-TR")}</p>
                      <p className="font-medium">{t("endDate")}: {new Date(campaign.end_date).toLocaleDateString("tr-TR")}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
                <p className="text-gray-500 text-lg">{t("noCampaigns")}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modern Footer */}
      <div className="fixed bottom-0 left-0 w-full py-4 bg-white/90 backdrop-blur-sm shadow-lg flex justify-between items-center px-6 border-t border-white/20">
        <button className="p-3 rounded-full bg-blue-100 hover:bg-blue-200 transition-colors">
          <IconInfoCircle size={24} className="text-blue-600" />
        </button>
        <button className="p-3 rounded-full bg-blue-100 hover:bg-blue-200 transition-colors">
          <IconLanguage size={24} className="text-blue-600" />
        </button>
      </div>
    </div>
  );
}
