import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

// Çalışma saatlerini getiren hook
export function useBusinessHours() {
  const APIURL = `/business-hours`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data: data?.data || null,
    error,
    isLoading,
    mutate,
    APIURL,
  };
}

// Çalışma saatlerini güncelleme
export async function updateBusinessHours(dayStartTime, dayEndTime, isOvernight) {
  try {
    const response = await ApiClient.put("/business-hours", {
      day_start_time: dayStartTime,
      day_end_time: dayEndTime,
      is_overnight: isOvernight
    });
    return response;
  } catch (error) {
    throw error;
  }
}

// Çalışma saatlerini kontrol etme
export async function checkBusinessHours(checkTime = null) {
  try {
    const url = checkTime ? `/business-hours/check?checkTime=${checkTime}` : `/business-hours/check`;
    const response = await ApiClient.get(url);
    return response;
  } catch (error) {
    throw error;
  }
}

// Çalışma saatlerini silme
export async function deleteBusinessHours() {
  try {
    const response = await ApiClient.delete("/business-hours");
    return response;
  } catch (error) {
    throw error;
  }
}
