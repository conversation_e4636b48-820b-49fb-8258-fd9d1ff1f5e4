import React, { useEffect, useState } from "react";
import { IconChevronLeft, IconCategory } from "@tabler/icons-react";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { getQRMenuInit } from "../../controllers/qrmenu.controller";
import { CURRENCIES } from "../../config/currencies.config";
import { getImageURL } from "../../helpers/ImageHelper";
import { useTranslation } from "react-i18next";
import { updateI18nResources } from "../../i18n";

export default function CategoryPage() {
  const { t, i18n } = useTranslation(['category']);
  const navigate = useNavigate();
  const params = useParams();
  const qrcode = params.qrcode;
  const [searchParams] = useSearchParams();
  const encryptedTableId = searchParams.get("table") ?? null;

  const [state, setState] = useState({
    isLoading: true,
    storeSettings: null,
    categories: [],
    currency: "",
  });

  useEffect(() => {
    _getQRMenu(qrcode);
    i18n.loadNamespaces("category");
  }, [qrcode, i18n.language]);

  const _getQRMenu = async (qrcode) => {
    try {
      const res = await getQRMenuInit(qrcode, encryptedTableId);
      if (res.status === 200) {
        const data = res.data;
        const currency = CURRENCIES.find(
          (c) => c.cc === data?.storeSettings?.currency
        );
        if (data?.translations) {
          updateI18nResources(data.translations);
        }

        setState({
          isLoading: false,
          storeSettings: data?.storeSettings,
          categories: data?.categories,
          currency: currency?.symbol || "",
        });
      }
    } catch (error) {
      console.error("QR Menu loading error:", error);
    }
  };

  const { isLoading, storeSettings, categories } = state;
  const storeName = storeSettings?.store_name || "";

  if (isLoading) {
    return (
      <div className="w-full min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-black border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-black font-mono text-sm uppercase tracking-wider">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-white font-mono">
      {/* MINIMAL HEADER - Siyah çizgi */}
      <header className="w-full bg-black text-white sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <IconChevronLeft
            size={24}
            stroke={2}
            className="cursor-pointer hover:bg-white hover:text-black transition-colors p-1 rounded"
            onClick={() => navigate(-1)}
          />
          <div className="flex items-center">
            {storeSettings?.store_image ? (
              <img
                src={getImageURL(storeSettings.store_image)}
                alt={storeName}
                className="h-8 object-contain filter invert"
              />
            ) : (
              <h1 className="text-lg font-bold uppercase tracking-wider">{storeName}</h1>
            )}
          </div>
          <div className="w-6"></div>
        </div>
      </header>

      <div className="container mx-auto px-4">
        {/* MINIMAL TITLE */}
        <div className="py-12 text-center border-b border-gray-200">
          <h2 className="text-4xl font-bold text-black uppercase tracking-widest mb-2">
            CATEGORIES
          </h2>
          <div className="w-16 h-0.5 bg-black mx-auto"></div>
        </div>

        {/* MINIMAL CATEGORIES - Liste formatında */}
        <div className="py-8 space-y-1">
          {categories.map((category, index) => {
            const { id, name, image } = category;
            const imageURL = getImageURL(image);

            return (
              <div
                key={id}
                className="group border-b border-gray-100 hover:border-black transition-colors cursor-pointer"
                onClick={() => {
                  navigate(
                    encryptedTableId
                      ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                      : `/m/${qrcode}/menu`,
                    {
                      state: { selectedCategory: id },
                    }
                  );
                }}
              >
                <div className="py-6 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-black group-hover:w-4 transition-all duration-300"></div>
                    <div className="w-12 h-12 border border-gray-300 flex items-center justify-center">
                      {image ? (
                        <img
                          src={imageURL}
                          alt={name}
                          className="w-full h-full object-cover grayscale"
                        />
                      ) : (
                        <IconCategory size={20} className="text-gray-400" />
                      )}
                    </div>
                    <h3 className="text-xl font-bold text-black uppercase tracking-wide group-hover:tracking-widest transition-all">
                      {t(`category:${id}`, { defaultValue: name })}
                    </h3>
                  </div>
                  <div className="text-2xl font-light text-gray-400 group-hover:text-black transition-colors">
                    {String(index + 1).padStart(2, '0')}
                  </div>
                </div>
              </div>
            );
          })}

          {/* MINIMAL "ALL CATEGORIES" */}
          <div
            className="group border-b-2 border-black hover:bg-black hover:text-white transition-all cursor-pointer"
            onClick={() => {
              navigate(
                encryptedTableId
                  ? `/m/${qrcode}/menu?table=${encryptedTableId}`
                  : `/m/${qrcode}/menu`,
                {
                  state: { selectedCategory: "all" },
                }
              );
            }}
          >
            <div className="py-8 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-4 h-4 bg-black group-hover:bg-white transition-colors"></div>
                <div className="w-12 h-12 border-2 border-black group-hover:border-white flex items-center justify-center">
                  <IconCategory size={20} className="text-black group-hover:text-white" />
                </div>
                <h3 className="text-2xl font-bold text-black group-hover:text-white uppercase tracking-widest">
                  ALL CATEGORIES
                </h3>
              </div>
              <div className="text-3xl font-light text-black group-hover:text-white">
                ∞
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
