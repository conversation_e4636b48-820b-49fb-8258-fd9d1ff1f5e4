import React, { useRef, useState } from "react";
import Page from "../../components/Page";
import { IconPencil, IconPlus, IconTrash } from "@tabler/icons-react";
import { iconStroke } from "../../config/config";
import { useFloors } from "../../controllers/floors.controller";
import { usePrinters } from "../../controllers/settings.controller";
import { useCategories } from "../../controllers/settings.controller";
import { 
  useFloorPrinterMappings, 
  addFloorPrinterMapping, 
  updateFloorPrinterMapping, 
  deleteFloorPrinterMapping 
} from "../../controllers/printer-mappings.controller";
import toast from "react-hot-toast";
import { mutate } from "swr";

export default function PrinterMappingPage() {
  // Refs for Add Modal
  const floorIdAddRef = useRef();
  const categoryIdAddRef = useRef();
  const printerIdAddRef = useRef();
  const printerTypeAddRef = useRef();

  // Refs for Update Modal
  const mappingIdUpdateRef = useRef();
  const floorIdUpdateRef = useRef();
  const categoryIdUpdateRef = useRef();
  const printerIdUpdateRef = useRef();
  const printerTypeUpdateRef = useRef();

  // Fetch data
  const { data: floors, error: floorsError, isLoading: floorsLoading } = useFloors();
  const { data: printers, error: printersError, isLoading: printersLoading } = usePrinters();
  const { data: categories, error: categoriesError, isLoading: categoriesLoading } = useCategories();
  const { mappings, isLoading: mappingsLoading, isError: mappingsError, APIURL } = useFloorPrinterMappings();

  // Loading state
  if (floorsLoading || printersLoading || categoriesLoading || mappingsLoading) {
    return <Page className="px-8 py-6">Lütfen bekleyin...</Page>;
  }

  // Error state
  if (floorsError || printersError || categoriesError || mappingsError) {
    console.error(floorsError || printersError || categoriesError || mappingsError);
    return <Page className="px-8 py-6">Veriler yüklenirken hata oluştu, Daha Sonra Deneyin!</Page>;
  }

  // Add new mapping
  const btnAddMapping = async () => {
    const floorId = floorIdAddRef.current.value;
    const categoryId = categoryIdAddRef.current.value === "" ? null : categoryIdAddRef.current.value;
    const printerId = printerIdAddRef.current.value;
    const printerType = printerTypeAddRef.current.value;

    if (!floorId || !printerId || !printerType) {
      toast.error("Lütfen gerekli alanları doldurun");
      return;
    }

    try {
      toast.loading("Eşleştirme ekleniyor...");
      const res = await addFloorPrinterMapping(floorId, categoryId, printerId, printerType);
      
      if (res.status === 201) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
        document.getElementById("modal-add-mapping").close();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Eşleştirme eklenirken bir hata oluştu";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  // Update mapping
  const btnUpdateMapping = async () => {
    const mappingId = mappingIdUpdateRef.current.value;
    const floorId = floorIdUpdateRef.current.value;
    const categoryId = categoryIdUpdateRef.current.value === "" ? null : categoryIdUpdateRef.current.value;
    const printerId = printerIdUpdateRef.current.value;
    const printerType = printerTypeUpdateRef.current.value;

    if (!mappingId || !floorId || !printerId || !printerType) {
      toast.error("Lütfen gerekli alanları doldurun");
      return;
    }

    try {
      toast.loading("Eşleştirme güncelleniyor...");
      const res = await updateFloorPrinterMapping(mappingId, floorId, categoryId, printerId, printerType);
      
      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
        document.getElementById("modal-update-mapping").close();
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Eşleştirme güncellenirken bir hata oluştu";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  // Delete mapping
  const btnDeleteMapping = async (mappingId) => {
    if (!confirm("Bu eşleştirmeyi silmek istediğinize emin misiniz?")) {
      return;
    }

    try {
      toast.loading("Eşleştirme siliniyor...");
      const res = await deleteFloorPrinterMapping(mappingId);
      
      if (res.status === 200) {
        await mutate(APIURL);
        toast.dismiss();
        toast.success(res.data.message);
      }
    } catch (error) {
      const message = error?.response?.data?.message || "Eşleştirme silinirken bir hata oluştu";
      console.error(error);
      toast.dismiss();
      toast.error(message);
    }
  };

  // Open update modal
  const openUpdateModal = (mapping) => {
    mappingIdUpdateRef.current.value = mapping.id;
    floorIdUpdateRef.current.value = mapping.floor_id;
    categoryIdUpdateRef.current.value = mapping.category_id || "";
    printerIdUpdateRef.current.value = mapping.printer_id;
    printerTypeUpdateRef.current.value = mapping.printer_type;
    
    document.getElementById("modal-update-mapping").showModal();
  };

  // Get floor name by ID
  const getFloorName = (floorId) => {
    const floor = floors.find(f => f.id === floorId);
    return floor ? floor.title : "Bilinmeyen Kat";
  };

  // Get category name by ID
  const getCategoryName = (categoryId) => {
    if (!categoryId) return "Tüm Kategoriler";
    const category = categories.find(c => c.id === categoryId);
    return category ? category.title : "Bilinmeyen Kategori";
  };

  // Get printer name by ID
  const getPrinterName = (printerId) => {
    const printer = printers.find(p => p.id === printerId);
    return printer ? printer.name : "Bilinmeyen Yazıcı";
  };

  // Get printer type label
  const getPrinterTypeLabel = (type) => {
    return type === "kitchen" ? "Mutfak Yazıcısı" : "Fiş Yazıcısı";
  };

  return (
    <Page className="px-8 py-6">
      <div className="flex items-center gap-6">
        <h3 className="text-3xl font-light">Kat-Yazıcı Eşleştirmeleri</h3>
        <button
          onClick={() => document.getElementById("modal-add-mapping").showModal()}
          className="rounded-lg border bg-gray-50 hover:bg-gray-100 transition active:scale-95 hover:shadow-lg text-gray-500 px-2 py-1 flex items-center gap-1"
        >
          <IconPlus size={22} stroke={iconStroke} /> Yeni
        </button>
      </div>

      <div className="mt-8">
        <div className="overflow-x-auto">
          <table className="table w-full">
            <thead>
              <tr>
                <th>Kat</th>
                <th>Kategori</th>
                <th>Yazıcı</th>
                <th>Yazıcı Tipi</th>
                <th>İşlemler</th>
              </tr>
            </thead>
            <tbody>
              {mappings.length === 0 ? (
                <tr>
                  <td colSpan="5" className="text-center py-4">
                    Henüz eşleştirme bulunmuyor
                  </td>
                </tr>
              ) : (
                mappings.map((mapping) => (
                  <tr key={mapping.id} className="hover:bg-gray-50">
                    <td>{getFloorName(mapping.floor_id)}</td>
                    <td>{getCategoryName(mapping.category_id)}</td>
                    <td>{getPrinterName(mapping.printer_id)}</td>
                    <td>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        mapping.printer_type === 'kitchen' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {getPrinterTypeLabel(mapping.printer_type)}
                      </span>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => openUpdateModal(mapping)}
                          className="rounded-full hover:bg-gray-200 transition active:scale-95 bg-gray-100 text-gray-500 w-8 h-8 flex items-center justify-center"
                        >
                          <IconPencil size={16} stroke={iconStroke} />
                        </button>
                        <button
                          onClick={() => btnDeleteMapping(mapping.id)}
                          className="rounded-full hover:bg-red-200 transition active:scale-95 bg-red-50 text-red-500 w-8 h-8 flex items-center justify-center"
                        >
                          <IconTrash size={16} stroke={iconStroke} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Mapping Modal */}
      <dialog id="modal-add-mapping" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Yeni Kat-Yazıcı Eşleştirmesi</h3>
          
          <div className="my-4">
            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Kat</label>
              <select
                ref={floorIdAddRef}
                className="select select-bordered w-full"
                required
              >
                <option value="">Kat Seçin</option>
                {floors.map((floor) => (
                  <option key={floor.id} value={floor.id}>
                    {floor.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Kategori (Opsiyonel)</label>
              <select
                ref={categoryIdAddRef}
                className="select select-bordered w-full"
              >
                <option value="">Tüm Kategoriler</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Yazıcı</label>
              <select
                ref={printerIdAddRef}
                className="select select-bordered w-full"
                required
              >
                <option value="">Yazıcı Seçin</option>
                {printers.map((printer) => (
                  <option key={printer.id} value={printer.id}>
                    {printer.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Yazıcı Tipi</label>
              <select
                ref={printerTypeAddRef}
                className="select select-bordered w-full"
                required
              >
                <option value="">Yazıcı Tipi Seçin</option>
                <option value="kitchen">Mutfak Yazıcısı</option>
                <option value="receipt">Fiş Yazıcısı</option>
              </select>
            </div>
          </div>

          <div className="modal-action">
            <form method="dialog">
              <button className="btn btn-ghost">İptal</button>
              <button
                type="button"
                onClick={btnAddMapping}
                className="btn bg-restro-green text-white hover:bg-restro-green-dark ml-2"
              >
                Ekle
              </button>
            </form>
          </div>
        </div>
      </dialog>

      {/* Update Mapping Modal */}
      <dialog id="modal-update-mapping" className="modal modal-bottom sm:modal-middle">
        <div className="modal-box">
          <h3 className="font-bold text-lg">Kat-Yazıcı Eşleştirmesini Düzenle</h3>
          
          <div className="my-4">
            <input type="hidden" ref={mappingIdUpdateRef} />
            
            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Kat</label>
              <select
                ref={floorIdUpdateRef}
                className="select select-bordered w-full"
                required
              >
                <option value="">Kat Seçin</option>
                {floors.map((floor) => (
                  <option key={floor.id} value={floor.id}>
                    {floor.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Kategori (Opsiyonel)</label>
              <select
                ref={categoryIdUpdateRef}
                className="select select-bordered w-full"
              >
                <option value="">Tüm Kategoriler</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Yazıcı</label>
              <select
                ref={printerIdUpdateRef}
                className="select select-bordered w-full"
                required
              >
                <option value="">Yazıcı Seçin</option>
                {printers.map((printer) => (
                  <option key={printer.id} value={printer.id}>
                    {printer.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-gray-500 text-sm mb-1">Yazıcı Tipi</label>
              <select
                ref={printerTypeUpdateRef}
                className="select select-bordered w-full"
                required
              >
                <option value="">Yazıcı Tipi Seçin</option>
                <option value="kitchen">Mutfak Yazıcısı</option>
                <option value="receipt">Fiş Yazıcısı</option>
              </select>
            </div>
          </div>

          <div className="modal-action">
            <form method="dialog">
              <button className="btn btn-ghost">İptal</button>
              <button
                type="button"
                onClick={btnUpdateMapping}
                className="btn bg-restro-green text-white hover:bg-restro-green-dark ml-2"
              >
                Güncelle
              </button>
            </form>
          </div>
        </div>
      </dialog>
    </Page>
  );
}
