import ApiClient from "../helpers/ApiClient";
import useS<PERSON> from "swr";

const fetcher = (url) => ApiClient.get(url).then((res) => res.data);

// SWR hook ile çevirileri getir
export function useTranslations() {
  const APIURL = `/translation`;
  const { data, error, isLoading, mutate } = useSWR(APIURL, fetcher);
  return {
    data,
    error,
    isLoading,
    APIURL,
    mutate
  };
}

// Tek bir dil için çeviri ekle veya güncelle
export async function saveTranslation(objectType, objectId, languageCode, translation) {
  try {
    const response = await ApiClient.post(`/translation/save`, {
      object_type: objectType,
      object_id: objectId,
      language_code: languageCode,
      translation
    });
    
    return response;
  } catch (error) {
    throw error;
  }
}

// Bir öğe için birden fazla dil çevirisi ekle veya güncelle
export async function saveTranslationsForObject(objectType, objectId, translations) {
  try {
    // Her bir dil için ayrı bir API çağrısı yapalım
    const results = [];
    
    for (const [languageCode, translation] of Object.entries(translations)) {
      if (translation && translation.trim() !== '') {
        try {
          const response = await saveTranslation(objectType, objectId, languageCode, translation);
          results.push({
            language_code: languageCode,
            success: response?.data?.success || false,
            message: response?.data?.message || '',
          });
        } catch (error) {
          results.push({
            language_code: languageCode,
            success: false,
            message: error.response?.data?.message || 'Hata oluştu',
          });
        }
      }
    }
    
    // Tüm işlemlerin sonucunu döndür
    const allSuccess = results.every(r => r.success);
    
    return {
      data: {
        success: allSuccess,
        message: allSuccess ? 'Tüm çeviriler kaydedildi' : 'Bazı çeviriler kaydedilemedi',
        results
      }
    };
  } catch (error) {
    throw error;
  }
}

// Toplu çeviri için yeni fonksiyon
export async function autoTranslate(items) {
  try {
    const response = await ApiClient.post(`/translation/auto-translate`, {
      items: items.map(item => ({
        objectType: item.objectType,
        objectId: item.objectId,
        originalText: item.originalText,
        targetLanguages: item.targetLanguages
      }))
    });

    return response.data;
  } catch (error) {
    console.error('Otomatik çeviri hatası:', error);
    throw error;
  }
}

// Çoklu öğe için toplu güncelleme
export async function saveBatchTranslations(items) {
  try {
    const allResults = [];
    
    for (const item of items) {
      if (item.objectType && item.objectId) {
        const result = await saveTranslationsForObject(
          item.objectType,
          item.objectId,
          item.translations
        );
        
        allResults.push({
          objectType: item.objectType,
          objectId: item.objectId,
          success: result?.data?.success || false,
          message: result?.data?.message || '',
          results: result?.data?.results || []
        });
      }
    }
    
    const successCount = allResults.filter(r => r.success).length;
    
    return {
      data: {
        success: successCount > 0,
        message: `${successCount}/${allResults.length} öğe güncellendi`,
        results: allResults
      }
    };
  } catch (error) {
    throw error;
  }
}

// ID ile çeviri sil
export async function deleteTranslation(id) {
  try {
    const response = await ApiClient.delete(`/translation/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

// Belirli bir nesne için tüm çevirileri sil
export async function deleteAllTranslationsForObject(objectType, objectId) {
  try {
    const response = await ApiClient.post(`/translation/delete-all-for-object`, {
      object_type: objectType,
      object_id: objectId
    });
    
    return response;
  } catch (error) {
    throw error;
  }
}